{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Run Router Tests",
            "type": "shell",
            "command": "script npm",
            "args": [
                "run",
                "test-router",
                "--",
                "--debug",
                "--inspect-brk"
            ],
            "isBackground": true,
            "problemMatcher": [
                {
                    "owner": "custom",
                    "background": {
                        "activeOnStart": true,
                        "beginsPattern": "________",
                        "endsPattern": "^.*For help see.*"
                    },
                    "pattern": {
                        "regexp": "________"
                    }
                }
            ],
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
        },
        {
            "label": "TypeScript Watch",
            "command": "script tsc",
            "args": [
                "--watch",
                "--pretty"
            ],
            "problemMatcher": [
                "$tsc-watch"
            ]
        },
        {
            "label": "tsc watch",
            "type": "shell",
            "command": "tsc",
            "isBackground": true,
            "args": [
                "--build",
                "--watch"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "reveal": "never",
                "echo": false,
                "focus": false,
                "panel": "dedicated"
            },
            "problemMatcher": {
                "base": "$tsc-watch",
                "applyTo": "allDocuments"
            }
        }
    ]
}
