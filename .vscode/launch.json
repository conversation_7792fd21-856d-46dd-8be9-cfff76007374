{"version": "0.2.0", "configurations": [{"type": "node", "request": "attach", "name": "NodeJS Local Attach", "port": 9229, "outFiles": ["dist/**/*.js"], "localRoot": "${workspaceRoot}", "remoteRoot": "${workspaceRoot}", "skipFiles": ["node_modules/*"], "protocol": "inspector", "sourceMaps": true}, {"type": "node", "request": "attach", "name": "NodeJS Remote Attach", "port": 9229, "outFiles": ["dist/**/*.js"], "localRoot": "${workspaceRoot}", "remoteRoot": "/src/studio", "skipFiles": ["node_modules/*"], "protocol": "inspector", "sourceMaps": true}, {"type": "node", "request": "attach", "name": "Debug Router Tests", "port": 9229, "outFiles": ["dist/**/*.js"], "preLaunchTask": "Run Router Tests", "localRoot": "${workspaceRoot}", "skipFiles": ["node_modules/*"], "smartStep": true, "protocol": "inspector", "sourceMaps": true}, {"name": "Debug Jest Tests", "type": "node", "request": "launch", "runtimeArgs": ["--inspect-brk", "${workspaceRoot}/node_modules/jest/bin/jest.js", "--runInBand", "--config=jest.config.ng.ts", "span.utils.spec.ts"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "port": 9229}, {"name": "Debug Frontend in Chrome", "type": "chrome", "request": "launch", "url": "https://remote-studio.bannerflow.com/brand/61fbcc09fd22d62491519309/creativeset/12741", "webRoot": "${workspaceFolder}", "sourceMaps": true, "runtimeArgs": ["--remote-debugging-port=9222"]}]}