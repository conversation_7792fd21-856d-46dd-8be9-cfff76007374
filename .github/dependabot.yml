version: 2
registries:
    npm-azure-artifacts:
        type: npm-registry
        url: https://pkgs.dev.azure.com/bannerflow/_packaging/bannerflow-npm/npm/registry/
        token: PAT:${{ secrets.AZURE_DEVOPS_PAT}}
updates:
    - package-ecosystem: 'npm'
      directory: '/'
      registries:
          - npm-azure-artifacts
      schedule:
          # Check for npm updates once a week on Sundays
          interval: 'weekly'
          day: 'sunday'
      open-pull-requests-limit: 10
      groups:
          production-dependencies:
              dependency-type: 'production'
          development-dependencies:
              dependency-type: 'development'

    - package-ecosystem: 'github-actions'
      directory: '/'
      schedule:
          interval: 'weekly'
          day: 'sunday'
      groups:
          all-actions:
              patterns:
                  - '*'
