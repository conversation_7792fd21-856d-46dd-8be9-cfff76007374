import { expect, Page, test as base } from '@playwright/test';
import { DesignViewPage } from './design-view-page';

type TestOptions = {
    page: Page;
};

type Test = ReturnType<typeof base.extend<TestOptions>>;
const test = (globalThis as typeof globalThis & { pwTest: Test }).pwTest as Test;

const CREATIVE_URL =
    'https://preview-studio.bannerflow.com/brand/642e9c43cf5ac3fbe4a15656/creativeset/326910/editor/12589123/7020026/6889340?version=531079';
const SAVE_ENDPOINT = 'https://api.bannerflow.com/studio/api/creative-sets/326910/designs-and-versions';
const rectangleElementSelector = '.kind-rectangle';

test('move the rectangle in DV and click save', async ({ page }) => {
    await page.goto(CREATIVE_URL);

    await page.waitForLoadState('domcontentloaded');

    const designViewPage = new DesignViewPage(page);

    await designViewPage.selectDesignElement(rectangleElementSelector);
    const rectangleInitialPosition = await designViewPage.getSelectedElementPosition();

    const newRectanglePosition = {
        x: rectangleInitialPosition.x > 0 ? 0 : 100,
        y: rectangleInitialPosition.y > 0 ? 0 : 100
    };
    await designViewPage.setSelectedElementPosition(newRectanglePosition.x, newRectanglePosition.y);
    await designViewPage.saveDesign(SAVE_ENDPOINT);

    await page.goto(CREATIVE_URL);
    await designViewPage.selectDesignElement(rectangleElementSelector);
    const rectanglePosition = await designViewPage.getSelectedElementPosition();

    expect(rectanglePosition.x).toEqual(newRectanglePosition.x);
    expect(rectanglePosition.y).toEqual(newRectanglePosition.y);
});
