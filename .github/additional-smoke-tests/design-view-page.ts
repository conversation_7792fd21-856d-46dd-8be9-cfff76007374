import { Locator, <PERSON> } from '@playwright/test';

export class DesignViewPage {
    private page: Page;
    private selectedEl: Locator | null = null;
    private xInput: Locator;
    private yInput: Locator;
    private saveButton: Locator;

    constructor(page: Page) {
        this.page = page;
        this.xInput = page.locator('#x-input').first().locator('input');
        this.yInput = page.locator('#y-input').first().locator('input');
        this.saveButton = page.locator('#save-design-button').first();
    }

    async selectDesignElement(elementSelector: string): Promise<Locator> {
        const element = this.page.locator(elementSelector).first();
        await element.waitFor({ state: 'visible' });
        await element.click();
        this.selectedEl = element;
        return element;
    }

    async getSelectedElementPosition(): Promise<{ x: number; y: number }> {
        if (!this.selectedEl) {
            throw new Error('No element selected');
        }

        await this.xInput.waitFor({ state: 'visible' });
        const x = parseFloat(await this.xInput.inputValue());
        const y = parseFloat(await this.yInput.inputValue());

        return { x, y };
    }

    async setSelectedElementPosition(x: number, y: number): Promise<void> {
        if (!this.selectedEl) {
            throw new Error('No element selected');
        }

        await this.xInput.waitFor({ state: 'visible' });
        await this.xInput.fill(x.toString());
        await this.yInput.fill(y.toString());
    }

    async saveDesign(saveEndpoint: string): Promise<void> {
        await this.saveButton.click();
        await this.page.waitForResponse(
            response =>
                response.url() === saveEndpoint &&
                response.status() === 200 &&
                response.request().method() === 'PUT'
        );
    }
}
