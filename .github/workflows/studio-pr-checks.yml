name: "Studio PR checks"

concurrency:
  group: 'Studio-PR_checks-${{ github.ref_name }}'
  cancel-in-progress: true

on:
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review
    branches:
      - main

env:
  NX_CLOUD_ACCESS_TOKEN: ${{secrets.NX_CLOUD_ACCESS_TOKEN}}
  NPM_TOKEN: ${{ github.token }}

jobs:
  pr-checks:
    permissions:
      actions: read
      contents: read
      id-token: write
      packages: read 
      checks: write
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node
        uses: ./.github/actions/node-setup
        with:
          bf-org-npm-token: ${{ secrets.BF_ORG_NPM_TOKEN }}

      # This line enables distribution
      # The "--stop-agents-after" is optional, but allows idle agents to shut down once the "e2e-ci" targets have been requested
      # - run: pnpm exec nx-cloud start-ci-run --distribute-on="3 linux-medium-js" --stop-agents-after="e2e-ci"
      # Prepend any command with "nx-cloud record --" to record its logs to Nx Cloud
      # - run: pnpm exec nx-cloud record -- echo Hello World
      # Nx Affected runs only tasks affected by the changes in this PR/commit. Learn more: https://nx.dev/ci/features/affected
      # --no-agents disables remote task execution
      # Using custom distribution custom-studio-linux-medium-js (.nx/workflows) due to dynamically created .npmrc file, else installation fails with 401 on the agents
      # - run: npx nx-cloud start-ci-run --distribute-on="3 custom-studio-linux-medium-js" --with-env-vars="NPM_TOKEN"

      - name: Nx prepare affected
        uses: ./.github/actions/nx-prepare-affected

      - name: Run Format Check
        run: pnpm exec nx-cloud record -- nx format:check

      - run: pnpm exec nx affected -t lint test --parallel=3

      - name: Run AC/AD build
        run: |
          pnpm exec nx-cloud record -- nx run-many -t build -p ad creative

      - name: SonarQube Scan
        uses: SonarSource/sonarqube-scan-action@v5.2.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
