name: Creative generation E2E
on:
  workflow_dispatch:  # manual trigger
  schedule:
    - cron: 0 13 * * *  # daily trigger
  workflow_run:
    workflows: [ "ACG deploy" ]
    types:
      - completed
    branches: [ main ]

env:
  NX_CLOUD_ACCESS_TOKEN: ${{secrets.NX_CLOUD_ACCESS_TOKEN}}

jobs:
  creative-generation-e2e:
    runs-on: ubuntu-latest
    environment: sandbox
    steps:
      - name: "git: checkout"
        uses: actions/checkout@v4

      - name: "Azure: set environment variables"
        id: set_vars
        uses: nordicfactory/shared-github-actions/azure/defaults@main
        with:
          environment-region: production

      - name: Azure login
        uses: nordicfactory/shared-github-actions/azure/login@main
        with:
          client-id: ${{ secrets.AZURE_DEPLOY_CLIENT_ID }}
          client-secret: ${{ secrets.AZURE_DEPLOY_CLIENT_SECRET }}
          tenant-id: ${{ secrets.AZURE_DEPLOY_TENANT_ID }}
          subscription-id: ${{ steps.set_vars.outputs.subscription-id }}
      
      - name: "node: setup"
        uses: ./.github/actions/node-setup
        with:
          bf-org-npm-token: ${{ secrets.BF_ORG_NPM_TOKEN }}

      - uses: ./.github/actions/playwright-setup

      - name: Fetch prod secrets from Key Vault
        id: secrets
        uses: nordicfactory/shared-github-actions/azure/keyvault-secrets@main
        with:
          keyvault-name: "bf-shared-kv"
          secret-names: "AccountCredentials-BfProdSmokeTest--Password,AccountCredentials-BfSandboxSmokeTest--Password"

      - name: Run E2E tests
        env:
          TEST_PROD_USER_PASSWORD: ${{ fromJson(steps.secrets.outputs.secrets-json).AccountCredentials-BfProdSmokeTest--Password }}
          TEST_SANDBOX_USER_PASSWORD: ${{ fromJson(steps.secrets.outputs.secrets-json).AccountCredentials-BfSandboxSmokeTest--Password }}
        run: pnpm exec nx e2e creative-generation-e2e --no-cloud

      - name: Send failed E2E tests notification to Slack
        if: failure()
        uses: nordicfactory/shared-github-actions/slack/send-message@main
        with:
            channel-id: 'C06DWU7SJAX'
            slack-bot-token: ${{secrets.SLACK_BOT_TOKEN}}
            message: |
              🚨📢Creative generation E2E tests failed🚨📢
              Workflow Run: <https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}|Workflow Details>
