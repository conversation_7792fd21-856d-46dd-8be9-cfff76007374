name: "Studio deploy PR"

concurrency:
  group: 'Studio-Deploy_PR-${{ github.ref_name }}'
  cancel-in-progress: true

on:
  workflow_dispatch:
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review
    branches:
      - main

jobs:
  build-and-deploy:
    uses: ./.github/workflows/studio-client.yml
    name: Studio - Build and Deploy
    secrets: inherit
    permissions:
      actions: read
      contents: read
      packages: read
    with:
      branch: "${{ github.head_ref }}"
      environment: "sandbox"
      cf-project-name: "studio-sandbox"

