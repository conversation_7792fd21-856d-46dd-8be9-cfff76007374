name: "Studio - Build and Deploy"

on: 
  workflow_call:
    inputs:
      branch:
        required: true
        type: string
        default: ${{ github.head_ref }}
      environment:
        required: true
        type: string
      cf-project-name:
        required: true
        type: string

permissions:
  actions: read
  contents: read
  packages: read

env:
  NX_CLOUD_ACCESS_TOKEN: ${{secrets.NX_CLOUD_ACCESS_TOKEN}}

jobs:
  build-deploy:
    runs-on: ubuntu-latest
    environment: 
      name: ${{ inputs.environment }}
      url: ${{ steps.deploy.outputs.deployment-alias-url }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-tags: 'false'
          fetch-depth: 0

      - name: Setup Node
        uses: ./.github/actions/node-setup
        with:
          bf-org-npm-token: ${{ secrets.BF_ORG_NPM_TOKEN }}

      - name: "nx: check affected"
        id: nx_affected
        uses: ./.github/actions/nx-affected
        with:
          projects: 'studio'

      - name: Azure login
        if: ${{steps.nx_affected.outputs.is_affected == 'true'}}
        uses: nordicfactory/shared-github-actions/azure/login@main
        with:
          client-id: ${{ secrets.AZURE_DEPLOY_CLIENT_ID }}
          client-secret: ${{ secrets.AZURE_DEPLOY_CLIENT_SECRET }}
          tenant-id: ${{ secrets.AZURE_DEPLOY_TENANT_ID }}
          subscription-id: ${{ vars.STUDIO_AZ_SUBSCRIPTION_ID }}

      - name: Get secrets from key vault
        if: ${{ vars.STUDIO_KEYVAULT_NAME && steps.nx_affected.outputs.is_affected == 'true'}}
        id: set_secrets
        uses: nordicfactory/shared-github-actions/secrets/set@main
        with:
          keyvault-name: ${{ vars.STUDIO_KEYVAULT_NAME }}

      - name: Generate build info
        if: ${{steps.nx_affected.outputs.is_affected == 'true'}}
        run: |
          BRANCH="${{ inputs.branch }}"
          pnpm exec nx g @studio/tools:build-info studio --branch=$BRANCH

      - name: Build
        if: ${{steps.nx_affected.outputs.is_affected == 'true'}}
        run: |
          pnpm exec nx run studio:build --configuration=${{ inputs.environment }}

      - name: New Relic Application Deployment Marker
        if: ${{ env.secret-newrelic-api-key && steps.nx_affected.outputs.is_affected == 'true' && inputs.branch == 'main'}}
        uses: nordicfactory/shared-github-actions/new-relic/create-deployment@main
        with:
          NEW_RELIC_API_KEY: ${{ env.secret-newrelic-api-key }}
          application-name: ${{ vars.STUDIO_NEW_RELIC_APP_NAME }}
          environment: ${{ vars.STUDIO_NEW_RELIC_ENV }}
          version: "${{ github.sha }}"

      - name: New Relic Upload sourcemaps
        if: ${{ env.secret-newrelic-api-key && steps.nx_affected.outputs.is_affected == 'true' && inputs.branch == 'main'}}
        uses: nordicfactory/shared-github-actions/new-relic/upload-sourcemaps@main
        with:
          NEW_RELIC_API_KEY: ${{ env.secret-newrelic-api-key }}
          application-id: ${{ vars.STUDIO_NEW_RELIC_APP_ID }}
          origin: ${{ vars.STUDIO_CLIENT_ORIGIN }}

      - name: Cleanup sourcemaps from production
        if: ${{steps.nx_affected.outputs.is_affected == 'true' && inputs.environment == 'production'}}
        run: |
          find dist/apps/studio -name '*.map' -type f -delete

      - name: Deploy to Cloudflare
        if: ${{steps.nx_affected.outputs.is_affected == 'true'}}
        id: deploy
        uses: nordicfactory/shared-github-actions/deploy/cloudflare/pages@main
        with:
          api-token: ${{ secrets.CLOUDFLARE_PAGES_API_TOKEN }}
          account-id: ${{ secrets.CLOUDFLARE_PAGES_ACCOUNT_ID }}
          project-name: ${{ inputs.cf-project-name }}
          directory: dist/apps/studio
          branch: ${{ inputs.branch }}

      - name: Print URLs
        if: ${{steps.nx_affected.outputs.is_affected == 'true' && inputs.environment == 'sandbox' && inputs.branch != 'main'}}
        shell: bash
        run: |
          url="${{ steps.deploy.outputs.deployment-url }}"
          url_alias="${{ steps.deploy.outputs.deployment-alias-url }}"
          echo "### Deployed to: $url \n Aliased by: $url_alias"
          echo "### Deployed to: $url \n Aliased by: $url_alias" >> $GITHUB_STEP_SUMMARY
