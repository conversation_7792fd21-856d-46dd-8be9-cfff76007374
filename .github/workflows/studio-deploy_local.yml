name: "Studio deploy local Docker Image"

concurrency:
  group: 'Studio-Deploy_local-${{ github.ref_name }}'
  cancel-in-progress: true

on:
  push:
    branches:
      - main
  pull_request:
    types:
      - labeled
      - synchronize
    branches:
      - main

jobs:
  build-docker-local:
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      packages: read
      pull-requests: read
    environment: build-docker-local
    if: |
      github.ref_name == 'main' ||
      contains(github.event.pull_request.labels.*.name, 'build:local')
    steps:
      - name: Azure login
        uses: nordicfactory/shared-github-actions/azure/login@main
        with:
          client-id: ${{ secrets.AZURE_DEPLOY_CLIENT_ID }}
          client-secret: ${{ secrets.AZURE_DEPLOY_CLIENT_SECRET }}
          tenant-id: ${{ secrets.AZURE_DEPLOY_TENANT_ID }}
          subscription-id: ${{ vars.SUBSCRIPTION_ID }}

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node
        uses: ./.github/actions/node-setup
        with:
          bf-org-npm-token: ${{ secrets.BF_ORG_NPM_TOKEN }}

      - name: Nx affected
        id: nx_affected
        uses: ./.github/actions/nx-affected
        with:
          projects: 'studio'

      - name: Build studio
        if: steps.nx_affected.outputs.is_affected == 'true'
        run: |
          pnpm exec nx run studio:build:development

      - name: Build & Push Studio local Docker
        if: steps.nx_affected.outputs.is_affected == 'true'
        id: docker_build
        uses: nordicfactory/shared-github-actions/build/docker@main
        with:
          base-directory: ''
          container-registry: 'bannerflow'
          container-name: 'studio/studio-client'
          azure-devops-token: ${{ secrets.AZURE_DEVOPS_ARTIFACTS_PAT }}
          docker-file: 'apps/studio/Dockerfile'

