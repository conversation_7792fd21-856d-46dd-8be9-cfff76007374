name: "Studio E2E tests"

concurrency:
  group: 'Studio-E2E-${{ github.ref_name }}'
  cancel-in-progress: true

on:
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review
    branches:
      - main

env:
  NX_CLOUD_ACCESS_TOKEN: ${{secrets.NX_CLOUD_ACCESS_TOKEN}}

jobs:
  pr-e2e:
    permissions:
      actions: read
      contents: read
      id-token: write
      packages: read
      checks: write
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    strategy:
      fail-fast: true
      matrix:
        containers: [ 1, 2, 3, 4 ]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-tags: 'false'
          fetch-depth: 0

      - name: Setup Node
        uses: ./.github/actions/node-setup
        with:
          bf-org-npm-token: ${{ secrets.BF_ORG_NPM_TOKEN }}

      - name: Nx affected
        id: nx_affected
        uses: ./.github/actions/nx-affected
        with:
          projects: 'studio,studio-e2e'

      - name: Set up Chrome
        if: ${{steps.nx_affected.outputs.is_affected == 'true'}}
        uses: browser-actions/setup-chrome@v1
        with:
          chrome-version: stable

      - name: Set browser path
        if: ${{steps.nx_affected.outputs.is_affected == 'true'}}
        run: echo "BROWSER_PATH=$(which chrome)" >> $GITHUB_ENV

      - name: Add nx globally
        run: pnpm add --global nx@latest

      - name: Restore Cypress cache
        id: cypress-cache
        uses: actions/cache/restore@v4
        with:
          path: ~/.cache/Cypress
          key:  ${{ runner.os }}-cypress

      - name: Install Cypress binary
        if: steps.cypress-cache.outputs.cache-hit != 'true'
        run: pnpm exec cypress install

      - name: Save Cypress cache
        if: steps.cypress-cache.outputs.cache-hit != 'true'
        uses: actions/cache/save@v4
        with:
          path: ~/.cache/Cypress
          key: ${{ runner.os }}-cypress-${{ hashFiles('pnpm-lock.yaml') }}

      - name: Cypress run
        if: ${{steps.nx_affected.outputs.is_affected == 'true'}}
        uses: cypress-io/github-action@v6
        env:
          ELECTRON_EXTRA_LAUNCH_ARGS: "--remote-debugging-port=9222"
          CYPRESS_RECORD_KEY: ${{ secrets.CYPRESS_RECORD_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          install: false
          record: true
          parallel: true
          browser: ${{ env.BROWSER_PATH }}
          start: pnpm exec nx run studio:serve-static --port=4200 --no-agents
          project: apps/studio-e2e
          wait-on: http://localhost:4200
          wait-on-timeout: 240
          config: baseUrl=http://localhost:4200
          auto-cancel-after-failures: 1

      - name: Upload Cypress artifacts
        if: ${{steps.nx_affected.outputs.is_affected == 'true' && github.head_ref == 'update-bannerflow-ui' && !startsWith(github.head_ref, 'dependabot/')}}
        uses: actions/upload-artifact@v4
        with:
          name: test-results-${{ matrix.containers }}
          path: apps/studio-e2e/cypress/downloads
          retention-days: 30

  pr-visual-tests:
    needs: pr-e2e
    runs-on: ubuntu-latest
    if: github.head_ref == 'update-bannerflow-ui' && !startsWith(github.head_ref, 'dependabot/')
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node
        uses: ./.github/actions/node-setup
        with:
          bf-org-npm-token: ${{ secrets.BF_ORG_NPM_TOKEN }}

      - name: Download Cypress test results
        uses: actions/download-artifact@v4
        with:
          pattern: test-results-*
          path: apps/studio-e2e/cypress/downloads
          merge-multiple: true

      - name: Run Chromatic
        uses: chromaui/action@latest
        with:
          cypress: true
          projectToken: ${{ secrets.CHROMATIC_PROJECT_TOKEN }}
          autoAcceptChanges: "main"
          onlyChanged: false
          exitOnceUploaded: true
        env:
          CHROMATIC_ARCHIVE_LOCATION: apps/studio-e2e/cypress/downloads