# This workflow will trigger scheduled ad scans via the AdScannerService e2e project, or through changes in the app.
name: AdScanner E2E
on:
    workflow_dispatch:
    schedule:
        - cron: 0 13 * * *

jobs:
    e2e-adscanner:
        runs-on: ubuntu-latest
        environment: sandbox
        env:
            AZURE_STORAGE_ACCOUNT_ORIGIN: ${{ vars.AZURE_STORAGE_ACCOUNT_ORIGIN }}
            AZURE_STORAGE_ACCOUNT_NAME: ${{ vars.AZURE_STORAGE_ACCOUNT_NAME }}
            AZURE_STORAGE_ACCOUNT_ACCESS_KEY: ${{ secrets.AZURE_STORAGE_ACCOUNT_ACCESS_KEY }}
            MONGODB_CONNECTION_STRING: ${{secrets.AS_MONGODB_CONNECTION_STRING}}
            SLACK_TOKEN: ${{secrets.SLACK_BOT_TOKEN}}
            SLACK_CHANNEL_ID: 'C08LJ984AR4'
            NX_CLOUD_ACCESS_TOKEN: ${{secrets.NX_CLOUD_ACCESS_TOKEN}}

        steps:
            - name: Checkout repository
              uses: actions/checkout@v4

            - name: Setup Node 
              uses: ./.github/actions/node-setup
              with:
                bf-org-npm-token: ${{ secrets.BF_ORG_NPM_TOKEN }}

#            - name: Serve AdScanner
#              run: |
#                  # Run AdScanner in the background
#                  MONGODB_CONNECTION_STRING=${{ secrets.AS_MONGODB_CONNECTION_STRING }} \
#                  pnpm exec nx run adscanner:serve:production &

            - name: URL Health Check
              uses: Jtalk/url-health-check-action@v4
              with:
                  url: https://sandbox-api.bannerflow.com/ass/health/ready
                  max-attempts: 5
                  retry-delay: 5s

            - name: Run e2e tests
              run: pnpm exec nx run adscanner-e2e:e2e --no-cloud
              env:
                  HOST: https://sandbox-api.bannerflow.com/ass

            - name: Verify Ad Metrics
              uses: ./.github/actions/verify-ad-metrics
              with:
                mongodb-connection-string: ${{secrets.AS_MONGODB_CONNECTION_STRING}}

            - name: "Slack: send failure message"
              uses: nordicfactory/shared-github-actions/slack/send-message@main
              if: failure()
              with:
                slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
                channel-id: ${{ env.SLACK_CHANNEL_ID }}
                message: |
                  :seal_angry: AdScanner E2E failed
                  <https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}|Workflow Run>
