name: Creative Preview E2E
on:
  workflow_dispatch: # Manual trigger
  schedule:
    - cron: 0 13 * * *

env:
  NX_CLOUD_ACCESS_TOKEN: ${{secrets.NX_CLOUD_ACCESS_TOKEN}}

jobs:
  creative-preview-e2e:
    permissions:
      actions: read
      contents: read
      packages: read
    runs-on: ubuntu-latest
    steps:
      - name: "git: checkout"
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: "node: setup"
        uses: ./.github/actions/node-setup
        with:
          bf-org-npm-token: ${{ secrets.BF_ORG_NPM_TOKEN }}

      - name: "nx: affected"
        id: nx_affected
        uses: ./.github/actions/nx-affected
        with:
          projects: 'ad,creative'

      - name: "playwright: setup"
        uses: ./.github/actions/playwright-setup
        id: playwright-setup
        with:
          browsers: 'chromium'

      - name: "nx: creative-preview-e2e percy tests"
        if: steps.nx_affected.outputs.is_affected == 'true'
        run: pnpm exec nx run creative-preview-e2e:percy
        env:
          PERCY_TOKEN: ${{secrets.PERCY_TOKEN}}

      - name: "nx: creative-preview-e2e rest"
        if: steps.nx_affected.outputs.is_affected == 'true'
        run: pnpm exec nx run creative-preview-e2e:all

      - name: "slack: report failure"
        if: steps.nx_affected.outputs.is_affected == 'true' && failure()
        uses: nordicfactory/shared-github-actions/slack/send-message@main
        with:
          channel-id: 'C05R4EVNVGS'
          slack-bot-token: ${{secrets.SLACK_BOT_TOKEN}}
          message: |
            🚨📢Creative Preview E2E tests failed🚨📢
            Commit: <https://github.com/${{ github.repository }}/commit/${{ github.event.pull_request.head.sha }}|${{ github.event.pull_request.head.sha }}>
            <https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}|Workflow Run> Author: <https://github.com/${{ github.actor }}|${{ github.actor }}>
