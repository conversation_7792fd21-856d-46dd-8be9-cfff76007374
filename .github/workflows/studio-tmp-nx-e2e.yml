name: "Studio NX E2E"

on:
  workflow_dispatch:
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
      - ready_for_review
    branches:
      - main

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number || github.ref }}
  cancel-in-progress: true

# revisit which permissions are really needed
permissions: read-all

env:
  NX_CLOUD_ACCESS_TOKEN: ${{secrets.NX_CLOUD_ACCESS_TOKEN}}

# PER DOCS - https://nx.dev/ci/recipes/set-up/monorepo-ci-github-actions
jobs:
  main:
    runs-on: ubuntu-latest
    steps:
      - name: "git: checkout"
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      # The "--stop-agents-after" is optional, but allows idle agents to shut down once the "e2e-ci" targets have been requested
      - run: pnpm exec nx-cloud start-ci-run --distribute-on="4 custom-studio-linux-medium-js" --stop-agents-after="e2e-ci" --with-env-vars="NPM_TOKEN"
        env:
          NPM_TOKEN: ${{ github.token }}

      - name: "node: setup"
        uses: ./.github/actions/node-setup
        with:
          bf-org-npm-token: ${{ secrets.BF_ORG_NPM_TOKEN }}

      - name: "nx: prepare affected"
        uses: ./.github/actions/nx-prepare-affected

      # Cypress does not like to run multiple atomic tests on the same agent --> parallel=1
      - run: pnpm exec nx affected -t e2e-ci --parallel=1 --browser=chrome
