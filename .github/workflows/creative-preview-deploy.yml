name: "CPS deploy"

concurrency:
  group: 'CPS-Deploy-${{ github.ref_name }}'
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: 'Select deployment environment'
        required: false
        default: 'PR'
        options:
          - all
          - sandbox
          - production
          - PR

env:
  APP_RESOURCE_GROUP_PREFIX: "bf-cps"
  AZURE_DEV_SUBSCRIPTION_ID: "b736d7b3-0d45-4c37-9174-e671b53a6d83"
  CONTAINER_NAME: "studio/cps"
  FEATURE: "creative-generation"
  NAME: "cps"
  NAMESPACE: "creative-generation"
  NX_CLOUD_ACCESS_TOKEN: ${{secrets.NX_CLOUD_ACCESS_TOKEN}}

  ENV_OVERRIDES: |
    {
        'sandbox': {
            'storage-account': 'bfstudiosandbox',
            'storage-account-resource-group': 'bf-studiostorage-sandbox-rg',
            'servicebus': 'bf-common-sandbox-asb',
            'servicebus-resource-group': 'bf-common-sandbox-rg',
            'warmup-url': 'https://sandbox-api.bannerflow.com',
            'warmup-path': '/preview/'
        },
        'production': {
            'storage-account': 'bfstudio',
            'storage-account-resource-group': 'bf-studiostorage-westeurope-rg',
            'servicebus': 'bf-common-asb',
            'servicebus-resource-group': 'bf-common-rg',
            'warmup-url': 'https://api.bannerflow.com',
            'warmup-path': '/preview/'
        },
        'production_northeurope': {
        }
    }
  # these are automagically setup by our shared actions, defined to please the linter
  BUILD_DESCRIPTION: ""
  DEPLOYMENT_DESCRIPTION: ""
  COMMIT_MESSAGE: ""
  secret-newrelic-api-key: ""
  storage-account: ""
  storage-account-resource-group: ""
  servicebus: ""
  servicebus-resource-group: ""
  warmup-url: ""
  warmup-path: ""

jobs:
  check-deployment:
    permissions:
      actions: read
      contents: read
      packages: read
      pull-requests: read
    runs-on: ubuntu-latest
    outputs:
      branch: ${{ steps.check_branch.outputs.branch }}
      deploy_matrix: ${{ steps.check_deploy.outputs.matrix }}
      create_pr_env: ${{ steps.check_deploy.outputs.create_pr_env }}
      is_pr: ${{ steps.check_pr.outputs.is_pr }}
      is_draft: ${{ steps.check_pr.outputs.is_draft }}
    steps:
      - name: "Git: checkout"
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: "Git: check branch"
        id: check_branch
        run: |
          echo "branch=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}" >> $GITHUB_OUTPUT
          echo "is_main=$(echo ${{ github.ref_name }} | grep -q 'main' && echo 'true' || echo 'false')" >> $GITHUB_OUTPUT

      - name: "Git: check for open PRs"
        id: check_pr
        uses: nordicfactory/shared-github-actions/github/check-pr@main

      - name: "Node: setup"
        uses: ./.github/actions/node-setup
        with:
          bf-org-npm-token: ${{ secrets.BF_ORG_NPM_TOKEN }}

      - name: "NX: check affected"
        id: nx_affected
        uses: ./.github/actions/nx-affected
        with:
          projects: 'creative-preview,ad,creative'

      - name: Check deployment
        if: ${{steps.nx_affected.outputs.is_affected == 'true'}}
        id: check_deploy
        run: |
          selected_env="${{ github.event.inputs.environment }}"
          
          # manual environment selection
          if [ "$selected_env" == "all" ]; then
            MATRIX='["sandbox_westeurope", "production_westeurope", "production_northeurope"]'
            echo "matrix=$MATRIX" >> $GITHUB_OUTPUT
            exit 0
          elif [ "$selected_env" == "PR" ]; then
            MATRIX='["sandbox_westeurope"]'
            echo "matrix=$MATRIX" >> $GITHUB_OUTPUT
            echo "create_pr_env=true" >> $GITHUB_OUTPUT
            exit 0
          elif [ "$selected_env" == "sandbox" ]; then
            MATRIX='["sandbox_westeurope"]'
            echo "matrix=$MATRIX" >> $GITHUB_OUTPUT
            exit 0
          elif [ "$selected_env" == "production" ]; then
            MATRIX='["production_westeurope", "production_northeurope"]'
            echo "matrix=$MATRIX" >> $GITHUB_OUTPUT
            exit 0
          fi
          
          # automatic environment selection
          if [ "${{ steps.check_branch.outputs.is_main }}" == "true" ]; then
            MATRIX='["sandbox_westeurope", "production_westeurope", "production_northeurope"]'
            echo "matrix=$MATRIX" >> $GITHUB_OUTPUT
          else
            MATRIX='["sandbox_westeurope"]'
            echo "create_pr_env=false" >> $GITHUB_OUTPUT
            echo "matrix=$MATRIX" >> $GITHUB_OUTPUT
          fi

  build:
    runs-on: ubuntu-latest
    needs: check-deployment
    if: needs.check-deployment.outputs.deploy_matrix
    env:
      BRANCH: ${{ needs.check-deployment.outputs.branch }}
    steps:
      - name: "Azure: login"
        uses: nordicfactory/shared-github-actions/azure/login@main
        with:
          client-id: ${{ secrets.AZURE_DEPLOY_CLIENT_ID }}
          client-secret: ${{ secrets.AZURE_DEPLOY_CLIENT_SECRET }}
          tenant-id: ${{ secrets.AZURE_DEPLOY_TENANT_ID }}
          subscription-id: ${{ vars.SUBSCRIPTION_ID }}

      - name: "Git: checkout"
        uses: actions/checkout@v4

      - name: "Node: setup"
        uses: ./.github/actions/node-setup
        with:
          bf-org-npm-token: ${{ secrets.BF_ORG_NPM_TOKEN }}

      - name: "Docker: prepare CPS"
        run: |
          pnpm exec nx run creative-preview:docker-prepare --branch=$BRANCH

      - name: "Docker: build & push"
        id: docker_build
        uses: nordicfactory/shared-github-actions/build/docker@main
        with:
          base-directory: ''
          container-registry: 'bannerflow'
          container-name: ${{ env.CONTAINER_NAME }}
          azure-devops-token: ${{ secrets.AZURE_DEVOPS_ARTIFACTS_PAT }}
          docker-file: 'apps/creative-preview/Dockerfile'

  prepare-deploy:
    needs: check-deployment
    if: needs.check-deployment.outputs.deploy_matrix
    runs-on: ubuntu-latest
    timeout-minutes: 25
    outputs:
      message-state: ${{ steps.send-deploy-started.outputs.state }}
    steps:
      - uses: actions/checkout@v4

      - name: "Slack: get release info"
        id: release-info
        uses: nordicfactory/shared-github-actions/slack/create-build-info@main
        with:
          service-name: ${{ env.NAME }}

      - name: "Slack: send initial message"
        uses: nordicfactory/shared-github-actions/deploy-message/prepare-deploy-message@main
        id: send-deploy-started
        with:
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          message: "${{ env.DEPLOYMENT_DESCRIPTION }}: In Progress:loading-dots:"
          sandbox-channel: "releases-sandbox"
          production-channel: "releases"

  deploy:
    needs: [ check-deployment, prepare-deploy, build ]
    name: "deploy ${{ matrix.environment-region }}"
    permissions:
      actions: read
      contents: read
      id-token: write
      checks: write
      packages: read
      pull-requests: read
    runs-on: ubuntu-latest
    timeout-minutes: 25
    concurrency:
      group: cps-deploy-${{ matrix.environment-region }}-${{ github.ref }}
      cancel-in-progress: false
    strategy:
      matrix:
        environment-region: ${{ fromJson(needs.check-deployment.outputs.deploy_matrix) }}
      fail-fast: false
    environment:
      name: ${{ matrix.environment-region }}
      url: ${{ steps.get_release_url.outputs.release_url }}
    steps:
      - name: "Git: checkout"
        uses: actions/checkout@v4

      - name: "Git: setup PR environment"
        id: branch_name
        uses: nordicfactory/shared-github-actions/github/create-name@main
        with:
          name: ${{ env.NAME }}
          create-branch-specific-name: ${{ needs.check-deployment.outputs.create_pr_env == 'true' }}

      - name: "Azure: set environment variables"
        id: set_vars
        uses: nordicfactory/shared-github-actions/azure/defaults@main
        with:
          environment-region: "${{ matrix.environment-region }}"
          overrides: "${{ env.ENV_OVERRIDES }}"
          service-name: "${{ env.NAME }}"
          name-prefix: ${{ steps.branch_name.outputs.prefix }}

      - name: "GitHub: set release url"
        id: get_release_url
        run: echo release_url=${{env.warmup-url}}${{ steps.branch_name.outputs.ingress-path }}${{env.warmup-path}} >> $GITHUB_OUTPUT

      - name: "Azure: login"
        uses: nordicfactory/shared-github-actions/azure/login@main
        with:
          client-id: ${{ secrets.AZURE_DEPLOY_CLIENT_ID }}
          client-secret: ${{ secrets.AZURE_DEPLOY_CLIENT_SECRET }}
          tenant-id: ${{ secrets.AZURE_DEPLOY_TENANT_ID }}
          subscription-id: ${{ steps.set_vars.outputs.subscription-id }}

      - name: "Azure: get secrets from key vault"
        id: set_secrets
        uses: nordicfactory/shared-github-actions/secrets/set@main
        with:
          keyvault-name: ${{ steps.set_vars.outputs.primary-keyvault-name }}
          application-name: ${{ env.NAME }}
          region: ${{ steps.set_vars.outputs.region }}

      - name: "Slack: get release info"
        id: release_info
        uses: nordicfactory/shared-github-actions/slack/create-build-info@main
        with:
          service-name: ${{ env.NAME }}

      - name: "Slack: parse pre-deploy message"
        uses: nordicfactory/shared-github-actions/deploy-message/parse-message@main
        id: parse-message-state
        with:
          message-state: ${{ needs.prepare-deploy.outputs.message-state }}
          environment-region: ${{ matrix.environment-region }}

      - name: "Slack: send starting message"
        uses: nordicfactory/shared-github-actions/slack/send-message@main
        id: send-deploy-started
        with:
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          channel-id: ${{ steps.parse-message-state.outputs.channel-id }}
          thread-ts: ${{ steps.parse-message-state.outputs.ts }}
          message: "${{ matrix.environment-region }}: Starting:loading-dots:"

      - name: "Azure: create resource group name"
        uses: nordicfactory/shared-github-actions/azure/resource-group-name@main
        id: resource_group_name
        with:
          environment: ${{ steps.set_vars.outputs.environment }}
          region: ${{ steps.set_vars.outputs.region }}
          resource-group-prefix: ${{ env.APP_RESOURCE_GROUP_PREFIX }}

      - name: "Azure: deploy infrastructure"
        uses: nordicfactory/shared-github-actions/deploy/bicep@main
        id: infrastructure
        with:
          deployment-name: ${{ env.NAME }}-${{ steps.set_vars.outputs.aks-cluster-name }}-infra
          subscription-id: ${{ steps.set_vars.outputs.subscription-id }}
          resource-group: ${{ steps.resource_group_name.outputs.name }}
          region: ${{ steps.set_vars.outputs.region }}
          environment: ${{ steps.set_vars.outputs.environment }}
          directory: apps/creative-preview/infrastructure
          parameters: managedIdentityName=${{steps.set_vars.outputs.managed-identity-name}} managedFederatedName=${{steps.set_vars.outputs.managed-federated-name}} serviceName=${{ steps.branch_name.outputs.name }} namespace=${{ env.NAMESPACE }} aksClusterName=${{ steps.set_vars.outputs.aks-cluster-name }} aksClusterResourceGroup=${{ steps.set_vars.outputs.aks-resource-group }}
          tags: "feature=${{ env.FEATURE }}"
          only-run-on-change: false

      - name: "Azure: lookup managed identity id"
        uses: nordicfactory/shared-github-actions/azure/identity-lookup@main
        id: lookup-identity
        with:
          identity-name: ${{steps.set_vars.outputs.managed-identity-name}}

      - name: "Azure: assign role access to key vault"
        uses: nordicfactory/shared-github-actions/azure/role-assign/keyvault/secrets@main
        with:
          keyvault-name: ${{ steps.set_vars.outputs.primary-keyvault-name }}
          principal-id: ${{ steps.lookup-identity.outputs.principal-id }}

      - name: "Azure: assign role access to storage account"
        uses: nordicfactory/shared-github-actions/azure/role-assign/storageaccount@main
        with:
          storage-account-name: ${{ env.storage-account }}
          storage-account-resource-group: ${{ env.storage-account-resource-group }}
          storage-account-role: "Storage Blob Data Contributor"
          principal-id: ${{ steps.lookup-identity.outputs.principal-id }}

      - name: "Azure: assign role access to service bus"
        uses: nordicfactory/shared-github-actions/azure/role-assign/servicebus@main
        with:
          service-bus-name: ${{ env.servicebus }}
          service-bus-resource-group: ${{ env.servicebus-resource-group }}
          principal-id: ${{ steps.lookup-identity.outputs.principal-id }}

      - name: "Slack: send deploying message"
        uses: nordicfactory/shared-github-actions/slack/send-message@main
        with:
          ts: ${{ steps.send-deploy-started.outputs.ts }}
          channel-id: ${{ steps.parse-message-state.outputs.channel-id }}
          slack-bot-token: ${{secrets.SLACK_BOT_TOKEN}}
          message: "${{ matrix.environment-region }}: Deploying:loading-dots:"

      - name: "AKS: set secrets file"
        uses: nordicfactory/shared-github-actions/aks/create-secrets@main
        with:
          environment: ${{ matrix.environment-region }}
          directory: apps/creative-preview/infrastructure/aks/deploy

      - name: "AKS: deploy"
        uses: nordicfactory/shared-github-actions/deploy/aks@main
        with:
          chart: microservice-chart
          name: ${{ steps.branch_name.outputs.name }}
          namespace: ${{ env.NAMESPACE }}
          application-settings-file: apps/creative-preview/infrastructure/aks/deploy/settings-${{ matrix.environment-region }}.yaml
          application-secrets-file: apps/creative-preview/infrastructure/aks/deploy/secrets-${{ matrix.environment-region }}.yaml
          image: "bannerflow.azurecr.io/${{ env.CONTAINER_NAME }}"
          resource-group: ${{ steps.set_vars.outputs.aks-resource-group }}
          cluster-name: ${{ steps.set_vars.outputs.aks-cluster-name }}
          environment: ${{ steps.set_vars.outputs.environment }}
          environment-region: ${{ matrix.environment-region }}
          managed-identity: ${{ steps.lookup-identity.outputs.client-id }}
          timeout: 600s
          extra-parameters: "env.AZURE_REGION=${{ steps.set_vars.outputs.region }} ingressPrefixPath=${{ steps.branch_name.outputs.ingress-path }} env.IS_PR_ENVIRONMENT=${{ steps.branch_name.outputs.is-pr-environment }} env.PR_ENVIRONMENT_NAME=${{ steps.branch_name.outputs.prefix }} prefixName=${{ steps.branch_name.outputs.prefix }}"

      - name: "NewRelic: create deployment marker (finished)"
        uses: nordicfactory/shared-github-actions/new-relic/create-deployment@main
        with:
          NEW_RELIC_API_KEY: ${{env.secret-newrelic-api-key}}
          application-name: "CreativePreviewService"
          environment: ${{ steps.set_vars.outputs.environment }}
          description: "Deployment finished ${{ steps.set_vars.outputs.region }}"
          version: ${{ env.COMMIT_MESSAGE }}

      - name: Run Smoke Test
        id: smoke_test
        uses: nordicfactory/shared-github-actions/rest@main
        with:
          warmup-url: "${{ env.warmup-url }}${{ steps.branch_name.outputs.ingress-path }}${{env.warmup-path}}"
          files: apps/creative-preview/infrastructure/tests/smoke-tests.http
          env_file: apps/creative-preview/infrastructure/tests/http-client.env.json
          env-variables: "prefix-path=${{ steps.branch_name.outputs.ingress-path }}"
          environment: ${{ steps.set_vars.outputs.environment }}

      - name: "Slack: report status"
        if: always()
        uses: nordicfactory/shared-github-actions/deploy-message/report-deployment-status@main
        with:
          status: ${{ job.status }}
          smoke-test-outcome: ${{ steps.smoke-test.outcome }}
          ts: ${{ steps.send-deploy-started.outputs.ts }}
          reaction-ts: ${{ steps.parse-message-state.outputs.ts }}
          channel-id: ${{ steps.parse-message-state.outputs.channel-id }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          success-message: "${{ matrix.environment-region }}: Deployed 🗸"
          failure-message: "${{ matrix.environment-region }}: Failed ❌"
          smoke-test-failure-message: "${{ matrix.environment-region }}: <${{ steps.smoke-test.outputs.test-report }}|Smoke tests> failed ❌"
          cancelled-message: "${{ matrix.environment-region }}: Cancelled 🛇"
          environment-region: ${{ matrix.environment-region }}

  post-deploy:
    needs: [ prepare-deploy, deploy ]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - uses: actions/checkout@v4

      - name: "Slack: get release info"
        id: release-info
        uses: nordicfactory/shared-github-actions/slack/create-build-info@main
        with:
          service-name: ${{ env.NAME }}

      - name: "Slack: send done message"
        uses: nordicfactory/shared-github-actions/deploy-message/done-deploy-message@main
        with:
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          success-message: "${{ env.DEPLOYMENT_DESCRIPTION }}: Done 🗸"
          failure-message: "${{ env.DEPLOYMENT_DESCRIPTION }}: Failed ❌"
          cancelled-message: "${{ env.DEPLOYMENT_DESCRIPTION }}: Cancelled 🛇"
          status: ${{ needs.deploy.result }}
          state: ${{ needs.prepare-deploy.outputs.message-state }}