name: "Studio deploy Production"

on:
  workflow_dispatch:
  push:
    branches:
      - main

concurrency:
  group: studio-production
  cancel-in-progress: true

env:
  DEPLOYMENT_DESCRIPTION: "" # Is set by create-build-info
  RELEASE_CHANNEL_ID: C04V81W1FTN

jobs:
  report-release-start:
    runs-on: ubuntu-latest
    outputs:
      ts: ${{ steps.slack-message.outputs.ts }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Get Release info
        uses: nordicfactory/shared-github-actions/slack/create-build-info@main
        with:
          service-name: 'studio-client'

      - name: Send deploy start slack message
        uses: nordicfactory/shared-github-actions/slack/send-message@main
        id: slack-message
        with:
          channel-id: ${{ env.RELEASE_CHANNEL_ID }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          message: "${{ env.DEPLOYMENT_DESCRIPTION }}: Starting::loading-dots:"

  preview-build-and-deploy:
    needs: report-release-start
    uses: ./.github/workflows/studio-client.yml
    name: Preview Studio for smoke test
    secrets: inherit
    if: ${{ always() }}
    permissions:
      actions: read
      contents: read
      packages: read
    with:
      branch: preview
      environment: "production"
      cf-project-name: "studio"

  preview-smoke-test:
    needs: preview-build-and-deploy
    runs-on: ubuntu-latest
    if: ${{ always() }}
    permissions:
      actions: read
      contents: read
      id-token: write
      packages: read
      checks: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node
        uses: ./.github/actions/node-setup
        with:
          bf-org-npm-token: ${{ secrets.BF_ORG_NPM_TOKEN }}

      - name: "Azure: set environment variables"
        id: set_vars
        uses: nordicfactory/shared-github-actions/azure/defaults@main
        with:
          environment-region: production
        
      - name: 'Playwright: Setup and Run'
        uses: nordicfactory/shared-github-actions/testing/run-smoke-test@main
        with:
          additional-test-files: ".github/additional-smoke-tests/**"
          azure-credentials: |
            {
              "clientId": "${{ secrets.AZURE_DEPLOY_CLIENT_ID }}",
              "clientSecret": "${{ secrets.AZURE_DEPLOY_CLIENT_SECRET }}",
              "tenantId": "${{ secrets.AZURE_DEPLOY_TENANT_ID }}",
              "subscriptionId": "${{ steps.set_vars.outputs.subscription-id }}"
            }
          test: "check-app-health.spec.ts studio-save.spec.ts"
          slack-channel-id: CKTLZ8YV9
          options: |
            {
              "baseUrl": "https://preview-studio.bannerflow.com",
              "pageHealthUrl": "https://preview-studio.bannerflow.com/brand/642e9c43cf5ac3fbe4a15656/creativeset/326910?version=531079",
              "pageHealthSelector": "studio-creative"
            }
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}

  build-and-deploy:
    needs: preview-smoke-test
    uses: ./.github/workflows/studio-client.yml
    name: Studio - Build and Deploy
    secrets: inherit
    if: ${{ needs.preview-smoke-test.result == 'success' }}
    permissions:
      actions: read
      contents: read
      packages: read
    with:
      branch: "main"
      environment: "production"
      cf-project-name: "studio"

  report-release-complete:
    needs: build-and-deploy
    runs-on: ubuntu-latest
    if: ${{ always() }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          clean: false

      - name: Get Release info
        uses: nordicfactory/shared-github-actions/slack/create-build-info@main
        with:
          service-name: 'studio-client'

      - name: Send deploy complete slack message
        if: ${{ needs.build-and-deploy.result == 'success' }}
        uses: nordicfactory/shared-github-actions/slack/send-message@main
        with:
          channel-id: ${{ env.RELEASE_CHANNEL_ID }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          message: "${{ env.DEPLOYMENT_DESCRIPTION }}: Deployed 🗸"
          ts: ${{ needs.report-release-start.outputs.ts }}

      - name: Send deploy cancelled slack message
        if: ${{ needs.build-and-deploy.result == 'cancelled' }}
        uses: nordicfactory/shared-github-actions/slack/send-message@main
        with:
          channel-id: ${{ env.RELEASE_CHANNEL_ID }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          message: "${{ env.DEPLOYMENT_DESCRIPTION }}: Cancelled :warning:"
          ts: ${{ needs.report-release-start.outputs.ts }}

      - name: Send deploy skip slack message
        if: ${{ needs.build-and-deploy.result == 'skipped' }}
        uses: nordicfactory/shared-github-actions/slack/send-message@main
        with:
          channel-id: ${{ env.RELEASE_CHANNEL_ID }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          message: "${{ env.DEPLOYMENT_DESCRIPTION }}: Skipped :no_entry_sign:"
          ts: ${{ needs.report-release-start.outputs.ts }}

      - name: Send deploy failure slack message
        if: ${{ needs.build-and-deploy.result == 'failure' }}
        uses: nordicfactory/shared-github-actions/slack/send-message@main
        with:
          channel-id: ${{ env.RELEASE_CHANNEL_ID }}
          slack-bot-token: ${{ secrets.SLACK_BOT_TOKEN }}
          message: "${{ env.DEPLOYMENT_DESCRIPTION }}: Failed :x:"
          ts: ${{ needs.report-release-start.outputs.ts }}
