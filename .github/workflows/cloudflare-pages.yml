name: Cloudflare pages
on:
  pull_request:
    branches:
      - main
    types:
      - closed

jobs:
  cloudflare-cleanup:
    runs-on: ubuntu-latest
    name: Delete cloudflare pages deployment
    steps:
      - name: Checkout
        id: checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: ./.github/actions/node-setup
        with:
          bf-org-npm-token: ${{ secrets.BF_ORG_NPM_TOKEN }}

      - name: Build Action
        run: |
          pnpm i -g @vercel/ncc
          ncc build .github/actions/cloudflare/src/index.ts -o .github/actions/cloudflare/dist

      - name: Run action
        uses: ./.github/actions/cloudflare
        with:
          cf-account-id: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          cf-api-token: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          cf-project-name: 'studio-sandbox'
