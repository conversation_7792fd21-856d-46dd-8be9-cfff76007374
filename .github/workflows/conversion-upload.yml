name: Upload Conversion Script to Blob Storage

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'sandbox'
        type: 'choice'
        options:
          - sandbox
          - production

jobs:
  upload-conversion-script:
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}
    steps:
      - name: "git: checkout"
        uses: actions/checkout@v4

      - name: "node: setup"
        uses: ./.github/actions/node-setup
        with:
          bf-org-npm-token: ${{ secrets.BF_ORG_NPM_TOKEN }}

      - name: "nx: build conversion script"
        run: npx nx run ad:build-conversion

      - name: "azure: set environment variables"
        id: set_vars
        uses: nordicfactory/shared-github-actions/azure/defaults@main
        with:
          environment: "${{ github.event.inputs.environment }}"

      - name: "azure: login"
        uses: nordicfactory/shared-github-actions/azure/login@main
        with:
          client-id: ${{ secrets.AZURE_DEPLOY_CLIENT_ID }}
          client-secret: ${{ secrets.AZURE_DEPLOY_CLIENT_SECRET }}
          tenant-id: ${{ secrets.AZURE_DEPLOY_TENANT_ID }}
          subscription-id: ${{ steps.set_vars.outputs.subscription-id }}

      - name: "nx: release conversion script"
        run: npx nx generate @studio/tools:upload-conversion-script --environment=${{github.event.inputs.environment}}
