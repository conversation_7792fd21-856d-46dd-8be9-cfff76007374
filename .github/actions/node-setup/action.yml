name: 'Setup Node'
description: 'Setup Node. Installs Node 20, handles authorization using AZURE_DEVOPS_ARTIFACTS_PAT and installs and caches dependencies'
inputs:
    bf-org-npm-token:
        description: 'Token for npm'
        required: true
    setup-node:
        description: 'Run setup node inside action'
        required: false
        default: 'true'
    install-deps:
        description: 'Run package manager install'
        required: false
        default: 'true'
runs:
    using: 'composite'
    steps:
        - name: Authenticate NPM
          uses: nordicfactory/shared-github-actions/build/node/authenticate@main
          with:
              bf-org-npm-token: ${{ inputs.bf-org-npm-token }}

        - name: Authenticate NPM - GH
          shell: bash
          run: |
              token="${{ github.token }}"
              echo "//npm.pkg.github.com/:_authToken=$token" >> .npmrc

        - name: 'Setup pnpm'
          uses: pnpm/action-setup@v4
          with:
              version: 9.12.3

        - name: 'Setup Node.js'
          uses: actions/setup-node@v4
          if: inputs.setup-node == 'true'
          with:
              node-version: 20
              cache: 'pnpm'

        - name: 'Install Dependencies'
          shell: bash
          if: inputs.install-deps == 'true'
          run: pnpm install --frozen-lockfile
