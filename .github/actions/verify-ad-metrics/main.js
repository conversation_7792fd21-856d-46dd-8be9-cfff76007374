// https://github.com/actions/javascript-action/blob/main/src/main.js
import * as core from '@actions/core';
import { MongoClient, ServerApiVersion } from 'mongodb';

import { slackPostMessage } from '../../../tools/scripts/slack-utils.js';
import { generateChartImages } from './chart-helper.js';

const mongodbUri = core.getInput('mongodb-connection-string', { required: true });
const client = new MongoClient(mongodbUri, {
    serverApi: {
        version: ServerApiVersion.v1,
        strict: true,
        deprecationErrors: true
    }
});

/**
 * A date range with start and end dates.
 * @typedef {{start: Date, end: Date}} DateRange
 */

/**
 * The main function for the action.
 * @returns {Promise<void>} Resolves when the action is complete.
 */
export async function run() {
    try {
        const creativeId = '113729';
        core.info(`Reference creative: ${creativeId}`);
        const db = client.db('adscanner');
        const collectionExists = await db.listCollections({ name: 'ad_scans' }).hasNext();
        if (!collectionExists) {
            throw Error("Collection 'ad_scans' does not exist.");
        }
        const dates = getDates();
        let lastMonth = {};
        let lastWeek = {};
        const collection = db.collection('ad_scans');
        for (const { start, end } of dates) {
            const cursor = await aggregateScanMetrics(collection, creativeId, start, end);
            const metrics = await cursor.toArray();
            if (metrics.length === 0) {
                throw Error(`No metrics found for creative ${creativeId} between ${start} and ${end}`);
            }
            if (metrics.length !== 1) {
                throw Error(`Expected to find one value, but found ${metrics.length}`);
            }
            if (start === dates[0].start) {
                lastMonth = metrics[0];
            } else {
                lastWeek = metrics[0];
            }
        }

        const sizeChange = getMetricChange(lastMonth, lastWeek, 'totalSize95');
        const mtwtChange = getMetricChange(lastMonth, lastWeek, 'mtwt95');
        const rtChange = getMetricChange(lastMonth, lastWeek, 'renderTime95');
        await reportIfSignificantChange(sizeChange, 5, 'total size', ':rage:');
        await reportIfSignificantChange(mtwtChange, 150, 'main thread work time', ':fearful:');

        // Generates chart of the ad metrics, uploads to our blob storage and returns the URL
        const imageUrls = await generateChartImages(collection, creativeId);
        const reportMessage = `Size: ${lastWeek.totalSize95 / 1000}kB ${sizeChange}%, Render time: ${lastWeek.renderTime95}ms ${rtChange}%, MTWT: ${lastWeek.mtwt95}ms ${mtwtChange}%`;

        const message = getSlackBlocks(reportMessage, imageUrls[0], imageUrls[1]);
        await slackPostMessage(message, 'blocks');
    } catch (error) {
        core.setFailed(error.message);
    } finally {
        await client.close();
        console.log('Closed MongoDB connection.');
    }
}

/**
 * Get the start and end dates for the metrics query.
 * @returns {[DateRange,DateRange]} - An array of 2 DateRange's for last month and last week.
 * First one is the last month, second one is the last 7 days.
 */
function getDates() {
    // Calculate start & end dates
    const last7DaysStartDay = new Date();
    last7DaysStartDay.setDate(last7DaysStartDay.getDate() - 7);
    const last7DaysEndDay = new Date();
    const currentMonthEndDay = new Date();
    currentMonthEndDay.setDate(currentMonthEndDay.getDate() - 1);
    const currentMonthStartDay = new Date();
    currentMonthStartDay.setDate(currentMonthStartDay.getDate() - 30);

    const formatDate = date => {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    console.log(
        'Last 7 days start-end:',
        formatDate(last7DaysStartDay),
        '-',
        formatDate(last7DaysEndDay)
    );
    console.log(
        'Current month start-end:',
        formatDate(currentMonthStartDay),
        '-',
        formatDate(currentMonthEndDay)
    );

    return [
        {
            start: currentMonthStartDay,
            end: currentMonthEndDay
        },
        {
            start: last7DaysStartDay,
            end: last7DaysEndDay
        }
    ];
}

/**
 * Calculate the percentage change between two values.
 * @param lastMonth - The last month values to compare.
 * @param lastWeek - The last week values to compare.
 * @param metricName - The name of the metric to calculate the change for.
 * @returns {number} - The percentage change between the two values.
 */
function getMetricChange(lastMonth, lastWeek, metricName) {
    // Calculate TOTAL percentage difference ((last 7 days - current month) / current month ) * 100
    let percentageChange =
        ((lastWeek[metricName] - lastMonth[metricName]) / lastMonth[metricName]) * 100;
    percentageChange = Math.round(percentageChange * 100) / 100;

    core.info(`${metricName} last 7 days: ${lastWeek[metricName]}`);
    core.info(`${metricName} last 30 days: ${lastMonth[metricName]}`);
    core.info(`${metricName} change: ${percentageChange}`);
    return percentageChange;
}

/**
 * Report a message to Slack if the percentage change is greater than the threshold.
 * @param percentageChange - The percentage value
 * @param threshold - The threshold value
 * @param metricName - The name of the metric used for the slack message
 * @param emoji - The emoji appended in the slack message
 * @returns {Promise<void>}
 */
async function reportIfSignificantChange(percentageChange, threshold, metricName, emoji) {
    if (percentageChange > threshold) {
        const warningMessage = `Significant increase in ${metricName}. +${percentageChange}% is >${threshold}% `;
        await slackPostMessage(warningMessage + emoji);
        core.info(warningMessage);
    }
}

/**
 *
 * @param collection - The `ad_scan` collection to query
 * @param creativeId - The creative id to find
 * @param startDay - The start date for the query
 * @param endDay - The end date for the query
 * @returns {AggregationCursor<any>}
 */
function aggregateScanMetrics(collection, creativeId, startDay, endDay) {
    return collection.aggregate([
        {
            $match: {
                creative: creativeId,
                timestamp: { $gte: new Date(startDay), $lte: new Date(endDay) }
            }
        },
        {
            $group: {
                _id: null,
                totalSize95: {
                    $percentile: {
                        input: '$resources.Total.size',
                        p: [0.95],
                        method: 'approximate'
                    }
                },
                mtwt95: {
                    $percentile: {
                        input: '$mainThread.mainThreadWorkTime',
                        p: [0.95],
                        method: 'approximate'
                    }
                },
                renderTime95: {
                    $percentile: {
                        input: '$customTimings.ad-creation-to-success.duration',
                        p: [0.95],
                        method: 'approximate'
                    }
                }
            }
        }
    ]);
}

function getSlackBlocks(headline, workTimeImageUrl, renderTimeImageUrl) {
    return [
        {
            type: 'header',
            text: {
                type: 'plain_text',
                text: 'Ad Metrics Report',
                emoji: true
            }
        },
        {
            type: 'section',
            text: {
                type: 'mrkdwn',
                text: headline
            }
        },
        {
            type: 'divider'
        },
        {
            type: 'image',
            title: {
                type: 'plain_text',
                text: 'Main Thread Times',
                emoji: true
            },
            image_url: workTimeImageUrl,
            alt_text: 'main thread time chart'
        },
        {
            type: 'image',
            title: {
                type: 'plain_text',
                text: 'Render Times',
                emoji: true
            },
            image_url: renderTimeImageUrl,
            alt_text: 'render time chart'
        }
    ];
}
