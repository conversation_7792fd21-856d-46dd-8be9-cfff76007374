import * as core from '@actions/core';
import { createCanvas } from 'canvas';
import Chart from 'chart.js/auto';

import { uploadToAzure } from '../../../tools/scripts/azure-utils.js';

/**
 * Generates a chart image for the given creative ID
 * @param {import('mongodb').Collection} collection
 * @param {string} creativeId
 * @returns {Promise<Array<string>>}
 */
export async function generateChartImages(collection, creativeId) {
    const chartData = await getChartData(collection, creativeId);
    core.info(`Chart data received ${chartData.length} entries`);
    const chartBuffer = await generateMetricsChart(chartData);
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `charts/creative-${creativeId}-${timestamp}`;

    // Upload the chart to Azure
    const environment = process.env.NODE_ENV === 'production' ? 'production' : 'sandbox';
    core.info(`Uploading chart to Azure in: ${environment}`);
    const workTimeImageUrl = await uploadToAzure(
        environment,
        'scans',
        chartBuffer[0],
        filename + '_worktime.png',
        'image/png'
    );
    const renderTimeImageUrl = await uploadToAzure(
        environment,
        'scans',
        chartBuffer[1],
        filename + '_rendertime.png',
        'image/png'
    );
    core.info(`Charts uploaded to: ${workTimeImageUrl}, ${renderTimeImageUrl}`);

    return [workTimeImageUrl, renderTimeImageUrl];
}

/**
 * Get the date ranges for the last month and week
 * @param {import('mongodb').Collection} collection
 * @param {string} creativeId
 * @returns {Promise<Array<*>>}
 */
async function getChartData(collection, creativeId) {
    // get all scans of the creative in the last 30 days
    return collection
        .find({
            creative: creativeId,
            timestamp: {
                $gte: new Date(new Date().setDate(new Date().getDate() - 30)),
                $lte: new Date()
            }
        })
        .toArray();
}

/**
 * Fill the canvas with a color
 * @param {CanvasRenderingContext2D} ctx
 * @param {number} width
 * @param {number} height
 */
function fillCanvas(ctx, width, height) {
    ctx.fillStyle = 'rgba(255,255,255)';
    ctx.fillRect(0, 0, width, height);
}

/**
 * Generate a chart image for the metrics data
 * @param {Array<*>} data - The metrics data to visualize
 * @returns {Promise<Array<Buffer>>} - A buffer containing the PNG image
 */
async function generateMetricsChart(data) {
    const width = 800;
    const height = 600;
    const mainCanvas = createCanvas(width, height);
    const renderTimeCanvas = createCanvas(width, height);
    const mainCtx = mainCanvas.getContext('2d');
    const renderTimeCtx = renderTimeCanvas.getContext('2d');

    fillCanvas(mainCtx, width, height);
    fillCanvas(renderTimeCtx, width, height);

    if (!data || !data.length) {
        core.warning('No performance data available for the chart');
        // Optionally, create a blank chart with a message
        mainCtx.font = '20px Arial';
        mainCtx.textAlign = 'center';
        mainCtx.fillText('No performance data available for the last 30 days.', width / 2, height / 2);
        return [mainCanvas.toBuffer('image/png')];
    }

    // Format dates for x-axis
    const labels = data.map(item =>
        new Date(item.timestamp).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
    );

    // Extract performance metrics
    const workTimeData = data.map(item => item.mainThread?.mainThreadWorkTime ?? null); // Use null for missing data
    const taskTimeData = data.map(item => item.mainThread?.mainThreadTaskTime ?? null);
    const scriptEvalTimeData = data.map(item => item.mainThread?.scriptEvaluationTime ?? null);
    const renderTimeData = data.map(
        item => item.customTimings['ad-creation-to-success']?.duration ?? null
    );

    const createGradient = color => {
        const gradient = mainCtx.createLinearGradient(0, 0, 0, height);
        gradient.addColorStop(0, `${color}B3`); // 70% opacity
        gradient.addColorStop(1, `${color}33`); // 20% opacity
        return gradient;
    };

    const datasets = [
        {
            label: 'Main Thread Work Time',
            data: workTimeData,
            borderColor: '#36A2EB',
            backgroundColor: 'rgba(54, 162, 235, 0.4)',
            fill: true,
            tension: 0.4,
            pointRadius: 3,
            yAxisID: 'yMs'
        },
        {
            label: 'Main Thread Task Time',
            data: taskTimeData,
            borderColor: '#FF6384',
            backgroundColor: 'rgba(255, 99, 132, 0.6)',
            fill: true,
            tension: 0.4,
            pointRadius: 3,
            yAxisID: 'yMs'
        },
        {
            label: 'Script Evaluation Time',
            data: scriptEvalTimeData,
            borderColor: '#FFCD56',
            backgroundColor: 'rgba(255, 205, 86, 0.8)',
            fill: true,
            tension: 0.4,
            pointRadius: 3,
            yAxisID: 'yMs'
        }
    ];

    const mainChart = new Chart(mainCanvas, {
        type: 'line',
        data: {
            labels,
            datasets
        },
        options: {
            plugins: {
                title: {
                    display: true,
                    text: `Ad Metrics (Last 30 Days) - Creative ID: ${data[0]?.creative || 'N/A'}`,
                    font: {
                        size: 20,
                        weight: 'bold'
                    },
                    padding: {
                        top: 10,
                        bottom: 30
                    }
                },
                legend: {
                    position: 'top',
                    labels: {
                        font: {
                            size: 12
                        },
                        usePointStyle: true
                    }
                }
            },
            scales: {
                x: { ticks: { font: { size: 10 } } },
                yMs: { beginAtZero: true, title: { display: true, text: 'Time (ms)' } }
            },
            layout: {
                padding: {
                    left: 10,
                    right: 10,
                    top: 0,
                    bottom: 10
                }
            },
            elements: {
                line: {
                    borderWidth: 2
                },
                point: {
                    radius: 3
                }
            }
        }
    });

    const renderTimeChart = new Chart(renderTimeCanvas, {
        type: 'line',
        data: {
            labels,
            datasets: [
                {
                    label: `Render Time - Creative ID: ${data[0]?.creative || 'N/A'}`,
                    data: renderTimeData,
                    backgroundColor: createGradient('#73e482'),
                    borderColor: '#6dff63',
                    fill: true,
                    tension: 0.4,
                    pointRadius: 3
                }
            ]
        },
        options: {
            plugins: {
                title: {
                    display: true,
                    text: 'Render Time (Last 30 Days)',
                    font: { size: 16, weight: 'bold' }
                },
                legend: {
                    position: 'top',
                    labels: {
                        font: {
                            size: 12
                        },
                        usePointStyle: true
                    }
                }
            },
            scales: {
                x: { ticks: { font: { size: 8 } } },
                y: { beginAtZero: true, title: { display: true, text: 'Time (ms)' } }
            },
            elements: {
                line: {
                    borderWidth: 2
                },
                point: {
                    radius: 3
                }
            }
        }
    });

    // Wait for rendering to complete (Chart.js rendering is synchronous for canvas)
    // but a small delay can sometimes help with complex charts or specific environments.
    await new Promise(resolve => setTimeout(resolve, 50));

    const workTimePng = mainCanvas.toBuffer('image/png');
    const renderTimePng = renderTimeCanvas.toBuffer('image/png');

    mainChart.destroy();
    renderTimeChart.destroy();

    return [workTimePng, renderTimePng];
}
