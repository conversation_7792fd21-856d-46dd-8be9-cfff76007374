name: 'Nx Prepare Affected'
description: Sets the correct SHAs for the base and head for `nx affected` commands

runs:
    using: 'composite'
    steps:
        - name: Derive appropriate SHAs for base and head for `nx affected` commands
          uses: nrwl/nx-set-shas@v4

        - name: 'Print SHAs'
          shell: bash
          run: |
              echo "BASE: ${{ env.NX_BASE }}"
              echo "HEAD: ${{ env.NX_HEAD }}"

        - name: 'Checkout main branch'
          if: ${{ github.event_name == 'pull_request' }}
          shell: bash
          run: git branch --track main origin/main
