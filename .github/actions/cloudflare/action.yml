name: 'Cloudflare paged deployment cleanup'
description: 'Deletes the cloudflare pages deployment related to the current branch'
inputs:
    cf-account-id: # id of input
        description: 'Cloudflare account id'
        required: true
        default: ''
    cf-api-token: # id of input
        description: 'Cloudflare api token'
        required: true
        default: ''
    cf-project-name: # id of input
        description: 'Cloudflare project name'
        required: true
        default: ''
runs:
    using: 'node20'
    main: 'dist/index.js'
