import * as core from '@actions/core';
import * as github from '@actions/github';

interface DeploymentResult {
    result: {
        id: string;
        aliases?: `https://${string}.pages.dev`[];
        deployment_trigger: {
            metadata: {
                branch: string;
            };
        };
    }[];
}

let CF_DEPLOYMENTS_GET_URL =
    'https://api.cloudflare.com/client/v4/accounts/{accountId}/pages/projects/{projectName}/deployments?per_page=10&page={page}';
let CF_DEPLOYMENTS_DELETE_URL =
    'https://api.cloudflare.com/client/v4/accounts/{accountId}/pages/projects/{projectName}/deployments';

let accountId: string;
let apiToken: string;
let projectName: string;
let authHeader: Pick<RequestInit, 'headers'>;

(async () => {
    try {
        accountId = core.getInput('cf-account-id');
        apiToken = core.getInput('cf-api-token');
        projectName = core.getInput('cf-project-name');
        authHeader = {
            headers: {
                'Content-Type': 'application/json;charset=UTF-8',
                Authorization: `Bearer ${apiToken}`
            }
        };

        CF_DEPLOYMENTS_GET_URL = CF_DEPLOYMENTS_GET_URL.replace('{accountId}', accountId).replace(
            '{projectName}',
            projectName
        );
        CF_DEPLOYMENTS_DELETE_URL = CF_DEPLOYMENTS_DELETE_URL.replace('{accountId}', accountId).replace(
            '{projectName}',
            projectName
        );

        console.log(`Current branch: ${(github.context as any).payload.pull_request.head.ref}`);

        await deleteStaleEnvironments((github.context as any).payload.pull_request.head.ref);
    } catch (error: any) {
        core.setFailed(error.message);
    }
})();

async function deleteStaleEnvironments(pullRequestName: string): Promise<void> {
    const deployments = await getAllDeployments();

    const aliasCheckRegExp = new RegExp(pullRequestName, 'i');

    const deploymentsToDelete = deployments.result.filter(({ deployment_trigger }) => {
        return aliasCheckRegExp.test(deployment_trigger.metadata.branch);
    });

    for (const deployment of deploymentsToDelete) {
        console.log(
            `Deleting stale deployment slot ${pullRequestName} with Deployment ID ${deployment.id}.`
        );

        try {
            await fetch(`${CF_DEPLOYMENTS_DELETE_URL}/${deployment.id}?force=true`, {
                method: 'DELETE',
                headers: authHeader.headers
            });
        } catch (e) {
            console.error('Could not delete deployment slot');
        }
    }

    console.log('Cleanup completed.');
}

async function getAllDeployments(): Promise<DeploymentResult> {
    const deployments: DeploymentResult = {
        result: []
    };

    let resolve;
    const newFetchPromise = new Promise(res => {
        resolve = res;
    });

    console.log('Fetching all deployments...');

    (async function fetchDeployments(page: number): Promise<void> {
        const requestUrl = CF_DEPLOYMENTS_GET_URL.replace('{page}', `${page}`);
        try {
            const response = await fetch(requestUrl, authHeader);

            const result = (await response.json()) as DeploymentResult;
            console.log(`Request to ${requestUrl} completed.`);

            if (!result.result.length || page > 3) {
                resolve();
                return;
            }

            deployments.result.push(...result.result);

            setTimeout(() => {
                fetchDeployments(page + 1);
            }, 300);
        } catch (e) {
            console.log(`Error fetching deployments on ${requestUrl}`, e);
            resolve();
        }
    })(1);

    await newFetchPromise;
    console.log(`Deployments found:`, deployments.result.length);
    return deployments;
}
