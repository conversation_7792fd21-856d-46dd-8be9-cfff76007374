name: 'Playwright: Setup'
description: 'Set up Playwright with caching'
inputs:
    browsers:
        description: 'Browsers to install for Playwright (comma-separated)'
        required: false
        default: 'chromium'

outputs:
    cache-hit:
        description: 'Whether the Playwright cache was restored'
        value: ${{ steps.playwright-cache.outputs.cache-hit }}

runs:
    using: 'composite'
    steps:
        - name: 'playwright: get version'
          id: playwright-version
          shell: bash
          run: |
              PLAYWRIGHT_VERSION=$(node -p "require('./package.json').dependencies.playwright")
              PLAYWRIGHT_TEST_VERSION=$(node -p "require('./package.json').devDependencies['@playwright/test']")
              echo "'playwright': $PLAYWRIGHT_VERSION"
              echo "'@playwright/test': $PLAYWRIGHT_TEST_VERSION"
              echo "PLAYWRIGHT_VERSION=$PLAYWRIGHT_VERSION" >> $GITHUB_ENV

        - name: 'playwright: generate cache key'
          id: generate-cache-key
          shell: bash
          run: |
              # Sort browsers alphabetically to ensure consistent cache key
              SORTED_BROWSERS=$(echo "${{ inputs.browsers }}" | tr ',' '\n' | sort | tr '\n' '-' | sed 's/-$//')
              echo "CACHE_KEY=${{ runner.os }}-playwright-${{ env.PLAYWRIGHT_VERSION }}-$SORTED_BROWSERS" >> $GITHUB_ENV

        - name: 'playwright: get cache'
          id: playwright-cache
          uses: actions/cache@v4
          with:
              path: |
                  ~/.cache/ms-playwright
              key: ${{ env.CACHE_KEY }}
              restore-keys: |
                  ${{ runner.os }}-playwright-

        - name: 'playwright: install'
          if: steps.playwright-cache.outputs.cache-hit != 'true'
          run: pnpm exec playwright install --with-deps
          shell: bash

          # Some WebKit dependencies seem to lay outside the cache and will need to be installed separately
        - name: Install system dependencies for WebKit
          if: ${{ contains(inputs.browsers, 'webkit') && steps.playwright-cache.outputs.cache-hit == 'true' }}
          shell: bash
          run: npx playwright install-deps webkit
