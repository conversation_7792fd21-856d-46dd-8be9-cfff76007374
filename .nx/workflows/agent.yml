#  https://nx.dev/ci/reference/launch-templates#private-npm-registry
launch-templates:
  custom-studio-linux-medium-js:
    resource-class: 'docker_linux_amd64/medium'
    image: 'ubuntu22.04-node20.11-v9'
    init-steps:
      - name: Checkout
        uses: 'nrwl/nx-cloud-workflows/v4/workflow-steps/checkout/main.yaml'

      - name: Authenticate to GitHub Registry
        script: |
          echo "Setting npm token"
          echo "NPM_TOKEN: ${NPM_TOKEN}"
          ls -la
          echo "//npm.pkg.github.com/:_authToken=${NPM_TOKEN}" >> .npmrc
          echo "Contents of .npmrc:"
          cat .npmrc

      - name: Restore Node Modules Cache
        uses: 'nrwl/nx-cloud-workflows/v4/workflow-steps/cache/main.yaml'
        inputs:
          key: 'package-lock.json|pnpm-lock.yaml'
          paths: 'node_modules'
          base-branch: 'main'
      - name: Install Node Modules
        uses: 'nrwl/nx-cloud-workflows/v4/workflow-steps/install-node-modules/main.yaml'

      - name: Restore Browser Binary Cache
        uses: 'nrwl/nx-cloud-workflows/v4/workflow-steps/cache/main.yaml'
        inputs:
          key: 'package-lock.json|yarn.lock|pnpm-lock.yaml|"browsers"'
          paths: |
            '../.cache/Cypress'
            '../.cache/ms-playwright'
          base-branch: 'main'

      - name: Install Browsers (if needed)
        uses: 'nrwl/nx-cloud-workflows/v4/workflow-steps/install-browsers/main.yaml'