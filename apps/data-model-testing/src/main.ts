// NOTE: OpenTelemetry has to be the first import
import '@shared/server/monitoring/instrumentation';
// rest of app imports
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { utilities as nestWinstonModuleUtilities, WinstonModule } from 'nest-winston';
import { createLogger, format, transports } from 'winston';
import { AppModule } from './app/app.module';

async function bootstrap(): Promise<void> {
    const loggerInstance = createLogger({
        level: process.env.LOG_LEVEL ?? 'info',
        transports: [
            new transports.Console({
                format: format.combine(
                    format.timestamp(),
                    format.ms(),
                    nestWinstonModuleUtilities.format.nestLike('data-model-testing', {
                        colors: process.env.STAGE === 'local',
                        prettyPrint: true
                    })
                )
            })
        ]
    });

    const app = await NestFactory.create<NestFastifyApplication>(
        AppModule,
        new FastifyAdapter({
            logger: process.env.STAGE === 'local'
        }),
        {
            logger: WinstonModule.createLogger({
                instance: loggerInstance
            })
        }
    );

    app.enableCors();

    const configService = app.get(ConfigService);
    const port = configService.getOrThrow<number>('port');

    await app.listen(port, '::');
    Logger.log(`data-model-testing is running on: ${await app.getUrl()}`, 'Bootstrap');
}

bootstrap().catch(e => {
    Logger.error(`Failed to bootstrap, due to ${e}`, 'Bootstrap');
    process.exit(1);
});
