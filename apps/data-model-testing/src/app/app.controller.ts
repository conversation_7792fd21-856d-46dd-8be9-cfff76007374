import { initializeElements } from '@creative/nodes';
import { visitAllNodes } from '@creative/visitor';
import { convertCreativesetToDto } from '@data/deserialization/creativeset';
import { CreativeSetDtoV2 } from '@domain/api/generated/sapi';
import { ICreativeset } from '@domain/creativeset';
import { IFontFamily } from '@domain/font-families';
import { BadRequestException, Controller, Get, Query } from '@nestjs/common';
import { CreativesetService } from '@shared/server/data-access/creativeset';
import { Auth } from '@shared/server/data-access/creativeset/auth.decorator';
import { fakeVisitor } from './data-model-test-helpers/helpers';

@Controller()
export class AppController {
    constructor(private creativesetService: CreativesetService) {}

    @Auth('studio')
    @Get('/test-creativeset-conversion')
    async testCreativesetConversion(
        @Query('creativesetId') creativesetId: string
    ): Promise<CreativeSetDtoV2> {
        const creativeset = await this.creativesetService.getCreativesetFromDesignApi(creativesetId);

        for (const creative of creativeset.creatives) {
            if (!creative.design) {
                continue;
            }

            const { document } = creative.design;

            try {
                initializeElements(document, {
                    versionProperties: creative.version.properties,
                    defaultVersionProperties: creativeset.defaultVersion.properties,
                    fontFamilies: creativeset.fontFamilies as IFontFamily[]
                });
            } catch (e) {
                throw new BadRequestException(
                    `Could not intitialize elements for creative with id ${creative.id}. ${e}`
                );
            }

            try {
                visitAllNodes(document, fakeVisitor(), true);
            } catch (e) {
                throw new BadRequestException(
                    `Could not "render" all nodes for creative with id ${creative.id}. ${e}`
                );
            }
        }

        try {
            return convertCreativesetToDto(creativeset as ICreativeset);
        } catch (e) {
            throw new BadRequestException(`Could not serialize creativeset. ${e}`);
        }
    }
}
