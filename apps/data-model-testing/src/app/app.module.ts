import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthModule } from '@shared/server/auth';
import { AzureModule } from '@shared/server/azure';
import { SharedServerDataAccessModule } from '@shared/server/data-access';
import { HealthModule } from '@shared/server/health/lib/health.module';
import { ScriptsModule } from '@shared/server/scripts/scripts.module';
import options from '../environments/options';
import { AppController } from './app.controller';

@Module({
    imports: [
        HttpModule.register({
            timeout: 15000
        }),
        ConfigModule.forRoot({
            isGlobal: true,
            load: [options]
        }),
        HealthModule.withEndpoints({
            configModule: ConfigModule,
            configService: ConfigService,
            useFactory: (configService: ConfigService) => {
                return configService.get('health.readyEndpoints', { infer: true })!;
            }
        }),
        AuthModule,
        SharedServerDataAccessModule,
        AzureModule,
        ScriptsModule
    ],
    controllers: [AppController]
})
export class AppModule {}
