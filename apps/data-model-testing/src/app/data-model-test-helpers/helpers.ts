import {
    createViewElement,
    CreativeDataNode,
    isImageNode,
    isTextNode,
    isVideoNode,
    isWidgetNode
} from '@creative/nodes';
import { getTextShadowPropertyValue, getViewElementPropertyValue } from '@creative/rendering';
import { INodeVisitor } from '@creative/visitor';
import {
    IButtonElementDataNode,
    ICreativeDataNode,
    IEllipseElementDataNode,
    IImageElementDataNode,
    IRectangleElementDataNode,
    ITextElementDataNode,
    IVideoElementDataNode,
    OneOfElementDataNodes,
    OneOfTextDataNodes,
    OneOfViewNodes
} from '@domain/nodes';
import { IState } from '@domain/state';
import { ITextElementCharacterProperties, SpanType } from '@domain/text';
import { InternalWidgetEvent, IWidgetElementDataNode } from '@domain/widget';
import { BadRequestException } from '@nestjs/common';
import { isUrl } from '@studio/utils/url';
import { deepEqual } from '@studio/utils/utils';

export function fakeVisitor(): INodeVisitor {
    return {
        visitCreative_m: (creativeDataNode: CreativeDataNode): void => {
            visitCreative(creativeDataNode);
        },
        visitRectangle_m: (rect: IRectangleElementDataNode): void => {
            const viewElement = createViewElement(rect);
            setViewElementValuesWithState(viewElement, rect);
        },
        visitEllipse_m: (ellipse: IEllipseElementDataNode): void => {
            const viewElement = createViewElement(ellipse);
            setViewElementValuesWithState(viewElement, ellipse);
        },
        visitImage_m: (image: IImageElementDataNode): void => {
            const viewElement = createViewElement(image);
            setViewElementValuesWithState(viewElement, image);
        },
        visitButton_m: (text: IButtonElementDataNode): void => {
            const viewElement = createViewElement(text);
            setViewElementValuesWithState(viewElement, text);
        },
        visitText_m: (text: ITextElementDataNode): void => {
            const viewElement = createViewElement(text);
            setViewElementValuesWithState(viewElement, text);
        },
        visitWidget_m: (widget: IWidgetElementDataNode): void => {
            const viewElement = createViewElement(widget);
            setViewElementValuesWithState(viewElement, widget);
        },
        visitVideo_m: (video: IVideoElementDataNode): void => {
            const viewElement = createViewElement(video);
            setViewElementValuesWithState(viewElement, video);
        }
    };
}

function visitCreative(creative: ICreativeDataNode): void {
    if (
        !(
            typeof creative.fill === 'object' &&
            'red' in creative.fill &&
            'green' in creative.fill &&
            'blue' in creative.fill
        )
    ) {
        throw new BadRequestException(
            'creative.fill is not a valid color object. value: ',
            creative.fill
        );
    }
}

/** THIS IS A COPY OF `renderer.ts@_setViewElementValuesWithState` */
function setViewElementValuesWithState(
    viewElement: OneOfViewNodes,
    element: OneOfElementDataNodes,
    state: IState<number> = {}
): void {
    const isWidget = isWidgetNode(element);

    viewElement.x = getViewElementPropertyValue('x', element, state);
    viewElement.y = getViewElementPropertyValue('y', element, state);
    viewElement.width = getViewElementPropertyValue('width', element, state);
    viewElement.height = getViewElementPropertyValue('height', element, state);
    viewElement.originX = getViewElementPropertyValue('originX', element, state);
    viewElement.originY = getViewElementPropertyValue('originY', element, state);
    viewElement.scaleX = getViewElementPropertyValue('scaleX', element, state);
    viewElement.scaleY = getViewElementPropertyValue('scaleY', element, state);
    viewElement.rotationX = getViewElementPropertyValue('rotationX', element, state);
    viewElement.rotationY = getViewElementPropertyValue('rotationY', element, state);
    viewElement.rotationZ = getViewElementPropertyValue('rotationZ', element, state);
    viewElement.mirrorX = getViewElementPropertyValue('mirrorX', element, state);
    viewElement.mirrorY = getViewElementPropertyValue('mirrorY', element, state);
    viewElement.opacity = getViewElementPropertyValue('opacity', element, state);
    viewElement.border = getViewElementPropertyValue('border', element, state);
    viewElement.fill = getViewElementPropertyValue('fill', element, state);
    viewElement.shadows = getViewElementPropertyValue('shadows', element, state);
    viewElement.radius = getViewElementPropertyValue('radius', element, state);
    viewElement.filters = getViewElementPropertyValue('filters', element, state);

    if (isWidget && 1 !== 1) {
        viewElement.x -= (viewElement.width * (1 - 1)) / 2;
        viewElement.y -= (viewElement.height * (1 - 1)) / 2;
    }

    if (isImageNode(viewElement)) {
        const imgElement = element as IImageElementDataNode;
        if (imgElement.feed) {
            viewElement.feed = getViewElementPropertyValue('feed', imgElement, state);
            imgElement.imageAsset = undefined;
        } else if (imgElement.imageAsset) {
            viewElement.feed = imgElement.feed = undefined;
        }

        viewElement.imageUrl = getImageUrl(viewElement.__data);
    }

    const textPropertyChangedMap: { [key in keyof ITextElementCharacterProperties]?: boolean } = {};
    if (isTextNode(viewElement) && isTextNode(element)) {
        const previousProps = {};
        const dynamicProperties: (keyof ITextElementCharacterProperties)[] = ['textColor'];
        for (const prop of dynamicProperties) {
            previousProps[prop] = viewElement[prop];
        }

        viewElement.content.spans.forEach(span => {
            if (
                span.type === SpanType.Word ||
                span.type === SpanType.Composition ||
                span.type === SpanType.Space ||
                span.type === SpanType.Variable ||
                span.type === SpanType.Newline
            ) {
                if (span.style.textShadows) {
                    span.style.textShadows = span.style.textShadows.map(shadow =>
                        getTextShadowPropertyValue(shadow, 1)
                    );
                }
            }
        });

        viewElement.textColor = getViewElementPropertyValue('textColor', element, state);
        viewElement.textShadows = getViewElementPropertyValue('textShadows', element, state, 1);
        viewElement.underline = getViewElementPropertyValue('underline', element, state);
        viewElement.strikethrough = getViewElementPropertyValue('strikethrough', element, state);
        viewElement.uppercase = getViewElementPropertyValue('uppercase', element, state);
        viewElement.textOverflow = getViewElementPropertyValue('textOverflow', element, state);
        viewElement.lineHeight = getViewElementPropertyValue('lineHeight', element, state);
        viewElement.fontSize = getViewElementPropertyValue('fontSize', element, state, 1);
        viewElement.maxRows = getViewElementPropertyValue('maxRows', element, state);
        viewElement.padding = getViewElementPropertyValue('padding', element, state, 1);
        viewElement.characterSpacing = getViewElementPropertyValue('characterSpacing', element, state);
        viewElement.horizontalAlignment = getViewElementPropertyValue(
            'horizontalAlignment',
            element,
            state
        );
        viewElement.verticalAlignment = getViewElementPropertyValue(
            'verticalAlignment',
            element,
            state
        );

        const dirty = (element as OneOfTextDataNodes).__dirtyContent;
        const fontSize = dirty?.style.fontSize ?? viewElement.fontSize;
        const textShadows = dirty?.style.textShadows ?? viewElement.textShadows;
        const padding = dirty?.style.padding ?? viewElement.padding;
        viewElement.__richTextRenderer?.setStyle_m(
            'textShadows',
            textShadows,
            viewElement.__richTextRenderer.style
        );
        viewElement.__richTextRenderer?.setStyle_m(
            'fontSize',
            fontSize,
            viewElement.__richTextRenderer.style
        );
        viewElement.__richTextRenderer?.setStyle_m(
            'padding',
            padding,
            viewElement.__richTextRenderer.style
        );

        for (const prop of dynamicProperties) {
            textPropertyChangedMap[prop] = !deepEqual(viewElement[prop], previousProps[prop]);
        }
    }

    if (isVideoNode(viewElement) && viewElement.__videoRenderer) {
        viewElement.__videoRenderer.updateVideo();
    }

    if (isWidget && isWidgetNode(viewElement)) {
        element.__widget?.emit(InternalWidgetEvent.ViewNodeChanged, viewElement);
    }
}

function getImageUrl(element: IImageElementDataNode): string | undefined {
    const imageAsset = element.imageAsset;
    const url = imageAsset?.url;

    if (!isUrl(url)) {
        return '';
    }

    return url;
}
