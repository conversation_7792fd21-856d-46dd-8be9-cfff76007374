import { sandboxOrigins } from '@shared/server/environment';
import { defaultOptions } from './options.dev';
import { IDataModelTestingServiceOptions } from './options.types';

export const sandboxOptions: IDataModelTestingServiceOptions = {
    ...defaultOptions,
    origins: sandboxOrigins,
    serviceHostName: 'https://sandbox-api.bannerflow.com/data-model-testing', // host name needs to include the protocol
    inProduction: false,
    inSandbox: true,
    stage: 'sandbox',
    azureStorage: {
        ...defaultOptions.azureStorage,
        accountName: process.env.AZ_STORAGE_NAME || 'bfstudiosandbox',
        origin: process.env.AZ_STORAGE_ORIGIN || 'https://bfstudiosandbox.blob.core.windows.net'
    },
    auth0: {
        ...defaultOptions.auth0,
        uri: process.env.AUTH0_DOMAIN || 'https://sandbox-login.bannerflow.com'
    },
    azure: {
        keyVaultUrl: 'https://bf-shared-sandbox-kv.vault.azure.net',
        secrets: {
            'auth0.clientId': 'data-model-testing-auth0-client-id',
            'auth0.clientSecret': 'data-model-testing-auth0-client-secret'
        }
    },
    unleash: {
        ...defaultOptions.unleash,
        enabled: false
    },
    tokenCache: {
        ...defaultOptions.tokenCache,
        redis: {
            ...defaultOptions.tokenCache.redis,
            enabled: true,
            host:
                process.env.CLIENT_CREDENTIALS_REDIS_HOST ||
                'bf-identity-sandbox.redis.cache.windows.net',
            port: 6380,
            password: process.env.CLIENTCREDENTIALS__REDIS__KEY
        }
    }
};
