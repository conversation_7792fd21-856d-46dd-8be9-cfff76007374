import { productionOrigins } from '@shared/server/environment';
import { defaultOptions } from './options.dev';
import { IDataModelTestingServiceOptions } from './options.types';

export const productionOptions: IDataModelTestingServiceOptions = {
    ...defaultOptions,
    origins: productionOrigins,
    serviceHostName: 'https://api.bannerflow.com/data-model-testing', // host name needs to include the protocol
    inProduction: true,
    inSandbox: false,
    stage: 'production',
    azureStorage: {
        ...defaultOptions.azureStorage,
        accountName: process.env.AZ_STORAGE_NAME || 'bfstudio',
        origin: process.env.AZ_STORAGE_ORIGIN || 'https://bfstudio.blob.core.windows.net'
    },
    auth0: {
        ...defaultOptions.auth0,
        uri: process.env.AUTH0_DOMAIN || 'https://login.bannerflow.com'
    },
    azure: {
        keyVaultUrl: 'https://bf-shared-kv.vault.azure.net',
        secrets: {
            'auth0.clientId': 'data-model-testing-auth0-client-id',
            'auth0.clientSecret': 'data-model-testing-auth0-client-secret'
        }
    },
    tokenCache: {
        ...defaultOptions.tokenCache,
        redis: {
            ...defaultOptions.tokenCache.redis,
            enabled: true,
            host:
                process.env.CLIENT_CREDENTIALS_REDIS_HOST ||
                'bf-identity-westeurope.westeurope.redisenterprise.cache.azure.net',
            port: 10000,
            password: process.env.CLIENTCREDENTIALS__REDIS__KEY
        }
    },
    unleash: {
        ...defaultOptions.unleash,
        enabled: false
    }
};
