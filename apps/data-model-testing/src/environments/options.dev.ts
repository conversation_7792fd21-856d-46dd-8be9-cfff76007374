import { Logger } from '@nestjs/common';
import { devOrigins } from '@shared/server/environment';
import { build } from './build-info';
import { IDataModelTestingServiceOptions } from './options.types';

export const defaultOptions: IDataModelTestingServiceOptions = {
    serviceHostName: 'http://api.bannerflow.local/data-model-testing', // host name needs to include the protocol
    build,
    inProduction: false,
    inSandbox: false,
    stage: 'local',
    port: getPort(),
    origins: devOrigins,
    azureStorage: {
        accountName: 'devstoreaccount1',
        accountAccessKey:
            'Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==',
        origin: 'http://storage-emulator:10000/devstoreaccount1'
    },
    unleash: {
        enabled: false,
        url: 'https://bf-feature-flags.azurewebsites.net/api',
        auth: 'default:development.unleash-insecure-api-token'
    },
    tokenCache: {
        cacheKeyPrefix: 'data-model-testing',
        redis: {
            enabled: false
        }
    },
    auth0: {
        uri: process.env.AUTH0_URI || 'https://local-login.bannerflow.com',
        audience: process.env.AUTH0_AUDIENCE || 'https://bannerflow.com/resources/',
        clientId: process.env.AUTH0_CLIENT_ID || 'ObmQrgkzVe8W9gfBshBjlKFcTbZQxeo8',
        clientSecret:
            process.env.AUTH0_CLIENT_SECRET ||
            '4pZmwHaorLdz75lFw5_Y4k7vzXOoeISz4bTMNNpUnRA1ZxUsJx7zIP-VZf4JEOtp'
    },
    azure: {
        keyVaultUrl: 'https://bf-shared-sandbox-kv.vault.azure.net'
    }
};

function getPort(): number {
    const defaultPort = 80;
    try {
        if (process.env.PORT) {
            const customPort = parseInt(process.env.PORT, 10);
            if (!isNaN(customPort)) {
                return customPort;
            }
            Logger.warn(`Parsed PORT="${process.env.PORT}" was NaN. Defaulting to ${defaultPort}.`);
        }
    } catch {
        Logger.error(`Error parsing PORT="${process.env.PORT}"`);
    }

    return defaultPort;
}
