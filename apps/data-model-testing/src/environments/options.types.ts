import { IBuildInfo } from '@domain/environment';
import {
    IAuth0ClientOptions,
    IAzureBlobServiceOptions,
    IAzureOptions,
    ITokenCacheOptions,
    ServerStage
} from '@domain/server';

export interface IDataModelTestingServiceOptions {
    serviceHostName: string;
    stage: ServerStage;
    port: number;
    build: IBuildInfo;
    inProduction: boolean;
    inSandbox: boolean;
    azureStorage: IAzureBlobServiceOptions;
    azure: IAzureOptions;
    auth0: IAuth0ClientOptions;
    unleash: {
        enabled: boolean;
        url: string;
        auth: string;
    };
    origins: {
        designApi: string;
    };
    tokenCache: ITokenCacheOptions;
}
