import { Logger } from '@nestjs/common';
import { defaultOptions } from './options.dev';
import { productionOptions } from './options.production';
import { sandboxOptions } from './options.sandbox';
import { IDataModelTestingServiceOptions } from './options.types';

function getOptions(): IDataModelTestingServiceOptions {
    if (process.env.STAGE === 'production') {
        Logger.log('Using production config', 'Configuration');
        return productionOptions;
    }
    if (process.env.STAGE === 'sandbox') {
        Logger.log('Using sandbox config', 'Configuration');
        return sandboxOptions;
    }

    Logger.log('Using dev config', 'Configuration');
    return defaultOptions;
}

export default (): IDataModelTestingServiceOptions => {
    const options = getOptions();
    Logger.debug(options, 'Configuration');
    return options;
};
