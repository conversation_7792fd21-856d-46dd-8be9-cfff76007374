{"name": "data-model-testing", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/data-model-testing/src", "projectType": "application", "tags": ["type:app", "scope:server", "scope:data-model-testing"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/data-model-testing", "main": "apps/data-model-testing/src/main.ts", "tsConfig": "apps/data-model-testing/tsconfig.app.json", "assets": ["apps/data-model-testing/src/assets"], "webpackConfig": "apps/data-model-testing/webpack.config.js", "generatePackageJson": true}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "data-model-testing:build"}, "configurations": {"development": {"inspect": true, "buildTarget": "data-model-testing:build:development"}, "production": {"buildTarget": "data-model-testing:build:production"}}}, "generate-build-info": {"executor": "nx:run-commands", "options": {"commands": ["pnpm exec nx g @studio/tools:build-info data-model-testing --branch={args.branch}"]}}, "docker-prepare": {"dependsOn": ["build-ad", {"target": "generate-build-info", "params": "forward"}, {"target": "build"}], "command": "echo Generate Info and Build Data Model Testing Service Success"}, "docker-build": {"dependsOn": [{"target": "docker-prepare", "params": "forward"}], "command": "docker build -f apps/data-model-testing/Dockerfile . -t bannerflow.azurecr.io/studio/data-model-testing:local"}}}