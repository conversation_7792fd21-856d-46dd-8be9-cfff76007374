env:
    ENVIRONMENT: Production
    STAGE: production
    LOG_LEVEL: info
    PORT: 8080
    OTEL_EXPORTER_OTLP_ENDPOINT: 'https://otlp.eu01.nr-data.net'
    OTEL_SERVICE_NAME: 'data-model-testing'
    OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT: 4095
    OTEL_EXPORTER_OTLP_COMPRESSION: gzip
    OTEL_EXPORTER_OTLP_PROTOCOL: http/protobuf
    OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE: delta

ingressnginx:
    enabled: true
    host: api.bannerflow.com
    paths:
        - path: /data-model-testing/(.*)
          pathType: ImplementationSpecific

networkpolicy:
    enabled: true
    policyname: allow-incoming
    cidr: *********/19 #Always static cidr, belongs to Azure app gateway subnet
    nginxcidr: *********/19 #Always static cidr, belongs to Azure vnet backend subnet
    accessvianamespaces: true
    namespaces:
        - studio #Allow SAPI to access data-model-testing within the cluster

serviceAccount:
    create: true
    disableAutomountTokens: 'true'

service:
    type: ClusterIP
    port: 8080

labels:
    enabled: true
    label:
        azure.workload.identity/use: 'true'

autoscaling:
    enabled: true
    minReplicas: 1
    maxReplicas: 4
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80

nodeSelector:
    scaling: static

securityContext:
    runAsUser: 1001
    runAsGroup: 1001
    allowPrivilegeEscalation: false
    privileged: false

resources:
    requests:
        cpu: 200m
        memory: 300Mi
    limits:
        cpu: 500m
        memory: 800Mi
