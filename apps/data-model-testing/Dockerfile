# This file is generated by Nx.
#
# Build the docker image with `pnpm exec nx docker-build data-model-testing`.
# Tip: Modify "docker-build" options in project.json to change docker build args.
#
# Run the container with `docker run -p 3006:3006 -t data-model-testing`.
FROM docker.io/node:lts-alpine

WORKDIR /app

RUN addgroup -g 1001 appuser && \
    adduser -D -u 1001 -G appuser appuser

RUN chown -R appuser:appuser /app

COPY dist/apps/data-model-testing data-model-testing

# Set npm authentication for private registry
COPY .npmrc data-model-testing

# You can remove this install step if you build with `--bundle` option.
# The bundled output will include external dependencies.
RUN npm --prefix data-model-testing --omit=dev -f install --ignore-scripts

# Switch to the appuser
EXPOSE 8080
USER appuser:appuser

CMD ["node", "--enable-source-maps", "data-model-testing"]
