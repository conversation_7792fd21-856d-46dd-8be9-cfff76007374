import scanList from '../assets/scan-list.json';

const useLocalPreview = false;
const creativePreviewUrl = useLocalPreview
    ? 'http://api.bannerflow.local/preview/preview'
    : 'https://sandbox-api.bannerflow.com/preview/preview';

export function getScanList(): { name: string; url: string }[] {
    return prepareList(scanList);
}

function prepareList(blob: Record<string, string>): { name: string; url: string }[] {
    return Object.entries(blob).map(([name, url]) => ({ name, url: prepareUrl(url) }));
}

function prepareUrl(url: string): string {
    const isDataUrl = url.includes('data-url');
    if (isDataUrl) {
        return `${creativePreviewUrl}${url}`;
    }
    return url;
}
