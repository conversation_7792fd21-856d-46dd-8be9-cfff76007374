import axios from 'axios';

describe('GET /health/live', () => {
    it('should return a message', async () => {
        const res = await axios.get(`/health/live`);
        expect(res.status).toBe(200);
    });
});

describe('GET /scan-ads', () => {
    const scanList = global.scanList;
    for (const { name, url } of scanList) {
        it(`should scan ${name} - ${url}`, async () => {
            const res = await axios.get(`/scan?url=${encodeURIComponent(url)}`);

            expect(res.status).toBe(200);
            expect(res.data.message).toEqual('Scan successful');
        });
    }
});
