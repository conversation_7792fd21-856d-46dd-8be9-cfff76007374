{"name": "creative-generation-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/creative-generation-e2e/e2e", "projectType": "application", "targets": {"e2e": {"executor": "@nx/playwright:playwright", "options": {"config": "{projectRoot}/playwright.config.ts"}}, "open": {"executor": "@nx/playwright:playwright", "options": {"ui": true, "headed": true, "config": "{projectRoot}/playwright.config.ts"}}}}