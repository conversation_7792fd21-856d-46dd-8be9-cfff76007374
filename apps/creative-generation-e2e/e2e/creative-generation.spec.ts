import { expect, test } from '@playwright/test';
import { LoginPage } from './LoginPage';
import { ManageView } from './ManageView';
import { TranslationPage } from './TranslationPage';

const BRAND_ID = '6194f0d1515f4735ce1a7266';
const CREATIVESET_ID = '12469';
const CREATIVE_ID = '122442';
const CREATIVE_ACCESS_TOKEN = '820d969e33c0795119a5d6a49275d8660bd2ca4081d598808cae9a3bb78965b9';

test.describe.configure({ mode: 'serial' });

test.slow(); // slow test will be given triple the default timeout

const testTimestamp = Date.now().toString();
console.log(`test timestamp: ${testTimestamp}`);

test('push creative changes', async ({ page }) => {
    const creativesetUrl = `https://sandbox-studio.bannerflow.com/brand/${BRAND_ID}/creativeset/${CREATIVESET_ID}/translate`;
    console.log('Creativeset:', creativesetUrl);
    await page.goto(creativesetUrl);

    await page.waitForURL('https://sandbox-login.bannerflow.com/**');
    await LoginPage.loginWithNewEmailAndPassword(page);

    const redirectedUrl = page.url();
    console.log('redirected URL:', redirectedUrl);

    // If the URL loses the path, re-navigate to the intended URL
    // eslint-disable-next-line playwright/no-conditional-in-test
    if (!redirectedUrl.includes(`/creativeset/${CREATIVESET_ID}/translate`)) {
        await page.goto(creativesetUrl);
    }

    await TranslationPage.changeTextInTextElement(page, testTimestamp);
    await TranslationPage.goToManageView(page);

    await ManageView.pushChanges(page, BRAND_ID, CREATIVESET_ID);
    await ManageView.awaitPushChanges(page);
});

test(`open creative preview and verify that it renders without errors`, async ({ page }) => {
    page.on('console', msg => {
        if (msg.type() === 'error') {
            console.log(msg.text());
            expect(msg.text()).toEqual('');
        }
    });

    const previewUrl = `https://sandbox-api.bannerflow.com/preview/${CREATIVESET_ID}/${CREATIVE_ID}/preview?access-token=${CREATIVE_ACCESS_TOKEN}&cache=off`;
    console.log(`Preview: ${previewUrl}`);
    await page.goto(previewUrl);

    await page.locator('script[data-rendered="true"]').waitFor({ state: 'attached' });

    const textElement = page
        .frameLocator('#creative iframe')
        .locator('span[data-test-id="rich-text-span-text"]')
        .first();

    await expect(textElement).toHaveCount(1);
    const elementTimestamp = await textElement.innerText();
    console.log('preview - element timestamp: ', elementTimestamp);
    // rich-text adds zero width characters which breaks the check otherwise
    await expect(textElement).toContainText(testTimestamp);
});

test('open the published ad in webpage and verify it renders without errors', async ({ page }) => {
    page.on('console', msg => {
        if (msg.type() === 'error') {
            console.log(msg.text());
            expect(msg.text()).toEqual('');
        }
    });

    const adHostUrl =
        'https://bfstudiosandbox.blob.core.windows.net/static-assets/test/e2e/creative-generation.html';
    console.log(`adHostUrl url: ${adHostUrl}`);
    await page.goto(adHostUrl);
    const bodyElement = page.locator('body');
    expect(bodyElement).toBeTruthy();

    await page.locator('script[data-rendered="true"]').waitFor({ state: 'attached' });

    const textElement = page
        .frameLocator('iframe')
        .first() // lets grab the first iframe in the ad
        .locator('span[data-test-id="rich-text-span-text"]')
        .first();

    await expect(textElement).toHaveCount(1);
    const elementTimestamp = await textElement.innerText();
    console.log('ad - element timestamp: ', elementTimestamp);
    await expect(textElement).toContainText(testTimestamp);
});
