import { expect, Page } from '@playwright/test';

export class ManageView {
    static async pushChanges(page: Page, brandId: string, creativesetId: string): Promise<void> {
        const creativesetUrl = `https://sandbox-studio.bannerflow.com/brand/${brandId}/creativeset/${creativesetId}`;

        // Check if the current URL matches the manage view URL
        if (!page.url().startsWith(creativesetUrl)) {
            await page.goto(creativesetUrl);
        }

        const pushButton = page.locator('ui-button[text="PUSH CHANGES"]');
        const confirmDialog = page.locator('#push-changes-dialog-confirm');

        await pushButton.click();
        await confirmDialog.click();
    }

    static async awaitPushChanges(page: Page): Promise<void> {
        const campaignEl = page.locator('#interaction-topbar-open-campaigns');
        await campaignEl.waitFor({ state: 'visible' });
        await expect(campaignEl).toContainText(/view campaigns/i);
    }
}
