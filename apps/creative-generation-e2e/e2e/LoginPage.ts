export class LoginPage {
    static async loginWithNewEmailAndPassword(
        page,
        email = '<EMAIL>',
        password = process.env.TEST_SANDBOX_USER_PASSWORD
    ): Promise<void> {
        if (!password) {
            throw new Error('TEST_SANDBOX_USER_PASSWORD environment variable is not set');
        }
        await page.fill('input[id="username"]', email);
        await page.locator('button[name="action"]').click();
        await page.fill('input[id="password"]', password);
        await page
            .locator('button[data-action-button-primary="true"]')
            .filter({ hasText: 'Continue' })
            .click();
        await page.waitForURL(`https://**studio.bannerflow.com/**`);
    }

    static loginWithNewEmailAndPasswordProd(page): Promise<void> {
        if (!process.env.TEST_PROD_USER_PASSWORD) {
            throw new Error('TEST_PROD_USER_PASSWORD environment variable is not set');
        }
        return LoginPage.loginWithNewEmailAndPassword(
            page,
            '<EMAIL>',
            process.env.TEST_PROD_USER_PASSWORD
        );
    }
}
