import { expect, test } from '@playwright/test';
import { LoginPage } from './LoginPage';
import { ManageView } from './ManageView';

const BRAND_ID = '642e9c43cf5ac3fbe4a15656';
const CREATIVESET_ID = '705511';

test.describe.configure({ mode: 'serial' });
test.slow(); // slow test will be given triple the default timeout

const testTimestamp = Date.now().toString();

test.skip('push creative changes', async ({ page }) => {
    const creativesetUrl = `https://studio.bannerflow.com/brand/${BRAND_ID}/creativeset/${CREATIVESET_ID}`;
    console.log('Creativeset:', creativesetUrl);
    await page.goto(creativesetUrl);

    await page.waitForURL('https://login.bannerflow.com/**');
    await LoginPage.loginWithNewEmailAndPasswordProd(page);

    const redirectedUrl = page.url();

    // If the URL loses the path, re-navigate to the intended URL
    // eslint-disable-next-line playwright/no-conditional-in-test
    if (!redirectedUrl.includes(`/creativeset/${CREATIVESET_ID}`)) {
        await page.goto(
            `https://studio.bannerflow.com/brand/${BRAND_ID}/creativeset/${CREATIVESET_ID}`
        );
    }

    await ManageView.changeTextInTextElement(page, testTimestamp);
    await ManageView.pushChanges(page);

    // Wait until pushing changes is done
    const campaignEl = page.locator('#interaction-topbar-open-campaigns');
    await campaignEl.waitFor({ state: 'visible' });
    await expect(campaignEl).toContainText('VIEW CAMPAIGNS (1)');
});

test.skip('open the published ad in webpage and verify it streams without errors', async ({ page }) => {
    page.on('console', msg => {
        if (msg.type() === 'error') {
            console.log(msg.text());
            expect(msg.text()).toEqual('');
        }
    });
    const manifestUrlPattern = /.*\.mpd$/; // manifest format (e.g., DASH)
    const chunkUrlPattern = /.*\.(ts|m4s)$/; // Match chunk URLs (e.g., HLS or DASH)

    let manifestLoaded = false;
    let chunkCount = 0;
    const adHostUrl =
        'https://bfstudiosandbox.blob.core.windows.net/static-assets/test/e2e/streaming.html';
    console.log(`adHostUrl: ${adHostUrl}`);

    // Intercept network requests
    await page.route('**/*', (route, request) => {
        if (manifestUrlPattern.test(request.url())) {
            manifestLoaded = true;
        } else if (chunkUrlPattern.test(request.url())) {
            chunkCount++;
        }
        route.continue();
    });

    await page.goto(adHostUrl);

    const bodyElement = page.locator('body');
    expect(bodyElement).toBeTruthy();

    await page.locator('script[data-rendered="true"]').waitFor({ state: 'attached' });

    // Wait for the manifest to be requested (optional if loading is async)
    await page.waitForTimeout(5000); // Adjust the timeout to match loading behavior

    // Assert that the manifest was loaded
    expect(manifestLoaded).toBe(true);
    console.log(`Number of chunks loaded: ${chunkCount}`);
    expect(chunkCount).toBe(18);
});
