import { expect, Page } from '@playwright/test';

export class TranslationPage {
    static async changeTextInTextElement(page: Page, text: string): Promise<void> {
        const textArea = page.locator('textarea#mat-input-0').first();

        // Translation Panel flakiness, .fill() should be the way
        await textArea.clear();
        await textArea.pressSequentially(text);

        const saveButton = page.locator('#save-button');
        await saveButton.click();

        await page.waitForResponse(
            resp => resp.url().includes('/studio/api/graphql') && resp.status() === 200
        );

        const textElement = page.locator('span[data-test-id="rich-text-span-text"]').first();
        await expect(textElement).toContainText(text);
    }

    static goToManageView(page: Page): Promise<void> {
        return page.locator('close-button').click();
    }
}
