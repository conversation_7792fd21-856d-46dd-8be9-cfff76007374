const { FlatCompat } = require('@eslint/eslintrc');
const baseConfig = require('../../eslint.config.js');
const js = require('@eslint/js');

const compat = new FlatCompat({
    baseDirectory: __dirname,
    recommendedConfig: js.configs.recommended
});

module.exports = [
    ...baseConfig,
    ...compat.extends('plugin:playwright/recommended'),
    {
        files: ['**/*.ts', '**/*.tsx', '**/*.js', '**/*.jsx'],
        rules: {
            'no-console': 'off'
        }
    },
    {
        files: ['**/*.ts', '**/*.tsx'],
        rules: {}
    },
    {
        files: ['**/*.js', '**/*.jsx'],
        rules: {}
    },
    {
        files: ['e2e/**/*.{ts,js,tsx,jsx}'],
        rules: {}
    }
];
