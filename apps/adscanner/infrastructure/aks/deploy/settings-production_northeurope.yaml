env:
    ENVIRONMENT: Production
    STAGE: production
    LOG_LEVEL: info
    NODE_MEMORY_LIMIT: 2048
    PORT: 8080

    OTEL_EXPORTER_OTLP_ENDPOINT: 'https://otlp.eu01.nr-data.net'
    OTEL_SERVICE_NAME: 'AdScanner'
    OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT: 4095
    OTEL_EXPORTER_OTLP_COMPRESSION: gzip
    OTEL_EXPORTER_OTLP_PROTOCOL: http/protobuf
    OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE: delta

ingressnginx:
    enabled: true
    host: api.bannerflow.com
    paths:
        - path: /ass/(.*)
          pathType: ImplementationSpecific

networkpolicy:
    enabled: true
    policyname: allow-incoming
    cidr: *********/19 #Always static cidr, belongs to Azure app gateway subnet
    nginxcidr: *********/19 #Always static cidr, belongs to Azure vnet backend subnet
    accessvianamespaces: true
    namespaces:
        - studio #Allow SAPI to access ASS within the cluster

autoscaling:
    enabled: true
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
    minReplicas: 2
    maxReplicas: 4

serviceAccount:
    create: true
    disableAutomountTokens: 'true'

service:
    type: ClusterIP
    port: 8080

labels:
    enabled: true
    label:
        azure.workload.identity/use: 'true'

nodeSelector:
    scaling: static

securityContext:
    runAsNonRoot: true
    runAsUser: 1001
    runAsGroup: 1001
    allowPrivilegeEscalation: false
    privileged: false

resources:
    requests:
        cpu: 100m
        memory: 200Mi
    limits:
        cpu: 400m
        memory: 500Mi
