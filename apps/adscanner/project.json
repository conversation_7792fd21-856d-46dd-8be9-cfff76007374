{"name": "adscanner", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/adscanner/src", "projectType": "application", "tags": ["type:app", "scope:server", "scope:adscanner"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/adscanner", "main": "apps/adscanner/src/main.ts", "tsConfig": "apps/adscanner/tsconfig.app.json", "assets": ["apps/adscanner/src/assets"], "generatePackageJson": true, "webpackConfig": "apps/adscanner/webpack.config.js"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "adscanner:build", "inspect": true, "port": 9333}, "configurations": {"development": {"buildTarget": "adscanner:build:development"}, "production": {"buildTarget": "adscanner:build:production"}}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/adscanner/jest.config.ts"}}, "docker-build": {"dependsOn": ["build"], "command": "docker build -f apps/adscanner/Dockerfile . -t bannerflow.azurecr.io/studio/adscanner:latest"}, "docker-push": {"dependsOn": ["docker-build"], "command": "docker push bannerflow.azurecr.io/studio/adscanner:latest"}}}