# AdScanner

This NestJS service is responsible for testing and recording the performance of Bannerflow ad scripts.

It utilizes Google lighthouse to gather measurements and reports the key metrics to Application Insights. Furthermore,
it will archive reports on Azure blob storage.

## Hard Dependencies

-   No hard dependencies as of now.

## Feature Dependencies

-   Azure Blob Storage
-   Azure Application Insights

## Local Setup

The project is setup with NX.

This allows us to run the project using the following commands:

-   `nx serve adscanner` - by using NX from wherever you want
-   `nx run adscanner:serve` - by using NX run with target
-   `node ./dist/apps/adscanner/main.js` - by using NodeJS directly, requires build first to generate the files in the
    dist folder

### Try a scan

Perform a scan of a provided creative preview url.
`http://localhost:3005/scan?url=<creative-preview-url>`

Results are stored in a mongo collection and reports archived at `{storage}/scans/{creativeset}/{creative}`.

### Configuration

First you need to create a `.env` file in project root. Simply copy `dev.env` and fill in the secrets.

### Run

`nx serve adscanner`

### Docker Run

`docker run -p 3005:3005 -t adscanner`

### Build

`nx build adscanner`

### Docker Image

`nx docker-build adscanner` to build the docker image

`nx docker-push adscanner` to push the latest docker image to the registry.
Executes - `docker push bannerflow.azurecr.io/studio/adscanner` under the hood.

### Test

`nx test adscanner`

### E2E Test

`nx e2e adscanner-e2e`

## Infrastructure / Deployment

-   [App Service](https://portal.azure.com/#@bannerflow.onmicrosoft.com/resource/subscriptions/ef524e43-9115-45e3-9467-26b5e4b8bead/resourceGroups/bf-adscanner-rg/providers/Microsoft.Web/sites/adscanner/appServices)

To deploy, simply push the latest adscanner docker image via `nx docker-push adscanner`

## Additional Information

### ESModule

Because the latest lighthouse version is using esmodules, we still have to use the common js version of it to be able to
run it.
Until NodeJS and TypeScript support ES modules smoothly, we have to do dynamic imports like:

```typescript
const lighthouse = require('lighthouse/core/index.cjs'); // For access to just the lighthouse function in CommonJS
// instead of
import lighthouse from 'lighthouse';
```
