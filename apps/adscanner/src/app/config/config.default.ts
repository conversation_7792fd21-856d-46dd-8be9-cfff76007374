import { AdScannerConfiguration } from '@domain/adscanner';
import { ServerStage } from '@domain/server';
import { getPort } from '@shared/server/environment';

export default {
    azureStorage: {
        accountAccessKey:
            process.env.AZURE_STORAGE_ACCOUNT_ACCESS_KEY ||
            'Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==',
        accountName: process.env.AZURE_STORAGE_ACCOUNT_NAME || 'devstoreaccount1',
        origin:
            process.env.AZURE_STORAGE_ACCOUNT_ORIGIN || 'http://storage-emulator:10000/devstoreaccount1'
    },
    creativePreviewUrl: 'http://api.bannerflow.local/preview',
    database: {
        mongodbConnectionString: process.env.MONGODB_CONNECTION_STRING!
    },
    headless: true,
    lighthouseLogs: false,
    port: getPort(3005),
    psi: {
        enabled: false,
        key: process.env.PAGESPEEDINSIGHTS_KEY!
    },
    stage: (process.env.STAGE as ServerStage) || 'local',
    unleash: {
        enabled: false,
        url: 'https://bf-feature-flags.azurewebsites.net/api',
        auth: 'default:development.unleash-insecure-api-token'
    }
} satisfies AdScannerConfiguration;
