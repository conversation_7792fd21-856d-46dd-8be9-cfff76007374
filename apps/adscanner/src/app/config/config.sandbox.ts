import { AdScannerConfiguration } from '@domain/adscanner';
import configDefault from './config.default';

export default {
    ...configDefault,
    azureStorage: {
        ...configDefault.azureStorage,
        accountName: process.env.AZURE_STORAGE_ACCOUNT_NAME || 'bfstudiosandbox',
        origin:
            process.env.AZURE_STORAGE_ACCOUNT_ORIGIN || 'https://bfstudiosandbox.blob.core.windows.net'
    },
    creativePreviewUrl: 'https://sandbox-api.bannerflow.com/preview',
    psi: {
        ...configDefault.psi,
        enabled: true
    },
    unleash: {
        ...configDefault.unleash,
        enabled: false
    },
    azure: {
        appConfigUrl: 'https://bf-shared-sandbox-ac.azconfig.io',
        keyVaultUrl: 'https://bf-shared-sandbox-kv.vault.azure.net',
        secrets: {
            'database.mongodbConnectionString': 'adscanner-mongodb-connection-string',
            'psi.key': 'adscanner-pagespeedinsights-key'
        }
    }
} satisfies AdScannerConfiguration;
