import { AdScannerConfiguration } from '@domain/adscanner';
import { Logger } from '@nestjs/common';
import configDefault from './config.default';
import configProduction from './config.production';
import configSandbox from './config.sandbox';

function getConfig(): AdScannerConfiguration {
    if (process.env.STAGE === 'production') {
        Logger.log('Using production', 'Configuration');
        return configProduction;
    }
    if (process.env.STAGE === 'sandbox') {
        Logger.log('Using sandbox', 'Configuration');
        return configSandbox;
    }

    Logger.log('Using default', 'Configuration');
    return configDefault;
}

export default (): AdScannerConfiguration => {
    const config = getConfig();
    Logger.debug(config, 'Configuration');
    return config;
};
