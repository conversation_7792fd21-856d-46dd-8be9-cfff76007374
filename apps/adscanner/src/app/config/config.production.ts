import { AdScannerConfiguration } from '@domain/adscanner';
import configDefault from './config.default';

export default {
    ...configDefault,
    azureStorage: {
        ...configDefault.azureStorage,
        accountName: process.env.AZURE_STORAGE_ACCOUNT_NAME || 'bfstudio',
        origin: process.env.AZURE_STORAGE_ACCOUNT_ORIGIN || 'https://bfstudio.blob.core.windows.net'
    },
    creativePreviewUrl: 'https://api.bannerflow.com/preview',
    psi: {
        ...configDefault.psi,
        enabled: true
    },
    unleash: {
        ...configDefault.unleash,
        enabled: false
    },
    azure: {
        appConfigUrl: 'https://bf-shared-ac.azconfig.io',
        keyVaultUrl: 'https://bf-shared-kv.vault.azure.net',
        secrets: {
            'database.mongodbConnectionString': 'adscanner-mongodb-connection-string',
            'psi.key': 'adscanner-pagespeedinsights-key'
        }
    }
} satisfies AdScannerConfiguration;
