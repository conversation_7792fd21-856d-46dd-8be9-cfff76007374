import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AzureModule } from '@shared/server/azure';
import { HealthModule } from '@shared/server/health';
import { TrackingModule } from '@shared/server/monitoring';
import { AppController } from './app.controller';
import config from './config/config';
import { DatabaseModule } from './database/database.module';
import { ScanModule } from './scan/scan.module';

@Module({
    imports: [
        HttpModule,
        ConfigModule.forRoot({
            load: [config],
            isGlobal: true
        }),
        HealthModule,
        TrackingModule,
        AzureModule,
        DatabaseModule,
        ScanModule
    ],
    controllers: [AppController],
    providers: []
})
export class AppModule {}
