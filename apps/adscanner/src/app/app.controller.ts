import { AdScannerConfiguration, ScanResponse } from '@domain/adscanner';
import {
    Controller,
    Get,
    Logger,
    MethodNotAllowedException,
    Query,
    Redirect,
    Res
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Response } from 'express';
import { ScanService } from './scan/scan.service';
import { WeightServiceDto } from './scan/weight-service.adapter';
import { isCPSUrl } from './utils';

@Controller()
export class AppController {
    private logger = new Logger(AppController.name);
    private readonly CREATIVE_PREVIEW_URL: string;

    constructor(
        private readonly configService: ConfigService<AdScannerConfiguration, true>,
        private readonly scanService: ScanService
    ) {
        this.CREATIVE_PREVIEW_URL = this.configService.get('creativePreviewUrl');
    }

    @Get('/')
    @Redirect('/health/live', 301)
    baseRoute(): void {
        return undefined;
    }

    /**
     * Perform a scan of provided creative preview url.
     * Results are logged to AI and reports archived at `{storage}/scans/creativeset/creative`.
     */
    @Get('/scan')
    async scanUrl(
        @Res({ passthrough: true }) res: Response,
        @Query('url') url: string,
        @Query('html') htmlReport?: string,
        @Query('checksum') checksum?: string,
        @Query('sapi') sapi?: string,
        @Query('runs') runs?: number
    ): Promise<ScanResponse | WeightServiceDto | string> {
        const scanUrl = this.prepareUrl(url);
        const usePSI = this.configService.get('psi.enabled', { infer: true });

        if (htmlReport) {
            if (usePSI) {
                throw new MethodNotAllowedException('HTML output is not supported in PSI');
            }
            const data = await this.scanService.getHtmlReport(scanUrl);
            if (!data) {
                return 'Something went wrong';
            }
            res.type('html');
            return data;
        }

        if (sapi) {
            return await this.scanService.getReportForSapi(scanUrl);
        }

        const data = await this.scanService.getReport(scanUrl, checksum, runs);

        return {
            message: data ? 'Scan successful' : 'Something went wrong',
            data
        };
    }

    private prepareUrl(url: string): string {
        if (!isCPSUrl(url)) {
            this.logger.verbose('Detected non-CPS url. Skipping adding parameters');
            return url;
        }
        // for relative urls like "?brandId=xxx&data-url=https://api.bannerflow.com/preview/1/1/data.js"
        if (url.startsWith('?')) {
            url = `${this.CREATIVE_PREVIEW_URL}${url}`;
        }
        return `${url}&font-loading=true&iframeless=true`;
    }
}
