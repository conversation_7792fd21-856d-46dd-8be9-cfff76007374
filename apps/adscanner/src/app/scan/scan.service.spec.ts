import { createMock } from '@golevelup/ts-jest';
import { Test, TestingModule } from '@nestjs/testing';
import { AzureBlobService } from '@shared/server/azure';
import * as lighthouseAuditsMock from '@mocks/adscanner/audits.mock.json';
import { adScanDataMock } from '@mocks/adscanner/ad-scan-data.mock';
import { ScanService } from './scan.service';
import { ScanDataService } from './scan-data.service';
import { LighthouseService } from '../lighthouse/lighthouse.service';
import { AdScanDto } from '@domain/adscanner/data';
import { RunnerResult } from 'lighthouse';
import { ScanHttpService } from './scan-http.service';
import { IAdDataCreative } from '@domain/ad/ad-data-creative';
import { getMedianLighthouseResult } from '../lighthouse/median-run';
import * as LH from 'lighthouse/types/lh';

jest.mock('../lighthouse/median-run', () => ({
    getMedianLighthouseResult: jest.fn()
}));

describe('ScanService', () => {
    let service: ScanService;
    let scanDataService: ScanDataService;
    let lighthouseService: LighthouseService;
    let scanHttpService: ScanHttpService;

    beforeEach(async () => {
        const azureBlobServiceMock = createMock<AzureBlobService>();
        const scanDataServiceMock = createMock<ScanDataService>();
        const lightHouseServiceMock = createMock<LighthouseService>();
        const scanHttpServiceMock = createMock<LighthouseService>();

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                ScanService,
                { provide: AzureBlobService, useValue: azureBlobServiceMock },
                { provide: ScanDataService, useValue: scanDataServiceMock },
                { provide: LighthouseService, useValue: lightHouseServiceMock },
                { provide: ScanHttpService, useValue: scanHttpServiceMock }
            ]
        }).compile();

        service = module.get<ScanService>(ScanService);
        scanDataService = module.get<ScanDataService>(ScanDataService);
        lighthouseService = module.get<LighthouseService>(LighthouseService);
        scanHttpService = module.get<ScanHttpService>(ScanHttpService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('getReport', () => {
        const creativeChecksum = 'a1b2c3';

        const adScanDtoMock: AdScanDto = {
            ...adScanDataMock,
            metadata: {
                creativeset: '12345',
                creative: '67890',
                creativeChecksum
            },
            timestamp: new Date()
        };

        describe('Creative checksum provided', () => {
            describe('scan exists in database ', () => {
                test('return scan from database', async () => {
                    jest.spyOn(scanDataService, 'getOneByChecksum').mockResolvedValue(adScanDtoMock);

                    const result = await service.getReport('url', creativeChecksum);
                    expect(result).toEqual(adScanDataMock);
                    expect(lighthouseService.getScanResult).not.toHaveBeenCalled();
                });
            });
            describe('scan DOES NOT exist in database', () => {
                test(' scan creative with lighthouse and return processed result', async () => {
                    jest.spyOn(scanDataService, 'getOneByChecksum').mockResolvedValue(undefined);
                    const cpsURL =
                        'https://sandbox-api.bannerflow.com/preview/1/2/preview?access-token=TheToken';
                    const lhrResult = { finalDisplayedUrl: cpsURL } as unknown as RunnerResult;
                    (getMedianLighthouseResult as jest.Mock).mockReturnValue(lhrResult);

                    jest.spyOn(lighthouseService, 'getScanResult').mockResolvedValue(lhrResult);
                    jest.spyOn(service, 'processReport').mockResolvedValue(adScanDataMock);

                    const result = await service.getReport(cpsURL, creativeChecksum);

                    expect(lighthouseService.getScanResult).toHaveBeenCalled();
                    expect(result).toEqual(adScanDataMock);
                });
            });
        });

        describe('Metadata parameter provided in the URL', () => {
            it('should extract creativeId and creativesetId from metadata', async () => {
                const cpsURL =
                    'https://sandbox-api.bannerflow.com/preview/preview?access-token=21c796cb6cd97b8aa4fb2dc88aae645cd4bb7487804fd3562937cba4c9c41396&metadata=https%3a%2f%2fbfstudiosandbox.blob.core.windows.net%2faccounts%2fmateusz%2f620f5b6835603709fd2b4435%2fpublished%2f134644%2f89005%2fmetadata.0000007985934C.json&preload=on' +
                    '';

                const metadata = {
                    creativeset: {
                        id: 'creativesetId',
                        name: 'creativeset name'
                    },
                    id: 'creativeId'
                } as IAdDataCreative;

                const lhrResult = { finalDisplayedUrl: cpsURL } as unknown as RunnerResult;
                (getMedianLighthouseResult as jest.Mock).mockReturnValue(lhrResult);
                jest.spyOn(scanDataService, 'getOneByChecksum').mockResolvedValue(undefined);
                jest.spyOn(scanHttpService, 'getCreativeDataFromBlob').mockResolvedValue(metadata);
                jest.spyOn(lighthouseService, 'getScanResult').mockResolvedValue(lhrResult);
                jest.spyOn(service, 'processReport').mockResolvedValue(adScanDataMock);

                await service.getReport(cpsURL);

                expect(service.processReport).toHaveBeenCalledWith(
                    lhrResult,
                    'creativesetId',
                    'creativeId',
                    undefined
                ); // first undefined is lhr result, second is checksum
            });
        });

        describe('Ad preview URL provided', () => {
            it('should return scan data without saving results to DB and blob storage', async () => {
                const cpsURL =
                    'https://api.bannerflow.com/preview/ad?url=https%3A%2F%2Fc.sandbox-bannerflow.net%2Fa%2F67e517cd624482a168411099%3Fdid%3D5d4d785bfc8b610001106cc3%26deeplink%3Don%26responsive%3Doff%26preload%3Doff%26redirecturl%3D';

                const lhrResult = {
                    finalDisplayedUrl: cpsURL,
                    audits: lighthouseAuditsMock,
                    timing: { total: 1000, entries: [] },
                    environment: {
                        benchmarkIndex: 100,
                        hostUserAgent: 'test agent',
                        networkUserAgent: 'test agent'
                    },
                    runWarnings: []
                } as unknown as LH.Result;

                (getMedianLighthouseResult as jest.Mock).mockReturnValue(lhrResult);
                jest.spyOn(lighthouseService, 'getScanResult').mockResolvedValue({
                    lhr: lhrResult
                } as unknown as RunnerResult);

                jest.spyOn(service, 'processReport');

                const result = await service.getReport(cpsURL);

                expect(service.processReport).not.toHaveBeenCalled();
                expect(result).toHaveProperty('url', cpsURL);
                expect(result).toHaveProperty('duration', 1000);
                expect(result).toHaveProperty('environment');
            });
        });
    });

    describe('metrics - weights', () => {
        it('should extract ad weight', () => {
            const { weights } = service.getMetrics(lighthouseAuditsMock);
            expect(weights.ad).toBe(18055);
        });

        it('should extract animated-creative weight', () => {
            const { weights } = service.getMetrics(lighthouseAuditsMock);
            expect(weights['animated-creative']).toBe(54706);
        });

        it('should extract data.js weight', () => {
            const { weights } = service.getMetrics(lighthouseAuditsMock);
            expect(weights['data']).toBe(9691);
        });

        it('should extract video.js weight', () => {
            const { weights } = service.getMetrics(lighthouseAuditsMock);
            expect(weights['video']).toBe(4179);
        });

        it('should extract widget.js weight', () => {
            const { weights } = service.getMetrics(lighthouseAuditsMock);
            expect(weights['widget']).toBe(9162);
        });

        it('should extract feed.js weight', () => {
            const { weights } = service.getMetrics(lighthouseAuditsMock);
            expect(weights['feed']).toBe(2970);
        });
    });

    describe('metrics - main thread', () => {
        it('should extract main thread task time', () => {
            const { mainThread } = service.getMetrics(lighthouseAuditsMock);
            expect(mainThread.mainThreadTaskTime).toBe(218);
        });

        it('should extract main thread work time', () => {
            const { mainThread } = service.getMetrics(lighthouseAuditsMock);
            expect(mainThread.mainThreadWorkTime).toBe(1267);
        });
        it('should extract script evaluation time', () => {
            const { mainThread } = service.getMetrics(lighthouseAuditsMock);
            expect(mainThread.scriptEvaluationTime).toBe(951);
        });
    });

    describe('metrics - resources', () => {
        it('should extract Images', () => {
            const { resources } = service.getMetrics(lighthouseAuditsMock);
            expect(resources.Image).toEqual({
                count: 5,
                size: 52790,
                assets: [
                    {
                        size: 5887,
                        type: 'Image',
                        url: 'https://c.sandbox-bannerflow.net/accounts/bannerflow-enterprise/5e53a02edd716127a0365ff8/images/7cf4d182-1405-4474-8c39-573b01626426.png'
                    },
                    {
                        size: 1084,
                        type: 'Image',
                        url: 'https://c.sandbox-bannerflow.net/accounts/bannerflow-enterprise/5e53a02edd716127a0365ff8/images/dd375b4b-4382-4417-9f3a-a5c1ac1246de.svg'
                    },
                    {
                        size: 15637,
                        type: 'Image',
                        url: 'https://c.sandbox-bannerflow.net/io/api/image/optimize?u=https%3A%2F%2Fc.bannerflow.net%2Faccounts%2Fnadiia-productspec%2F62a0ac28a954234f299a2478%2Fimages%2F484a2cc0-d237-4b2a-88dd-69e356baf055.jpg&w=266&h=186&q=85&f=webp&rt=contain'
                    },
                    {
                        size: 12426,
                        type: 'Image',
                        url: 'https://c.sandbox-bannerflow.net/io/api/image/optimize?u=https%3A%2F%2Fc.bannerflow.net%2Faccounts%2Fnadiia-productspec%2F62a0ac28a954234f299a2478%2Fimages%2F617f8b39-bf7d-49b5-8fec-6595cd3f9fad.jpg&w=266&h=186&q=85&f=webp&rt=contain'
                    },
                    {
                        size: 17756,
                        type: 'Image',
                        url: 'https://c.sandbox-bannerflow.net/io/api/image/optimize?u=https%3A%2F%2Fc.bannerflow.net%2Faccounts%2Fnadiia-productspec%2F62a0ac28a954234f299a2478%2Fimages%2F639238b6-e042-47db-86df-b4880cc762f5.jpg&w=266&h=186&q=85&f=webp&rt=contain'
                    }
                ]
            });
        });

        it('should extract Video', () => {
            const { resources } = service.getMetrics(lighthouseAuditsMock);
            expect(resources.Video).toEqual({
                count: 1, // 3 streamed chunks
                size: 985932,
                assets: [
                    {
                        url: 'https://c.sandbox-bannerflow.net/accounts/bannerflow-enterprise/5e53a02edd716127a0365ff8/videos/32906e13f824483e97c18e79ae0b3939_lofi.mp4',
                        size: 985932,
                        type: 'Video'
                    }
                ]
            });
        });

        it('should extract Font', () => {
            const { resources } = service.getMetrics(lighthouseAuditsMock);
            expect(resources.Font).toEqual({
                count: 3,
                size: 18231,
                assets: [
                    {
                        size: 3718,
                        type: 'Font',
                        url: 'https://c.sandbox-bannerflow.net/fs/api/v2/font?u=https%3A%2F%2Ffonts.sandbox-bannerflow.net%2Ffontmanagerfonts%2F550686aa86699f0944e7371b%2F589c68c2-6124-4223-a6b9-ecae5fa9a1fd.woff&t=%20BNOTUWYeghimnopsty'
                    },
                    {
                        size: 10257,
                        type: 'Font',
                        url: 'https://c.sandbox-bannerflow.net/fs/api/v2/font?u=https%3A%2F%2Ffonts.sandbox-bannerflow.net%2Ffontmanagerfonts%2F550686aa86699f0944e7371b%2F2682e084-772f-446b-a8f8-e4ada779cc3d.woff&t=%20CFINPQSTacdehilnoprstuy'
                    },
                    {
                        size: 4256,
                        type: 'Font',
                        url: 'https://c.sandbox-bannerflow.net/fs/api/v2/font?u=https%3A%2F%2Ffonts.sandbox-bannerflow.net%2Ffontmanagerfonts%2F550686aa86699f0944e7371b%2F589c68c2-6124-4223-a6b9-ecae5fa9a1fd.woff%3Fr%3D57a79162-14c3-4cd7-8f91-2390862f82eefontfamily&t=%200123456789%3A%3C%3ESTaehlmnoprstuv'
                    }
                ]
            });
        });

        it('should extract Script', () => {
            const { resources } = service.getMetrics(lighthouseAuditsMock);
            expect(resources.Script).toEqual({
                count: 6,
                size: 98763,
                assets: [
                    {
                        size: 18055,
                        type: 'Script',
                        url: 'https://sandbox-api.bannerflow.net/acg/12619/123336/ad.js?access-token=158884fd49cc5bdf4c03aa98df4a48e555ee246845e54cfeb841429fa77e600e&font-loading=true&iframeless=true'
                    },
                    {
                        size: 4179,
                        type: 'Script',
                        url: 'https://sandbox-api.bannerflow.net/acg/scripts/video.edf66763f03175fa434e.js'
                    },
                    {
                        size: 9162,
                        type: 'Script',
                        url: 'https://sandbox-api.bannerflow.net/acg/scripts/widget.7344fd1cc1316959bffb.js'
                    },
                    {
                        size: 2970,
                        type: 'Script',
                        url: 'https://sandbox-api.bannerflow.net/acg/scripts/feed.6b460d46bcba05d2ede7.js'
                    },
                    {
                        size: 9691,
                        type: 'Script',
                        url: 'https://sandbox-api.bannerflow.net/acg/12619/123336/data.js?access-token=158884fd49cc5bdf4c03aa98df4a48e555ee246845e54cfeb841429fa77e600e'
                    },
                    {
                        size: 54706,
                        type: 'Script',
                        url: 'https://sandbox-api.bannerflow.net/acg/scripts/animated-creative.3fe1432f57033bd09875.js'
                    }
                ]
            });
        });

        it('should extract Total', () => {
            const { resources } = service.getMetrics(lighthouseAuditsMock);
            expect(resources.Total?.count).toEqual(16);
            expect(resources.Total?.size).toEqual(1156606);
        });
    });
});
