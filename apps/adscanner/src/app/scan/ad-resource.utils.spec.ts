import {
    AD_RESOURCE_REGEX,
    AD_RESOURCE_SCRIPTS_REGEX,
    extractFileNameWithoutHash,
    isAdResource,
    isAdResourceScript
} from './ad-resource.utils';

describe('Ad Resource Utils', () => {
    describe('AD_RESOURCE_REGEX', () => {
        it('should match ad.js files', () => {
            expect(
                AD_RESOURCE_REGEX.test(
                    'https://api.bannerflow.com/preview/822292/********/ad.js?access-token=8b5b4bffed382bcea8a36b88156c914eed065df1c7941d8aae21aa9ad06b9037'
                )
            ).toBe(true);
        });

        it('should match bannerflow ad service URLs', () => {
            expect(
                AD_RESOURCE_REGEX.test(
                    'https://c.bannerflow.net/a/681dc7af082cb12624fd018a?iframeless=true&font-loading=true'
                )
            ).toBe(true);
            expect(
                AD_RESOURCE_REGEX.test(
                    'https://c.sandbox-bannerflow.net/a/681dc7af082cb12624fd018a?iframeless=true&font-loading=true'
                )
            ).toBe(true);
        });

        it('should not match non-ad resources', () => {
            expect(AD_RESOURCE_REGEX.test('https://example.com/some-other-file.js')).toBe(false);
            expect(AD_RESOURCE_REGEX.test('https://c.bannerflow.net/other/path')).toBe(false);
            expect(AD_RESOURCE_REGEX.test('https://something.net/a/')).toBe(false);
            expect(
                AD_RESOURCE_REGEX.test(
                    'https://api.bannerflow.com/preview/822292/********/data.js?access-token=8b5b4bffed382bcea8a36b88156c914eed065df1c7941d8aae21aa9ad06b9037'
                )
            ).toBe(false);
        });
    });

    describe('AD_RESOURCE_SCRIPTS_REGEX', () => {
        it('should match data.js files', () => {
            expect(AD_RESOURCE_SCRIPTS_REGEX.test('data.js')).toBe(true);
            expect(AD_RESOURCE_SCRIPTS_REGEX.test('data.000000C23A612F.js')).toBe(true);
            expect(
                AD_RESOURCE_SCRIPTS_REGEX.test(
                    'https://api.bannerflow.com/preview/822292/********/data.js?access-token=8b5b4bffed382bcea8a36b88156c914eed065df1c7941d8aae21aa9ad06b9037'
                )
            ).toBe(true);
            expect(
                AD_RESOURCE_SCRIPTS_REGEX.test(
                    'https://c.bannerflow.net/accounts/mateuszkajsztura/64c364530b3ab7cde9656c1a/published/********/********/data.000000C23A612F.js'
                )
            ).toBe(true);
        });

        it('should match animated-creative.js files', () => {
            expect(AD_RESOURCE_SCRIPTS_REGEX.test('animated-creative.27b6414cecfabc493a17.js')).toBe(
                true
            );
            expect(
                AD_RESOURCE_SCRIPTS_REGEX.test(
                    'https://api.bannerflow.com/preview/scripts/animated-creative.27b6414cecfabc493a17.js'
                )
            ).toBe(true);
            expect(
                AD_RESOURCE_SCRIPTS_REGEX.test(
                    'https://c.bannerflow.net/scripts/animated-creative.27b6414cecfabc493a17.js'
                )
            ).toBe(true);
        });

        it('should match widget.js files', () => {
            expect(AD_RESOURCE_SCRIPTS_REGEX.test('widget.21503e303532cbf9c7fb.js')).toBe(true);
            expect(
                AD_RESOURCE_SCRIPTS_REGEX.test(
                    'https://api.bannerflow.com/preview/scripts/widget.21503e303532cbf9c7fb.js'
                )
            ).toBe(true);
            expect(
                AD_RESOURCE_SCRIPTS_REGEX.test(
                    'https://c.bannerflow.net/scripts/widget.21503e303532cbf9c7fb.js'
                )
            ).toBe(true);
        });

        it('should match feed.js files', () => {
            expect(AD_RESOURCE_SCRIPTS_REGEX.test('feed.e300f6e1f7dc5c44ac9f.js')).toBe(true);
            expect(
                AD_RESOURCE_SCRIPTS_REGEX.test(
                    'https://api.bannerflow.com/preview/scripts/feed.e300f6e1f7dc5c44ac9f.js'
                )
            ).toBe(true);
            expect(
                AD_RESOURCE_SCRIPTS_REGEX.test(
                    'https://c.bannerflow.net/scripts/feed.e300f6e1f7dc5c44ac9f.js'
                )
            ).toBe(true);
        });

        it('should match video.js files', () => {
            expect(AD_RESOURCE_SCRIPTS_REGEX.test('video.c49685bbc3d9fd30ab45.js')).toBe(true);
            expect(
                AD_RESOURCE_SCRIPTS_REGEX.test(
                    'https://api.bannerflow.com/preview/scripts/video.c49685bbc3d9fd30ab45.js'
                )
            ).toBe(true);
            expect(
                AD_RESOURCE_SCRIPTS_REGEX.test(
                    'https://c.bannerflow.net/scripts/video.c49685bbc3d9fd30ab45.js'
                )
            ).toBe(true);
        });

        it('should not match other script files', () => {
            expect(AD_RESOURCE_SCRIPTS_REGEX.test('script.js')).toBe(false);
            expect(AD_RESOURCE_SCRIPTS_REGEX.test('https://example.com/other-script.js')).toBe(false);
        });

        it('should not match ad.js files', () => {
            expect(
                AD_RESOURCE_SCRIPTS_REGEX.test(
                    'https://api.bannerflow.com/preview/822292/********/ad.js?access-token=8b5b4bffed382bcea8a36b88156c914eed065df1c7941d8aae21aa9ad06b9037'
                )
            ).toBe(false);
            expect(
                AD_RESOURCE_SCRIPTS_REGEX.test(
                    'https://c.bannerflow.net/a/681dc7af082cb12624fd018a?iframeless=true&font-loading=true'
                )
            ).toBe(false);
        });
    });

    describe('isAdResource', () => {
        it('should return true for ad resources', () => {
            expect(isAdResource('ad.js')).toBe(true);
            expect(
                isAdResource(
                    'https://api.bannerflow.com/preview/822292/********/ad.js?access-token=8b5b4bffed382bcea8a36b88156c914eed065df1c7941d8aae21aa9ad06b9037'
                )
            ).toBe(true);
            expect(
                isAdResource(
                    'https://c.bannerflow.net/a/681dc7af082cb12624fd018a?iframeless=true&font-loading=true'
                )
            ).toBe(true);
        });

        it('should return false for non-ad resources', () => {
            expect(isAdResource('https://example.com/some-other-file.js')).toBe(false);
        });
    });

    describe('isAdResourceScript', () => {
        it('should return true for ad resource scripts', () => {
            expect(isAdResourceScript('data.js')).toBe(true);
            expect(isAdResourceScript('animated-creative.27b6414cecfabc493a17.js')).toBe(true);
            expect(isAdResourceScript('widget.21503e303532cbf9c7fb.js')).toBe(true);
            expect(isAdResourceScript('feed.e300f6e1f7dc5c44ac9f.js')).toBe(true);
            expect(isAdResourceScript('video.c49685bbc3d9fd30ab45.js')).toBe(true);
        });

        it('should return false for non-ad resource scripts', () => {
            expect(isAdResourceScript('script.js')).toBe(false);
        });
    });

    describe('extractFileNameWithoutHash', () => {
        it('should extract file name without hash and extension', () => {
            expect(extractFileNameWithoutHash('data.000000C23A612F.js')).toBe('data');
            expect(extractFileNameWithoutHash('animated-creative.27b6414cecfabc493a17.js')).toBe(
                'animated-creative'
            );
            expect(extractFileNameWithoutHash('widget.21503e303532cbf9c7fb.js')).toBe('widget');
            expect(extractFileNameWithoutHash('feed.e300f6e1f7dc5c44ac9f.js')).toBe('feed');
            expect(extractFileNameWithoutHash('video.c49685bbc3d9fd30ab45.js')).toBe('video');
        });
    });
});
