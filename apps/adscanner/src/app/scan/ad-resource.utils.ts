/**
 * Regular expression to match ad.js files and bannerflow URLs
 */
export const AD_RESOURCE_REGEX = /ad\.js|c\.(sandbox-bannerflow|bannerflow)\.net\/a\//;
/**
 * Regular expression to match specific ad resource scripts
 * - data.js
 * - animated-creative.js
 * - widget.js
 * - feed.js
 * - video.js
 */
export const AD_RESOURCE_SCRIPTS_REGEX =
    /data\.[a-fA-F0-9]*\.*js|animated-creative\.[a-f0-9]+\.js|widget\.[a-f0-9]+\.js|feed\.[a-f0-9]+\.js|video\.[a-f0-9]+\.js/;

export function isAdResource(url: string): boolean {
    return AD_RESOURCE_REGEX.test(url);
}

export function isAdResourceScript(nameOrUrl: string): boolean {
    return AD_RESOURCE_SCRIPTS_REGEX.test(nameOrUrl);
}

export function extractFileNameWithoutHash(name: string): string {
    return name.split('.')[0];
}
