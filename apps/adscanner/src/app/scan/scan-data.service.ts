import { Inject, Injectable, Logger } from '@nestjs/common';
import { AdScanData } from '@domain/adscanner';
import { AdScanDto } from '@domain/adscanner/data';
import { Db } from 'mongodb';
import { MONGODB_TOKEN } from '../database/constants';

@Injectable()
export class ScanDataService {
    private logger = new Logger(ScanDataService.name);

    constructor(@Inject(MONGODB_TOKEN) private readonly db: Db) {}

    async save(
        scanData: AdScanData,
        creativeset: string,
        creative: string,
        creativeChecksum: string | undefined
    ): Promise<void> {
        try {
            this.logger.log(`Saving scan for creative: ${creative}, creativeset: ${creativeset}`);
            this.logger.debug(scanData);
            const adScanDto = getScanDataDto(scanData, creativeset, creative, creativeChecksum);
            await this.db.collection('ad_scans').insertOne(adScanDto);
        } catch (e: unknown) {
            this.logger.error(`Error saving scan: ${e}`);
            throw new Error('Failed to save scan data');
        }
    }

    async getOneByChecksum(creativeChecksum: string): Promise<AdScanDto | undefined> {
        const result = await this.db
            .collection('ad_scans')
            .findOne({ 'metadata.creativeChecksum': creativeChecksum });
        if (!result) {
            return;
        }
        return result as unknown as AdScanDto;
    }
}

function getScanDataDto(
    scanData: AdScanData,
    creativeset: string,
    creative: string,
    creativeChecksum: string | undefined
): AdScanDto {
    return {
        metadata: { creativeset, creative, creativeChecksum },
        timestamp: new Date(),
        ...scanData
    };
}
