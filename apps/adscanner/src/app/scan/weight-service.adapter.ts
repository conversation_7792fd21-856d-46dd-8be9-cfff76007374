import { AdScanData } from '@domain/adscanner';

export interface WeightServiceDto {
    url: string;
    totalWeight: number;
    frameworkOverhead: Load;
    initialLoad: Load;
    subLoad: Load;
}

interface Load {
    totalWeight: number;
    assets: LoadAsset[];
}

interface LoadAsset {
    weight: number;
    name: string;
    url: string;
    parentUrl: string;
    type: string;
}

export function getWeightServiceDto(adScanData: AdScanData): WeightServiceDto {
    const { url, resources } = adScanData;
    const totalWeight = resources.Total?.size ?? 0;
    const frameworkOverhead = {
        totalWeight: 0,
        assets: []
    };
    const initialLoad = {
        totalWeight: 0,
        assets: []
    };

    const assets = Object.keys(resources)
        .filter(key => key !== 'Total')
        .map(key => resources[key])
        .map(resource => resource?.assets)
        .filter(items => items !== undefined)
        .flat();

    const subLoad = {
        totalWeight,
        assets: assets.map(asset => {
            const assetURL = new URL(asset.url);
            const name = assetURL.pathname;
            return {
                weight: asset.size,
                name: name,
                url: asset.url,
                parentUrl: url,
                type: asset.type === 'Script' ? 'Application' : asset.type
            };
        })
    };

    return {
        url,
        totalWeight,
        frameworkOverhead,
        initialLoad,
        subLoad
    };
}
