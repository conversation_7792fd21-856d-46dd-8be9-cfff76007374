import {
    AdMainThreadTimes,
    AdMetrics,
    AdResources,
    AdScanData,
    AdTimings,
    AdWeights,
    BaseScanData,
    ScanReport,
    ScanReportResult
} from '@domain/adscanner';
import { Injectable, Logger } from '@nestjs/common';
import { AzureBlobService } from '@shared/server/azure';
import { getFileExtension } from '@studio/utils/url';
import * as LH from 'lighthouse/types/lh';
import { join, normalize } from 'node:path';
import { LighthouseService } from '../lighthouse/lighthouse.service';
import { getMedianLighthouseResult } from '../lighthouse/median-run';
import {
    getTimestampFilename,
    isBannerflowEntity,
    isCPSAdPreviewUrl,
    isCPSCreativePreviewMetadataUrl,
    isCPSCreativePreviewUrl,
    kB
} from '../utils';
import { extractFileNameWithoutHash, isAdResource, isAdResourceScript } from './ad-resource.utils';
import { ScanDataService } from './scan-data.service';
import { ScanHttpService } from './scan-http.service';
import { getWeightServiceDto, WeightServiceDto } from './weight-service.adapter';

const REPORT_DEST = 'scans';
const IGNORED_RESOURCES = ['Document', 'Other', 'Preflight'];

function getMediaTypeFromUrl(url: string): string {
    const extension = getFileExtension(url);
    if (extension === 'mp4') {
        return 'Video';
    }

    return 'Unknown';
}

@Injectable()
export class ScanService {
    private logger = new Logger(ScanService.name);

    constructor(
        private readonly azureBlobStorageService: AzureBlobService,
        private readonly scanDataService: ScanDataService,
        private readonly lighthouseService: LighthouseService,
        private readonly scanHttpService: ScanHttpService
    ) {}

    async processReport(
        lhr: LH.Result,
        creativeset: string,
        creative: string,
        creativeChecksum?: string
    ): Promise<AdScanData> {
        // save full report for archiving
        const adScanData = this.getScanData(lhr);
        const reportName = `${creativeset}/${creative}/report_${getTimestampFilename()}.json`;

        const storageOrigin = this.azureBlobStorageService.getStorageOrigin(REPORT_DEST);
        const scanData: AdScanData = {
            ...adScanData,
            creativeset,
            creative,
            reportPath: normalize(join(storageOrigin, reportName))
        };

        await this.scanDataService.save(scanData, creativeset, creative, creativeChecksum);
        await this.saveLHReport(reportName, JSON.stringify(lhr));
        return scanData;
    }

    getMetrics(audits: Record<string, ScanReportResult>): AdMetrics {
        let customTimings: AdTimings | undefined;
        try {
            customTimings = this.getCustomTimings(audits);
        } catch (err) {
            this.logger.error(err);
        }

        return {
            weights: this.getWeights(audits),
            mainThread: this.getMainThreadTimes(audits),
            resources: this.getResourcesFromNetwork(audits),
            customTimings
        };
    }

    async getReport(
        auditUrl: string,
        creativeChecksum?: string,
        runs = 1
    ): Promise<BaseScanData | ScanReport | string | undefined> {
        const report = creativeChecksum && (await this.getReportFromDb(creativeChecksum));
        if (report) {
            this.logger.log(`Report found for ${auditUrl} with creative checksum ${creativeChecksum}`);
            return report;
        }

        const results: LH.Result[] = [];
        for (let i = 0; i < runs; i++) {
            const result = await this.lighthouseService.getScanResult(auditUrl);
            if (result?.lhr) {
                results.push(result.lhr);
            }
        }

        const lhr = getMedianLighthouseResult(results);

        if (isCPSCreativePreviewMetadataUrl(auditUrl)) {
            const metadataUrlParam = new URL(auditUrl).searchParams.get('metadata');
            if (metadataUrlParam) {
                const { id, creativeset } =
                    await this.scanHttpService.getCreativeDataFromBlob(metadataUrlParam);
                const creativesetId = creativeset.id;

                return this.processReport(lhr, creativesetId, id, creativeChecksum);
            }
        }

        if (isCPSCreativePreviewUrl(auditUrl)) {
            const { creativeset, creative } = this.extractIdsFromUrl(lhr.finalDisplayedUrl);

            return this.processReport(lhr, creativeset, creative, creativeChecksum);
        }

        if (isCPSAdPreviewUrl(auditUrl)) {
            return this.getScanData(lhr);
        }

        return lhr;
    }

    async getReportForSapi(auditUrl: string): Promise<WeightServiceDto> {
        this.logger.verbose(`Generating SAPI report`);
        const decodedUrl = decodeURIComponent(auditUrl); // decoding metadataUrl
        const report = await this.getReport(decodedUrl);
        return getWeightServiceDto(report as AdScanData);
    }

    async getHtmlReport(auditUrl: string): Promise<string | undefined> {
        const result = await this.lighthouseService.getScanResult(auditUrl);
        if (!result) {
            return;
        }

        return result.report[1];
    }

    async getReportFromDb(checksum: string): Promise<BaseScanData | undefined> {
        const cachedResult = await this.scanDataService.getOneByChecksum(checksum);
        if (!cachedResult) {
            return;
        }

        const { metadata: _metadata, timestamp: _timestamp, ...adScanData } = cachedResult;

        return adScanData;
    }
    private getWeights(audits: Record<string, ScanReportResult>): AdWeights {
        this.logger.debug('Script bytes comparison');
        this.logger.debug('________________');

        const totalWeightItems = audits['network-requests'].details.items as any;
        const totalWeightItemsByLabel: object = totalWeightItems.reduce((acc, item) => {
            const segments = new URL(item.url).pathname.split('/');
            const last = segments.pop() || segments.pop(); // Handle potential trailing slash
            acc[last!] = item;
            return acc;
        }, {});

        const sortedTotalWeights: any = Object.entries(totalWeightItemsByLabel).sort(
            (a, b) => b[1].transferSize! - a[1].transferSize!
        );

        const weights: AdWeights = {
            ad: 0,
            'animated-creative': 0
        };
        const ad = sortedTotalWeights.find(([_name, details]) => isAdResource(details.url as string));

        if (ad) {
            const bytes = ad[1].transferSize as number;
            this.logger.debug(`Ad: ${kB(bytes)}kB`);
            weights.ad = bytes;
        }

        for (const [name, totalBytes] of sortedTotalWeights) {
            const bytes = totalBytes.transferSize as number;
            this.logger.debug(`${name}: ${kB(bytes)}kB`);
            if (isAdResourceScript(name)) {
                const fileName = extractFileNameWithoutHash(name);
                weights[fileName] = bytes;
            }
        }

        return weights;
    }

    private getScanData(lhr: LH.Result): BaseScanData {
        const metrics = this.getMetrics(lhr.audits);
        return {
            url: lhr.finalDisplayedUrl,
            duration: lhr.timing.total,
            environment: {
                benchmarkIndex: lhr.environment.benchmarkIndex,
                warnings: [...lhr.runWarnings]
            },
            ...metrics
        };
    }

    private getMainThreadTimes(audits: Record<string, ScanReportResult>): AdMainThreadTimes {
        const mainThreadWork = audits['mainthread-work-breakdown'] as LH.Audit.NumericProduct;
        this.logger.debug(`Main thread work time: ${Math.round(mainThreadWork.numericValue)} ms`);

        const mainThreadTasksItems = audits['main-thread-tasks'].details
            .items as LH.Artifacts.TaskNode[];
        const mainThreadTaskTime = mainThreadTasksItems.reduce(
            (totalTime, task) => totalTime + task.duration,
            0
        );
        this.logger.debug(`Main thread tasks time: ${Math.round(mainThreadTaskTime)} ms`);

        const mainThreadWorkDetails = mainThreadWork.details as LH.Audit.Details.Table;
        const scriptEvaluationTask = mainThreadWorkDetails.items.find(
            item => item.group === 'scriptEvaluation'
        );
        const scriptEvaluationTime = (scriptEvaluationTask?.duration as number) ?? 0;
        this.logger.debug(`Main thread script evaluation: ${Math.round(scriptEvaluationTime)} ms`);

        return {
            mainThreadWorkTime: Math.round(mainThreadWork.numericValue),
            mainThreadTaskTime: Math.round(mainThreadTaskTime),
            scriptEvaluationTime: Math.round(scriptEvaluationTime)
        };
    }

    private getResourcesFromNetwork(audits: Record<string, ScanReportResult>): AdResources {
        const requests = audits['network-requests'] as LH.Audit.NumericProduct;
        this.logger.debug(`Network summary: ${requests.displayValue}`);
        this.logger.debug('________________');

        const details = requests.details as LH.Audit.Details.Table;
        const summary: AdResources = {
            Total: { count: 0, size: 0, assets: [] }
        };

        details.items.filter(isBannerflowEntity).forEach(requestItem => {
            const resourceType = requestItem.resourceType as string;
            const transferSize = requestItem.transferSize as number;
            if (!transferSize) {
                return;
            }

            if (IGNORED_RESOURCES.includes(resourceType)) {
                return;
            }

            const url = requestItem.url as string;
            let type = resourceType;

            if (resourceType === 'Media') {
                type = getMediaTypeFromUrl(url);
            } else if (resourceType === 'Fetch') {
                type = 'Text';
            }

            const asset = {
                url,
                type,
                size: transferSize
            };

            if (summary[type]) {
                summary[type]!.size += transferSize;

                const existingAsset = summary[type]!.assets.find(i => i.url === url);
                if (existingAsset) {
                    existingAsset.size += transferSize;
                } else {
                    summary[type]!.count++;
                    summary[type]!.assets.push(asset);
                    summary['Total']!.count++;
                    summary['Total']!.assets.push(asset);
                }
            } else {
                summary[type] = {
                    count: 1,
                    size: transferSize,
                    assets: [asset]
                };
                summary['Total']!.count++;
                summary['Total']!.assets.push(asset);
            }

            summary['Total']!.size += transferSize;
        });

        for (const item in summary) {
            if (summary[item]?.count === 0) {
                continue;
            }
            this.logger.debug(`${item}: ${summary[item]?.count} \t\t- ${kB(summary[item]?.size)}kB`);
        }

        return summary;
    }

    private getCustomTimings(audits: Record<string, ScanReportResult>): AdTimings {
        const timings: AdTimings = {};

        const customTimings = audits['user-timings'] as LH.Audit.Product;

        const details = customTimings.details as LH.Audit.Details.Table;

        for (const timingItem of details.items) {
            this.logger.debug(`${timingItem.name}: ${timingItem.duration || timingItem.startTime}`);
            timings[timingItem.name as string] = {
                startTime: timingItem.startTime as number,
                duration: timingItem.duration as number | undefined
            };
        }

        // 'AC loading features'
        if (timings['ac:init:load:features:end']) {
            timings['loading-features'] = {
                startTime: timings['ac:init:load:features:start'].startTime,
                duration: Math.round(
                    timings['ac:init:load:features:end'].startTime -
                        timings['ac:init:load:features:start'].startTime
                )
            };
        }

        // 'AC loading feeds'
        if (timings['ac:init:load:feeds:end']) {
            timings['loading-feeds'] = {
                startTime: timings['ac:init:load:feeds:start'].startTime,
                duration: Math.round(
                    timings['ac:init:load:feeds:end'].startTime -
                        timings['ac:init:load:feeds:start'].startTime
                )
            };
        }

        // 'AC loading fonts'
        if (timings['ac:preload:fonts:end']) {
            timings['loading-fonts'] = {
                startTime: timings['ac:preload:fonts:start'].startTime,
                duration: Math.round(
                    timings['ac:preload:fonts:end'].startTime -
                        timings['ac:preload:fonts:start'].startTime
                )
            };
        }

        // Ad creation to success
        if (timings['ad:creation'] && timings['ad:success']) {
            timings['ad-creation-to-success'] = {
                startTime: timings['ad:creation'].startTime,
                duration: Math.round(timings['ad:success'].startTime - timings['ad:creation'].startTime)
            };
        }

        return timings;
    }

    private async saveLHReport(fileName: string, report: string | string[] | undefined): Promise<void> {
        if (report) {
            await this.azureBlobStorageService.createIfNotExists(REPORT_DEST);

            if (typeof report === 'string') {
                await this.saveFile(fileName, report);
            } else {
                for (const rep of report) {
                    await this.saveFile(fileName, rep);
                }
            }
        }
    }

    private async saveFile(fileName: string, content: string): Promise<void> {
        this.logger.log(`Saving file: ${REPORT_DEST}/${fileName}`);
        await this.azureBlobStorageService.uploadBlob(REPORT_DEST, fileName, content);
    }

    private extractIdsFromUrl(url: string): { creativeset: string; creative: string } {
        let parsedUrl = new URL(url);
        const dataUrl = parsedUrl.searchParams.get('data-url');
        if (dataUrl) {
            parsedUrl = new URL(dataUrl);
        }
        const path = parsedUrl.pathname; // Get the path from the parsed URL

        const idRegex = /\/(\d+)\/(\d+)\//;
        const matches = idRegex.exec(path);

        if (!matches || matches.length < 3) {
            throw new Error(`Malformed url. No IDs found in: ${url}`);
        }

        const creativeset = matches[1];
        const creative = matches[2];
        return { creativeset, creative };
    }
}
