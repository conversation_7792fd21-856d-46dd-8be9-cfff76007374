import { Modu<PERSON> } from '@nestjs/common';
import { AzureModule } from '@shared/server/azure';
import { DatabaseModule } from '../database/database.module';
import { LighthouseModule } from '../lighthouse/ligthouse.module';
import { ScanDataService } from './scan-data.service';
import { ScanService } from './scan.service';
import { HttpModule } from '@nestjs/axios';
import { ScanHttpService } from './scan-http.service';

@Module({
    imports: [DatabaseModule, AzureModule, LighthouseModule, HttpModule],
    providers: [ScanService, ScanDataService, ScanHttpService],
    exports: [ScanService, ScanDataService]
})
export class ScanModule {}
