import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { IAdDataCreative } from '@domain/ad/ad-data-creative';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class ScanHttpService {
    constructor(private readonly httpService: HttpService) {}

    async getCreativeDataFromBlob(url: string): Promise<IAdDataCreative> {
        const response = await firstValueFrom(this.httpService.get(url));
        const metadata = response.data as IAdDataCreative;

        if (!metadata.animated) {
            throw new Error('Metadata does not confirm to interface IAdDataCreative');
        }
        return metadata;
    }
}
