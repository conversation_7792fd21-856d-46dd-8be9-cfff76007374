import { access } from 'node:fs/promises';

export function kB(size: number | undefined): number {
    return Math.round((size ?? 0) / 1024);
}

export async function checkFolderExists(folderPath): Promise<boolean> {
    try {
        await access(folderPath);
        return true;
    } catch (error: any) {
        if (error.code === 'ENOENT') {
            // Folder does not exist
            return false;
        }
        throw error; // Throw for other errors
    }
}

export function getTimestampFilename(): string {
    return new Date().toISOString().replace(/[:T-]/g, '').slice(0, 14);
}

export function isCPSUrl(url: string): boolean {
    return (
        url.includes('api.bannerflow.local/preview') ||
        url.includes('sandbox-api.bannerflow.com/preview') ||
        url.includes('api.bannerflow.com/preview')
    );
}

export function isCPSCreativePreviewMetadataUrl(url: string): boolean {
    if (!isCPSUrl(url)) {
        return false;
    }

    return !!new URL(url).searchParams.get('metadata');
}

export function isCPSCreativePreviewUrl(url: string): boolean {
    if (!isCPSUrl(url)) {
        return false;
    }
    const parsedUrl = new URL(url);
    const idRegex = /\/(\d+)\/(\d+)\//;

    return idRegex.test(parsedUrl.pathname);
}

export function isCPSAdPreviewUrl(url: string): boolean {
    if (!isCPSUrl(url)) {
        return false;
    }

    const parsedUrl = new URL(url);
    return parsedUrl.pathname.includes('/preview/ad');
}

export function isBannerflowEntity({ entity }: any): boolean {
    if (!entity) {
        // include undefined entities -- meta content, inline images, etc.
        return true;
    }
    return entity.toLowerCase().includes('bannerflow');
}
