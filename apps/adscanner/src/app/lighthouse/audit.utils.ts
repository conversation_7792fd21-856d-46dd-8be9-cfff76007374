import { join } from 'node:path';
import * as LH from 'lighthouse/types/lh';

const IS_PROD = process.env.NODE_ENV === 'production';
const CREATIVE_AUDITS = [
    'creative-duration-audit',
    'creative-elements-audit',
    'creative-render-time-audit',
    'creative-widget-audit'
];

// Base directory for audits - local vs. docker differs
function getAuditBasePath(): string {
    if (IS_PROD) {
        return join(process.cwd(), 'adscanner', 'assets', 'audits');
    }
    return join(process.cwd(), 'apps', 'adscanner', 'src', 'assets', 'audits');
}

function resolveAuditPath(filename: string): string {
    return join(getAuditBasePath(), `${filename}.mjs`);
}

function mapAuditRefs(auditName: string): LH.Config.AuditRef {
    return { id: auditName, weight: 1 };
}

export function getCreativeAudits(): string[] {
    return CREATIVE_AUDITS.map(resolveAuditPath);
}

export function getBannerflowCategory(): LH.Config.CategoryJson {
    return {
        title: 'BannerFlow',
        description:
            'BannerFlow creative audits may vary. The score is calculated directly from the creative profile. [See Calculator](https://www.bannerflow.com/)',
        auditRefs: CREATIVE_AUDITS.map(mapAuditRefs)
    };
}

export function getCreativeGatherer(): LH.Config.ArtifactJson {
    return {
        id: 'CreativeProfile',
        gatherer: resolveAuditPath('creative-gatherer')
    };
}
