import * as LH from 'lighthouse/types/lh';
import { getBannerflowCategory, getCreativeAudits, getCreativeGatherer } from './audit.utils';

/*
    A combination of simulated throttling and the CPU slowdown allows to fully instrument the `ad:render` event.
    It was missing, at times, when configured with `throttlingMethod: 'devtoools'` & `cpuSlowdownMultiplier: 1.2`
 */
const config: LH.Config = {
    // 1. Run your custom tests along with all the default Lighthouse tests.
    extends: 'lighthouse:default',
    // 2. Register new artifact with custom gatherer.
    artifacts: [getCreativeGatherer()],
    // 3. Add custom audit to the list of audits 'lighthouse:default' will run.
    audits: getCreativeAudits(),
    // 4. Create a new 'BannerFlow' section in the default report for our results.
    categories: {
        bannerflow: getBannerflowCategory()
    },
    settings: {
        onlyCategories: ['performance', 'bannerflow'],
        formFactor: 'mobile',
        throttlingMethod: 'simulate', // 'simulate' can be faster than 'devtools'
        throttling: {
            cpuSlowdownMultiplier: 4 // this is adjusted to the current app service plan, depending on the benchmarkIndex
        },
        disableFullPageScreenshot: true
    }
};

export default config;
