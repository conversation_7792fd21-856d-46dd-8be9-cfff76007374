import { HttpService } from '@nestjs/axios';
import { Injectable, Logger, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AdScannerConfiguration } from '@domain/adscanner';

import * as LH from 'lighthouse/types/lh';
import puppeteer from 'puppeteer';

import { catchError, firstValueFrom, map } from 'rxjs';
import lighthouseConfig from './lighthouse.config';

// Releases: https://github.com/GoogleChrome/lighthouse/releases
// Docs: https://developer.chrome.com/docs/lighthouse/
const lighthouse = require('lighthouse/core/index.cjs'); // Access to just the lighthouse function in CommonJS

@Injectable()
export class LighthouseService implements OnModuleInit, OnModuleDestroy {
    private lighthouseLogs = false;
    private flags: LH.Flags = {
        output: ['json', 'html'],
        logLevel: 'warn'
    };

    private readonly logger = new Logger(LighthouseService.name);

    private puppeteerBrowser: LH.Puppeteer.Browser;
    private usePSI = false;

    constructor(
        private readonly configService: ConfigService<AdScannerConfiguration, true>,
        private readonly httpService: HttpService
    ) {
        this.lighthouseLogs = this.configService.get('lighthouseLogs');
        this.usePSI = this.configService.get('psi.enabled', { infer: true });
        if (this.lighthouseLogs) {
            this.flags.logLevel = 'info';
        }
    }

    async onModuleInit(): Promise<void> {
        if (!this.usePSI) {
            await this.launchPuppeteer();
        }
    }

    async onModuleDestroy(): Promise<void> {
        if (!this.usePSI) {
            await this.killPuppeteer();
        }
    }

    async getScanResult(url: string): Promise<LH.RunnerResult | undefined> {
        const tStart = performance.now();
        this.logger.log(`Starting scan - ${url}`);
        this.logger.debug(`Using ${this.usePSI ? 'PSI' : 'lighthouse cli'}`);

        let result: LH.RunnerResult | undefined;
        try {
            result = this.usePSI
                ? await this.getLighthouseReportFromPSI(url)
                : await this.getLighthouseReport(url);
        } catch (e: unknown) {
            this.logger.error(`Scan failed ${url}`);
            throw e;
        }

        if (!result) {
            this.logger.error('No runner result');
            return;
        }

        const measurementTime = performance.now() - tStart;
        this.logger.log(`Report was done for ${result.lhr.finalDisplayedUrl}`);
        this.logger.verbose(`Scan duration: ${measurementTime}`);

        if (result.lhr.runtimeError) {
            this.logger.error(`Runtime error: ${result.lhr.runtimeError.message}`);
            return;
        }

        return result;
    }

    async getLighthouseReport(url: string): Promise<LH.RunnerResult | undefined> {
        const browserContext = await this.puppeteerBrowser.createBrowserContext();
        const page = await browserContext.newPage();

        try {
            const result = await lighthouse(url, this.flags, lighthouseConfig, page);
            if (!result) {
                this.logger.error('No runner result');
            }
            return result;
        } finally {
            await browserContext.close();
        }
    }

    private async launchPuppeteer(): Promise<void> {
        this.logger.log('Launching puppeteer');

        // - Omit `--enable-automation` (See https://github.com/GoogleChrome/lighthouse/issues/12988)
        // - Don't use 800x600 default viewport
        // --no-sandbox needed for dockerized chrome https://github.com/puppeteer/puppeteer/issues/3698
        // --disable-dev-shm-usage needed for dockerized chrome https://github.com/GoogleChrome/lighthouse-ci/issues/504#issuecomment-740716892
        this.puppeteerBrowser = await puppeteer.launch({
            executablePath: process.env.PUPPETEER_EXECUTABLE_PATH,
            headless: !!this.configService.get('headless'),
            defaultViewport: null,
            ignoreDefaultArgs: ['--enable-automation'],
            args: ['--no-sandbox', '--disable-dev-shm-usage']
        });
    }

    private async killPuppeteer(): Promise<void> {
        this.logger.log('Killing puppeteer');
        await this.puppeteerBrowser.close();
    }

    private setupPSIQuery(url: string): URL {
        const api = 'https://www.googleapis.com/pagespeedonline/v5/runPagespeed';

        const query = new URL(api);
        query.searchParams.set('key', this.configService.get('psi.key', { infer: true }));
        query.searchParams.set('url', encodeURI(url));
        query.searchParams.set('strategy', 'mobile');
        query.searchParams.set('category', 'performance');
        return query;
    }

    private getLighthouseReportFromPSI(url: string): Promise<LH.RunnerResult | undefined> {
        const query = this.setupPSIQuery(url);
        const res = this.httpService.get<any>(query.toString()).pipe(
            map(({ data }) => {
                if (!data.lighthouseResult) {
                    this.logger.error('No lighthouse result');
                    return;
                }
                return {
                    lhr: data.lighthouseResult
                } as LH.RunnerResult;
            }),
            catchError(errRes => {
                const error = errRes.response.data;
                this.logger.error(error);
                return [];
            })
        );
        return firstValueFrom(res);
    }
}
