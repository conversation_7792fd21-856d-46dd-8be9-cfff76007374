import { createMock } from '@golevelup/ts-jest';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { LighthouseService } from './lighthouse.service';

describe('LighthouseService', () => {
    let service: LighthouseService;
    beforeEach(async () => {
        const configMock = createMock<ConfigService>();
        const httpServiceMock = createMock<HttpService>();
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                LighthouseService,
                { provide: ConfigService, useValue: configMock },
                { provide: HttpService, useValue: httpServiceMock }
            ]
        }).compile();

        service = module.get<LighthouseService>(LighthouseService);
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });
});
