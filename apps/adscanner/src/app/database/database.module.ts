import { Lo<PERSON>, Modu<PERSON> } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AdScannerConfiguration } from '@domain/adscanner';
import { Db, MongoClient, ServerApiVersion } from 'mongodb';
import { AzureModule, SecretInitializerService } from '@shared/server/azure';
import { AD_SCAN_COLLECTION, AD_SCANNER_DB, MONGODB_TOKEN } from './constants';

@Module({
    imports: [AzureModule],
    providers: [
        ConfigService,
        {
            provide: MONGODB_TOKEN,
            inject: [ConfigService, SecretInitializerService],
            useFactory: async (
                configService: ConfigService<AdScannerConfiguration, true>,
                secretsService: SecretInitializerService
            ): Promise<Db> => {
                try {
                    await secretsService.secretsInitialized;
                    const connectionString = configService.getOrThrow(
                        'database.mongodbConnectionString',
                        {
                            infer: true
                        }
                    );
                    Logger.verbose('Creating MongoClient');
                    const client = new MongoClient(connectionString, {
                        serverApi: {
                            version: ServerApiVersion.v1,
                            strict: true,
                            deprecationErrors: true
                        },
                        retryWrites: true,
                        writeConcern: {
                            w: 'majority'
                        }
                    });

                    Logger.verbose(`Connecting to db: '${AD_SCANNER_DB}'`, 'Database');
                    const db = client.db(AD_SCANNER_DB);
                    Logger.log(`Connected to db '${AD_SCANNER_DB}' successfully`, 'Database');

                    const collectionExists = await db
                        .listCollections({ name: AD_SCAN_COLLECTION })
                        .hasNext();
                    if (!collectionExists) {
                        Logger.log(`'${AD_SCAN_COLLECTION}' does not exist. Creating it.`, 'Database');
                        // Create a timeseries collection
                        await db.createCollection(AD_SCAN_COLLECTION, {
                            timeseries: {
                                timeField: 'timestamp',
                                metaField: 'metadata'
                            }
                        });
                        Logger.log(
                            `Created '${AD_SCAN_COLLECTION}' as a timeseries collection.`,
                            'Database'
                        );
                    }

                    return db;
                } catch (e) {
                    Logger.error(`Failed to connect to db '${AD_SCANNER_DB}'`, 'Database');
                    throw e;
                }
            }
        }
    ],
    exports: [MONGODB_TOKEN]
})
export class DatabaseModule {}
