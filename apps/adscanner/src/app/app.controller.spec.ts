import { createMock } from '@golevelup/ts-jest';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { AzureBlobService } from '@shared/server/azure';
import { AppController } from './app.controller';
import { ScanService } from './scan/scan.service';
import { AdScanData } from '@domain/adscanner';

describe('AppController', () => {
    let app: TestingModule;

    beforeAll(async () => {
        const configMock = createMock<ConfigService>();
        const azureServiceMock = createMock<AzureBlobService>({
            getBlob: () =>
                Promise.resolve(
                    JSON.stringify({
                        'test-ad': 'url'
                    })
                )
        });

        const scanServiceMock = createMock<ScanService>();

        app = await Test.createTestingModule({
            controllers: [AppController],
            providers: [
                { provide: ScanService, useValue: scanServiceMock },
                { provide: AzureBlobService, useValue: azureServiceMock },
                { provide: ConfigService, useValue: configMock }
            ]
        }).compile();
    });

    describe('scan', () => {
        it('should acknowledge that a scan has started', async () => {
            const scanUrl = 'https://example.com';

            const appController = app.get<AppController>(AppController);
            const scanServiceMock = app.get<ScanService>(ScanService);
            const getReportSpy = jest
                .spyOn(scanServiceMock, 'getReport')
                .mockResolvedValue({} as AdScanData);

            const result = await appController.scanUrl({ type: jest.fn() } as any, scanUrl);
            expect(result).toEqual({
                message: 'Scan successful',
                data: {}
            });
            expect(getReportSpy).toHaveBeenCalledWith(scanUrl, undefined, undefined);
        });
    });
});
