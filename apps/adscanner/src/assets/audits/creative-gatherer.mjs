import { Gather<PERSON> } from 'lighthouse';

// puppeteer context; https://github.com/GoogleChrome/lighthouse/blob/main/docs/recipes/custom-gatherer-puppeteer/custom-gatherer.js

/**
 * Produces the `CreativeProfile.value` artifact containing relevant information about the creative.
 */
class CreativeProfile extends Gatherer {
    meta = {
        supportedModes: ['navigation', 'timespan', 'snapshot']
    };

    // TODO; add beforePass to add eventlisteners

    async getArtifact(context) {
        const { driver } = context;
        const { executionContext } = driver;

        let value = {
            elements: [],
            duration: -1
        };

        function checkBannerflowScope() {
            const ad = window['_bannerflow'].ads[0];
            if (!ad) {
                throw new Error('Ad tag not found');
            }

            const duration = ad.selectedCreative.duration;
            return {
                duration: duration ?? -1
            };
        }
        const bannerflowData = await executionContext.evaluate(checkBannerflowScope, { args: [] });

        function checkDesign() {
            function mapCreativeElement(el) {
                const { id, kind, duration } = el;
                return {
                    id,
                    kind,
                    duration
                };
            }

            const ad = window['_bannerflow'].ads[0];
            if (!ad) {
                throw new Error('Ad tag not found');
            }
            // this is hacking unstable internal APIs
            const creativeData = ad.creativeLoader.creative.creativeData;
            return {
                elements: creativeData.elements.map(mapCreativeElement)
            };
        }

        const designData = await executionContext.evaluate(checkDesign, { args: [] });

        value = {
            ...value,
            ...bannerflowData,
            ...designData
        };

        return { value };
    }
}

export default CreativeProfile;
