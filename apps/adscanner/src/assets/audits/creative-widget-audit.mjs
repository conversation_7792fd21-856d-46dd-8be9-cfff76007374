import { Audit } from 'lighthouse';

const MAX_WIDGETS = 0;

/**
 * @fileoverview Tests that the creative does not contain too many widgets.
 */
class CreativeWidgetAudit extends Audit {
    static get meta() {
        return {
            id: 'creative-widget-audit',
            title: 'Eliminate widget usage',
            failureTitle: 'Too many widgets found in the design',
            description:
                'Avoid using widgets as they have a heavy impact on performance. Use native creative elements instead. [Learn more why widgets suck](https://github.com/)',

            // The name of the custom gatherer class that provides input to this audit.
            requiredArtifacts: ['CreativeProfile']
        };
    }

    static audit(artifacts) {
        const creativeProfile = artifacts['CreativeProfile'];
        const elements = creativeProfile.value.elements;
        const widgets = elements.filter(el => el.kind === 'widget').length;

        return {
            numericValue: widgets,
            numericUnit: 'unitless',
            score: widgets > MAX_WIDGETS ? 0 : 1,
            notApplicable: widgets === 0 // disable audit if design does not include any widgets
        };
    }
}

export default CreativeWidgetAudit;
