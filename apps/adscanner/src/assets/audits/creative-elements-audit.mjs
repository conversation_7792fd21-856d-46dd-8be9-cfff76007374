import { Audit } from 'lighthouse';

const MAX_ELEMENTS = 5;

/**
 * @fileoverview Tests that the creative does not contain too many elements.
 */
class CreativeElementsAudit extends Audit {
    static get meta() {
        return {
            id: 'creative-elements-audit',
            title: 'Creative elements count is within limits',
            failureTitle: 'Too many elements in the creative',
            description: 'Detects if the creative is using a proper amount of elements.',

            // The name of the custom gatherer class that provides input to this audit.
            requiredArtifacts: ['CreativeProfile']
        };
    }

    static audit(artifacts) {
        const creativeProfile = artifacts['CreativeProfile'];
        const elements = creativeProfile.value.elements;
        const numElements = elements.length;

        const headings = [
            { key: 'id', itemType: 'text', text: 'Id' },
            { key: 'kind', itemType: 'text', text: 'Kind' },
            { key: 'duration', itemType: 'numeric', text: 'Duration' }
        ];
        const details = Audit.makeTableDetails(headings, elements);

        return {
            numericValue: numElements,
            numericUnit: 'unitless',
            scoreDisplayMode: 'metricSavings',
            displayValue: `${numElements} elements`,
            score: numElements > MAX_ELEMENTS ? 0 : 1,
            details
        };
    }
}

export default CreativeElementsAudit;
