import { Audit } from 'lighthouse';
import { UserTimings } from 'lighthouse/core/computed/user-timings.js';

const MAX_RENDER_DURATION = 2000;

class CreativeRenderTimeAudit extends Audit {
    static get meta() {
        return {
            id: 'creative-render-time-audit',
            title: 'Creative render time',
            failureTitle: "Creative doesn't render quickly enough",
            description: 'Ad render time needs to be low for a good end-user experience.',
            requiredArtifacts: ['traces']
        };
    }

    static async audit(artifacts, context) {
        const trace = artifacts.traces[Audit.DEFAULT_PASS];
        const userTimings = await UserTimings.request(trace, context);
        const creativeRendered = userTimings.find(ut => ut.name === 'ad:render').startTime;

        const formattedTiming = Math.round((creativeRendered / 1000) * 10) / 10 + 's';
        const scoreTiming = Math.min(MAX_RENDER_DURATION / creativeRendered, 1);

        return {
            numericValue: creativeRendered,
            score: scoreTiming,
            scoreDisplayMode: 'metricSavings',
            displayValue: `Creative rendered in ${formattedTiming}`
        };
    }
}

export default CreativeRenderTimeAudit;
