import { Audit } from 'lighthouse';

const MAX_DURATION = 3;

/**
 * @fileoverview Tests that the creative duration does not exceed the limit
 */
class CreativeDurationAudit extends Audit {
    static get meta() {
        return {
            id: 'creative-duration-audit',
            title: 'Creative duration is within limits',
            failureTitle: `Creative is too long`,
            description: `Detects if the creative duration does not exceed the limit. Should be < ${MAX_DURATION}s`,

            // The name of the custom gatherer class that provides input to this audit.
            requiredArtifacts: ['CreativeProfile']
        };
    }

    static audit(artifacts) {
        const creativeProfile = artifacts['CreativeProfile'];
        const maxDurationReached = creativeProfile.value.elements.some(
            el => el.duration > MAX_DURATION
        );

        return {
            numericValue: maxDurationReached,
            numericUnit: 'unitless',
            scoreDisplayMode: 'metricSavings',
            score: maxDurationReached > MAX_DURATION ? 0 : 1
        };
    }
}

export default CreativeDurationAudit;
