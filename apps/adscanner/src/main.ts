console.time('bootstrap');
console.time('OpenTelemetry');
// NOTE: OpenTelemetry has to be the first import
import '@shared/server/monitoring/instrumentation';
console.timeEnd('OpenTelemetry');

// rest of app imports
console.time('main.ts imports');
import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { getPort } from '@shared/server/environment';
import { AppModule } from './app/app.module';
import { createLogger, format, transports } from 'winston';
import { utilities as nestWinstonModuleUtilities } from 'nest-winston/dist/winston.utilities';
import { WinstonModule } from 'nest-winston';
console.timeEnd('main.ts imports');

async function bootstrap(): Promise<void> {
    const loggerInstance = createLogger({
        level: process.env.LOG_LEVEL ?? 'info',
        transports: [
            new transports.Console({
                format: format.combine(
                    format.timestamp(),
                    format.ms(),
                    nestWinstonModuleUtilities.format.nestLike('AdScanner', {
                        colors: process.env.STAGE === 'local',
                        prettyPrint: true
                    })
                )
            })
        ]
    });

    const app = await NestFactory.create(AppModule, {
        logger: WinstonModule.createLogger({
            instance: loggerInstance
        })
    });

    app.enableCors();

    console.timeEnd('bootstrap');

    const port = getPort();
    await app.listen(port);
    Logger.log(`🚀 Application is running on: http://localhost:${port}`);
}

bootstrap();
