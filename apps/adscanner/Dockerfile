# This file is generated by Nx.
#
# Build the docker image with `pnpm exec nx docker-build adscanner`.
# Tip: Modify "docker-build" options in project.json to change docker build args.
#
# Run the container with `docker run -p 3005:3005 -t adscanner`.

# NX Docs: https://nx.dev/node-server-tutorial/5-docker-target
## Adjusted to support chrome in docker - https://github.com/GoogleChrome/lighthouse-ci/blob/main/docs/recipes/docker-client/Dockerfile
FROM docker.io/node:20

# Install utilities
RUN apt-get update --fix-missing && apt-get -y upgrade && apt-get install -y git wget gnupg && apt-get clean

# Install latest chrome stable package.
RUN wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add -
RUN sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list'
RUN apt-get update \
    && apt-get install -y google-chrome-stable --no-install-recommends \
    && apt-get clean


# Setup a user to avoid doing everything as root
RUN groupadd --system -g 1001 adscanner && \
  useradd --system --create-home -u 1001 -g 1001 adscanner && \
  mkdir --parents /home/<USER>/app /home/<USER>/.cache/google-chrome && \
  chown --recursive adscanner:adscanner /home/<USER>

WORKDIR /home/<USER>/app

COPY dist/apps/adscanner adscanner

# Set npm authentication for private registry
COPY .npmrc adscanner

# To only include dependencies adscanner needs, we do a fresh install with the auto-generated package.json
RUN npm --prefix adscanner --omit=dev -f install --ignore-scripts

ENV NODE_ENV=production
# Set the Puppeteer executable path environment variable
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome-stable

# Switch to non-root user
USER adscanner:adscanner

CMD ["node", "--enable-source-maps", "adscanner"]
