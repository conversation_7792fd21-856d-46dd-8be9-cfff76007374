{"name": "creative-preview-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/creative-preview-e2e/src", "projectType": "application", "tags": ["type:app"], "targets": {"open": {"executor": "@nx/playwright:playwright", "options": {"ui": true, "headed": true, "config": "{projectRoot}/playwright.config.ts"}}, "all": {"executor": "@nx/playwright:playwright", "options": {"config": "{projectRoot}/playwright.config.ts", "grepInvert": ".*percy\\.spec.*", "skipInstall": true}}, "percy": {"executor": "nx:run-commands", "options": {"command": "percy exec -- npx playwright test e2e/preview-percy.spec.ts --project=\"Google Chrome\"", "cwd": "apps/creative-preview-e2e"}}}}