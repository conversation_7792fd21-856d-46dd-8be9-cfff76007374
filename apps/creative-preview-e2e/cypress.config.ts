import { nxE2EPreset } from '@nx/cypress/plugins/cypress-preset';
import { defineConfig } from 'cypress';

export default defineConfig({
    projectId: 'z45ptt',
    chromeWebSecurity: false,
    defaultCommandTimeout: 30000,
    video: false,
    retries: {
        runMode: 1,
        openMode: 0
    },
    env: {},
    e2e: {
        ...nxE2EPreset(__filename, { cypressDir: 'cypress' }),
        testIsolation: false,
        // Please ensure you use `cy.origin()` when navigating between domains and remove this option.
        // See https://docs.cypress.io/app/references/migration-guide#Changes-to-cyorigin
        injectDocumentDomain: true
    }
});
