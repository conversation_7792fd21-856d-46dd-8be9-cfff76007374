const playwright = require('eslint-plugin-playwright');
const baseConfig = require('../../eslint.config.js');

module.exports = [
    playwright.configs['flat/recommended'],

    ...baseConfig,
    {
        files: ['**/*.ts', '**/*.tsx', '**/*.js', '**/*.jsx'],
        rules: {}
    },
    {
        files: ['**/*.ts', '**/*.tsx'],
        rules: {
            '@typescript-eslint/no-namespace': 'off',
            '@typescript-eslint/no-empty-interface': 'off'
        }
    },
    {
        files: ['**/*.js', '**/*.jsx'],
        rules: {}
    },
    {
        files: ['**/*.ts', '**/*.js'],
        // Override or add rules here
        rules: {}
    }
];
