import { IUrlParameterMap } from '@domain/ad/url-parameters';
import { expect } from '@playwright/test';
import { Page, FrameLocator, Locator } from 'playwright';

const local = false;
const CREATIVE_PREVIEW_URL = local
    ? 'http://api.bannerflow.local/preview'
    : 'https://sandbox-api.bannerflow.com/preview';

// creates a URL for the magic data.js endpoint
export function createUrl(url: string, time: string, env?: IUrlParameterMap['env']): string {
    return toStaticPreviewUrl(`/preview${url}`, time, env);
}

export function toStaticPreviewUrl(
    url: string,
    seek: IUrlParameterMap['seek'] = '2',
    env?: IUrlParameterMap['env'],
    autoplay: 'on' | 'off' = 'off'
): string {
    let previewUrl = `${CREATIVE_PREVIEW_URL}${url}&seek=${seek}&autoplay=${autoplay}&font-loading=true`;
    if (env) {
        previewUrl = `${previewUrl}&env=${env}`;
    }
    return previewUrl;
}

function getIframeBody(page: Page, iframeSelector: string): FrameLocator {
    return page.frameLocator(iframeSelector);
}

export function getWidgetLocator(page: Page, widgetSelector: string): Locator {
    const iframeBody = getAdIframeDocumentBody(page);
    const widgetDocument = iframeBody.locator(widgetSelector);
    if (!widgetDocument) {
        throw new Error('Widget document not found');
    }
    return widgetDocument;
}

export function getAdIframeDocumentBody(page: Page): FrameLocator {
    return getIframeBody(page, '.ad-wrapper iframe');
}

export function adRendered(page: Page): Promise<void> {
    const adLocator = page.locator('.studio-ad');
    // await adLocator.waitFor({ state: 'attached' });
    // await page.waitForSelector('[data-rendered="true"]');
    return expect(adLocator).toHaveAttribute('data-rendered', 'true', { timeout: 15000 });
}
