import { test, expect } from '@playwright/test';
import { adRendered, getAdIframeDocumentBody, toStaticPreviewUrl } from '../utils';

test.describe('States', () => {
    test.beforeEach(async ({ page }) => {
        await page.goto(
            toStaticPreviewUrl(
                '/11875/117467?access-token=f62a0c33b8cd35a9618fc14b381c93a376befee1ad3d769e719c55bffa7c4d17'
            )
        );

        await adRendered(page);
    });

    test.describe('hover', () => {
        test('should change the properties of elements on state change', async ({ page }) => {
            const iframeBody = getAdIframeDocumentBody(page);

            await iframeBody.locator('.kind-button').hover();
            await expect(iframeBody.locator('.kind-button path')).toHaveAttribute(
                'fill',
                'rgba(255,0,0,1)'
            );
            await expect(iframeBody.locator('.kind-rectangle path')).toHaveAttribute(
                'fill',
                'rgba(92,92,255,1)'
            );
            await expect(
                iframeBody.locator('[data-test-id="rich-text-span-text"][data-cy="b-t-0-0"]')
            ).toHaveCSS('color', 'rgb(0, 0, 0)');

            // move the mouse away to stop hover
            await page.mouse.move(0, 0);

            await expect(iframeBody.locator('.kind-button path')).toHaveAttribute(
                'fill',
                'rgba(128,128,128,1)'
            );
            await expect(iframeBody.locator('.kind-rectangle path')).toHaveAttribute(
                'fill',
                'rgba(0,0,0,1)'
            );
            await expect(
                iframeBody.locator('[data-test-id="rich-text-span-text"][data-cy="b-t-0-0"]')
            ).toHaveCSS('color', 'rgb(255, 255, 255)');
        });
    });
});
