import { test, expect } from '@playwright/test';
import { adRendered, getAdIframeDocumentBody, toStaticPreviewUrl } from '../utils';

test.describe('Video interactions', () => {
    test.describe('Autoplay off', () => {
        test.beforeEach(async ({ page }) => {
            await page.goto(
                toStaticPreviewUrl(
                    '/11876/117468?access-token=f98f7542295d758f6731b592fd29693534a42283cf6450baf373e98e07aaa721'
                )
            );

            await adRendered(page);
        });

        test('should be possible to pause and play video', async ({ page }) => {
            const iframeBody = getAdIframeDocumentBody(page);

            const videoRendererLocator = iframeBody.locator('video-renderer');
            await videoRendererLocator.click();
            await expect(videoRendererLocator.locator('.pause')).toBeVisible();

            await videoRendererLocator.click();
            await expect(videoRendererLocator.locator('.play')).toBeVisible();
        });

        test('should be possible to click outside of play/pause button and get redirected', async ({
            page
        }) => {
            const iframeBody = getAdIframeDocumentBody(page);
            await expect(iframeBody.locator('.playbackButton')).toBeVisible();
            await iframeBody.locator('video-renderer').click({ position: { x: 0, y: 0 } });
            const newPage = await page.waitForEvent('popup');
            expect(newPage.url()).toBe('https://www.bannerflow.com/');
        });
    });

    test.describe('Autoplay on', () => {
        test.beforeEach(async ({ page }) => {
            await page.goto(
                toStaticPreviewUrl(
                    '/11876/117616/preview?access-token=bd7ed34af79bbfabc3a49b611a9f4ec53be40e9a362513c60c2d679fa2eef52a'
                )
            );

            await adRendered(page);
        });

        test('should be possible to click anywhere and get redirected', async ({ page }) => {
            const iframeBody = getAdIframeDocumentBody(page);
            await expect(iframeBody.locator('.playbackButton')).toBeHidden();
            await iframeBody.locator('video-renderer').click({ force: true });
            const newPage = await page.waitForEvent('popup');
            expect(newPage.url()).toBe('https://www.bannerflow.com/');
        });
    });
});
