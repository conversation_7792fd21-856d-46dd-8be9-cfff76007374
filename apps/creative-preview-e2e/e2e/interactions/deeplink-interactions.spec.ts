import { test, expect } from '@playwright/test';
import { getAdIframeDocumentBody, getWidgetLocator, toStaticPreviewUrl, adRendered } from '../utils';

test.describe('Deeplink interactions', () => {
    test.beforeEach(async ({ page }) => {
        await page.goto(
            toStaticPreviewUrl(
                '/9340/104233/preview?access-token=dd2fc03f03f90a6f8c1be70d50e3e18de770866b63bfe2f59b651a3a2fa8a81c',
                '5',
                undefined,
                'on'
            )
        );

        await adRendered(page);
    });

    test.describe('creative', () => {
        test('should click on creative area', async ({ page }) => {
            const iframeBody = getAdIframeDocumentBody(page);
            await iframeBody.locator('#b1').click();
            const newPage = await page.waitForEvent('popup');
            expect(newPage.url()).toBe('https://www.bannerflow.com/?utm_source=creativeAdLevel');
        });
    });

    test.describe('Deeplinks', () => {
        test.describe('Widgets', () => {
            test('should redirect correctly when clicking on TODAY', async ({ page }) => {
                const widgetLocator = getWidgetLocator(page, '#e.element iframe');
                await widgetLocator.contentFrame().locator('#clickarea').click();
                const newPage = await page.waitForEvent('popup');
                expect(newPage.url()).toBe('https://www.bannerflow.com/?utm_source=TODAY');
            });

            test('should redirect correctly when clicking on TOMORROW', async ({ page }) => {
                const widgetLocator = getWidgetLocator(page, '#h.element iframe');

                await widgetLocator.contentFrame().locator('#clickarea').click();
                const newPage = await page.waitForEvent('popup');
                expect(newPage.url()).toBe('https://www.bannerflow.com/?utm_source=TOMORROW');
            });
        });

        // Sandbox Feed is broken, needs replacing
        test.describe('Feeded', () => {
            test.describe('Text', () => {
                test('should redirect correctly when clicking on feeded text element', async ({
                    page
                }) => {
                    const iframeBody = getAdIframeDocumentBody(page);
                    await iframeBody.locator('#k.element').click();
                    const newPage = await page.waitForEvent('popup');
                    expect(newPage.url()).toBe('https://play.bannerflow.com/redirect/?r=iPhone-Name');
                });
            });

            test.describe('Image', () => {
                test('should redirect correctly when clicking Iphone Image element', async ({
                    page
                }) => {
                    const iframeBody = getAdIframeDocumentBody(page);
                    await iframeBody.locator('#j.element').click();
                    const newPage = await page.waitForEvent('popup');
                    expect(newPage.url()).toBe('https://play.bannerflow.com/redirect/?r=iPhone-Image');
                });
            });
        });

        test.describe('Action', () => {
            test('should redirect correctly when clicking on "OR A PHONE" text element', async ({
                page
            }) => {
                const iframeBody = getAdIframeDocumentBody(page);
                await iframeBody.locator('#i.element').click();
                const newPage = await page.waitForEvent('popup');
                expect(newPage.url()).toBe(
                    'https://play.bannerflow.com/redirect/?r=Apple-iPhone-XS-Max'
                );
            });
        });
    });
});
