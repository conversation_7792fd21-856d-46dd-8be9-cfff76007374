import percySnapshot from '@percy/playwright';
import { test } from '@playwright/test';
import { creatives } from './fixtures/creatives';
import { adRendered, createUrl } from './utils';

const takeLocalScreenshots = false; // if you want to verify the local screenshots manually

const SCREENSHOT_FOLDER = 'apps/creative-preview-e2e/e2e/screenshots/';

test.describe('Creative Output', () => {
    for (const { name, params, timeStamps, env } of creatives) {
        const timesToSeek = timeStamps?.length ? timeStamps : ['2'];

        for (const time of timesToSeek) {
            const previewUrl = createUrl(params, time, env);
            test(`${name} at time ${time}s - ${previewUrl}`, async ({ page }) => {
                await page.goto(previewUrl);

                await adRendered(page);

                if (takeLocalScreenshots) {
                    const iframeBody = page.locator('.ad-wrapper iframe');
                    await iframeBody.screenshot({ path: `${SCREENSHOT_FOLDER}${name}-${time}s.png` });
                } else {
                    await percySnapshot(page, `${name} at time ${time}s`);
                }
            });
        }
    }
});
