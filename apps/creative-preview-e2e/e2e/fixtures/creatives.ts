import { IUrlParameterMap } from '@domain/ad/url-parameters';

type Creative = {
    params: string;
    name: string;
    /** Timestamps to seek to in the preview */
    timeStamps?: string[];
    env?: IUrlParameterMap['env'];
};

export const creatives: Creative[] = [
    {
        name: 'creative-2',
        params: '?brandId=5fc6314ba1f95115c0ba2bc0&data-url=https://api.bannerflow.com/preview/95115/1405397/data.js'
    },
    {
        name: 'headphones-1',
        params: '?brandId=6194f0d1515f4735ce1a7266&data-url=https://sandbox-api.bannerflow.com/preview/6104/46233/data.js'
    },
    {
        name: 'headphones-2',
        params: '?brandId=6194f0d1515f4735ce1a7266&data-url=https://sandbox-api.bannerflow.com/preview/6104/46232/data.js'
    },
    {
        name: 'boots-1',
        params: '?brandId=6194f0d1515f4735ce1a7266&data-url=https://sandbox-api.bannerflow.com/preview/6107/46256/data.js'
    },
    {
        name: 'Creative_more_elements',
        params: '?brandId=6194f0d1515f4735ce1a7266&data-url=https://sandbox-api.bannerflow.com/preview/9344/104246/data.js',
        timeStamps: ['2', '7', '11', '15', '18']
    },
    {
        name: 'Creative_more_elements image-generator',
        params: '?brandId=6194f0d1515f4735ce1a7266&data-url=https://sandbox-api.bannerflow.com/preview/9344/104246/data.js',
        env: 'image-generator'
    },
    {
        name: 'Creative_more_elements video-generator',
        params: '?brandId=6194f0d1515f4735ce1a7266&data-url=https://sandbox-api.bannerflow.com/preview/9344/104246/data.js',
        env: 'video-generator'
    },
    {
        name: 'Grouped_elements',
        params: '?brandId=6194f0d1515f4735ce1a7266&data-url=https://sandbox-api.bannerflow.com/preview/9358/104381/data.js'
    },
    {
        name: 'SpecialCharactersV2_1',
        params: '?brandId=6194f0d1515f4735ce1a7266&data-url=https://sandbox-api.bannerflow.com/preview/11883/117488/data.js'
    },
    {
        name: 'SpecialCharactersV2_2',
        params: '?brandId=6194f0d1515f4735ce1a7266&data-url=https://sandbox-api.bannerflow.com/preview/11883/117492/data.js'
    },
    {
        name: 'SpecialCharactersV2_3',
        params: '?brandId=6194f0d1515f4735ce1a7266&data-url=https://sandbox-api.bannerflow.com/preview/11883/117491/data.js'
    },
    {
        name: 'Feeded_elements',
        params: '?brandId=6194f0d1515f4735ce1a7266&data-url=https://sandbox-api.bannerflow.com/preview/9341/104236/data.js'
    },
    {
        name: 'Keyframe_animated_elements',
        params: '?brandId=6194f0d1515f4735ce1a7266&data-url=https://sandbox-api.bannerflow.com/preview/9343/104245/data.js',
        timeStamps: ['0', '1', '2', '3']
    }
];
