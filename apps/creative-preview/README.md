# Creative Preview Service

## AKS Scaling Configuration Guide

### Node Selector Configuration

-   **Production**: Uses `*nodeSelector: scaling: static` for stability and consistent performance
-   **Sandbox**: Uses `nodeSelector: scaling: dynamic` to leverage cost-saving autoscaling capabilities

Static nodes provide consistent performance, protection from scaling disruptions, guaranteed resources, and workload isolation from dynamic components.
This configuration works in conjunction with the Pod Disruption Budget (PDB) and resource settings to maximize service stability in the production environment.

### Resource Settings

These settings define the resource allocation for Creative Preview service pods in Azure Kubernetes Service:

#### Resource Requests (Guaranteed Resources)~~~~

-   **cpu: 500m** - Each pod is guaranteed 500 millicores (0.5 CPU core)
-   **memory: 900Mi** - Each pod is guaranteed 900 MiB(943.7 MB) of memory

#### Resource Limits (Maximum Usage)

-   **cpu: 1000m** - Each pod can use at most 1 full CPU core
-   **memory: 1800Mi** - Each pod can use at most 1800 MiB(1887.4 MB) of memory

#### Node.js App Memory Configuration (set in Dockerfile)

-   **max-old-space-size=1536** - Sets the V8 JavaScript heap limit to 1536MB [Node.js docs](https://nodejs.org/api/cli.html#--max-old-space-sizesize-in-megabytes)
    This setting should correspond with Resource Limit, being slightly below it, to avoid container OOM kills and provide Node.js with a clear boundary for garbage collection.
-   If memory usage exceeds this limit:
    -   Application will crash with "JavaScript heap out of memory" error~~~~
    -   Container will be terminated and Kubernetes will restart the pod

### Pod Disruption Budget (PDB)

The PDB is a Kubernetes resource that limits the number of pods that can be simultaneously unavailable during planned maintenance events.
PDB Prevents all pods from being evicted at once and allows infrastructure updates without service interruption.

#### Configuration

-   **enabled: true** - Activates the Pod Disruption Budget protection mechanism
-   **minAvailable: 50%** - Ensures at least half of the service's pods remain available during voluntary disruptions

### Auto Scaling

Service uses HPA for autoscaling.
HPA(Horizontal Pod Autoscaler) is a Kubernetes resource that automatically adjusts the number of pod replicas based on observed metrics like CPU and memory usage.

#### Basic Settings

-   **enabled: true** - Activates autoscaling for the deployment
-   **minReplicas: 2** - Minimum number of pods that will run at all times
-   **maxReplicas: 10** - Maximum number of pods that can be created during high load
-   **targetCPUUtilizationPercentage: 65** - Triggers scaling when average CPU usage reaches 65% of the limit
-   **targetMemoryUtilizationPercentage: 65** - Triggers scaling when average memory usage reaches 65% of the limit

#### Scale Down Behavior

-   **stabilizationWindowSeconds: 150** - Waits 2.5 minutes before considering scale down to prevent rapid fluctuations
-   **Policy:**
    -   **type: Pods** - Scales down by a specific number of pods
    -   **value: 2** - Removes up to 2 pods at a time
    -   **periodSeconds: 60** - Allows scaling down every minute

#### Scale Up Behavior

-   **stabilizationWindowSeconds: 10** - Short 10-second window to quickly respond to increased load
-   **Policy:**
    -   **type: Percent** - Scales up by percentage of current pods
    -   **value: 100** - Can double the number of pods in a single scaling action
    -   **periodSeconds: 15** - Allows scaling up every 15 seconds
-   **selectPolicy: Max** - When multiple scaling policies exist, chooses the one that scales the most aggressively

This configuration prioritizes quick scaling up to handle traffic spikes while ensuring more conservative scaling down to maintain stability.

### Pod Lifecycle Management

When a pod termination begins, the preStop hook runs first, giving time for:

-   Service mesh to remove the pod from load balancing
-   Completion of in-flight requests
-   Proper connection closing
-   Clean resource release

#### Graceful Termination Settings

-   **prestopSeconds: 10** - Delays termination by 10 seconds
-   **terminationGracePeriodSeconds: 15** - Allows 15 seconds for graceful shutdown

---

## Open Telemetry in Creative Preview

To bundle the creative preview's (HTML page) open telemetry code, run `nx run creative-preview:build-ot.` The output is stored under `assets/ot` `opentelemetry-web.js`.

This is required as OpenTelemetry comes as npm packages and are not bundled automatically.
This way we can ensure we only bundle the required packages and not the entire OpenTelemetry library, which improves the load time a bit.
