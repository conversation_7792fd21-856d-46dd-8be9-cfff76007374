{"name": "creative-preview", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/creative-preview/src", "projectType": "application", "tags": ["type:app", "scope:server", "scope:creative-preview"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/creative-preview", "main": "apps/creative-preview/src/main.ts", "tsConfig": "apps/creative-preview/tsconfig.app.json", "assets": ["apps/creative-preview/src/assets", "apps/creative-preview/src/templates"], "webpackConfig": "apps/creative-preview/webpack.config.js", "generatePackageJson": true}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "creative-preview:build"}, "configurations": {"development": {"inspect": true, "buildTarget": "creative-preview:build:development"}, "production": {"buildTarget": "creative-preview:build:production"}}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/creative-preview/jest.config.ts"}}, "build-ad": {"executor": "nx:run-commands", "options": {"commands": ["pnpm run build:scripts"]}}, "build-ot": {"executor": "@nx/rspack:rspack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "web", "outputPath": "apps/creative-preview/src/assets/ot", "outputFileName": "opentelemetry-web.js", "main": "apps/creative-preview/src/templates/partials/opentelemetry-web.ts", "tsConfig": "apps/creative-preview/tsconfig.app.json", "rspackConfig": "apps/creative-preview/rspack.ot.config.js", "assets": []}, "configurations": {"development": {"mode": "development"}, "production": {"mode": "production", "sourceMap": false}}}, "generate-build-info": {"executor": "nx:run-commands", "options": {"commands": ["pnpm exec nx g @studio/tools:build-info creative-preview --branch={args.branch}"]}}, "docker-prepare": {"dependsOn": ["build-ad", {"target": "generate-build-info", "params": "forward"}, {"target": "build"}], "command": "echo Build Ad, Generate Info and Build CPS Success"}, "docker-build": {"dependsOn": [{"target": "docker-prepare", "params": "forward"}], "command": "docker build -f apps/creative-preview/Dockerfile . -t bannerflow.azurecr.io/studio/cps:local"}}}