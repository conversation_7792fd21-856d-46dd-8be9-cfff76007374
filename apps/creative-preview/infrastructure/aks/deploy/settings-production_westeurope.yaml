env:
    ENVIRONMENT: Production
    STAGE: production
    LOG_LEVEL: info
    PORT: 8080
    OTEL_EXPORTER_OTLP_ENDPOINT: 'https://otlp.eu01.nr-data.net'
    OTEL_SERVICE_NAME: CreativePreviewService
    OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT: 4095
    OTEL_EXPORTER_OTLP_COMPRESSION: gzip
    OTEL_EXPORTER_OTLP_PROTOCOL: http/protobuf
    OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE: delta
    OTEL_TRACES_SAMPLER: traceidratio
    OTEL_TRACES_SAMPLER_ARG: 0.2

ingressnginx:
    enabled: true
    host: api.bannerflow.com
    annotations:
        nginx.ingress.kubernetes.io/proxy-connect-timeout: '300'
    paths:
        - path: /preview/(.*)
          pathType: ImplementationSpecific

networkpolicy:
    enabled: true
    policyname: allow-incoming
    cidr: *********/19 #Always static cidr, belongs to Azure app gateway subnet
    nginxcidr: *********/19 #Always static cidr, belongs to Azure vnet backend subnet
    accessvianamespaces: true
    namespaces:
        - studio
        - creative-generation
        - publishing
        - generation

serviceAccount:
    create: true

service:
    type: ClusterIP
    port: 8080

labels:
    enabled: true
    label:
        azure.workload.identity/use: 'true'

nodeSelector:
    scaling: static

resources:
    requests:
        cpu: 500m
        memory: 900Mi
    limits:
        cpu: 1000m
        memory: 1800Mi

pdb:
    enabled: true
    minAvailable: 50%

autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 10
    targetCPUUtilizationPercentage: 65
    targetMemoryUtilizationPercentage: 65
    behavior:
        scaleDown:
            stabilizationWindowSeconds: 150
            policies:
                - type: Pods
                  value: 2
                  periodSeconds: 60
        scaleUp:
            stabilizationWindowSeconds: 10
            policies:
                - type: Percent
                  value: 100
                  periodSeconds: 15
            selectPolicy: Max

lifecycle:
    prestopSeconds: 10
    terminationGracePeriodSeconds: 15

securityContext:
    runAsUser: 100
    runAsGroup: 101
    allowPrivilegeEscalation: false
    privileged: false
