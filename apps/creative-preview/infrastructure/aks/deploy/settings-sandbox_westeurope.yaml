env:
    ENVIRONMENT: Production
    STAGE: sandbox
    LOG_LEVEL: verbose
    PORT: 8080
    OTEL_EXPORTER_OTLP_ENDPOINT: 'https://otlp.eu01.nr-data.net'
    OTEL_SERVICE_NAME: CreativePreviewService
    OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT: 4095
    OTEL_EXPORTER_OTLP_COMPRESSION: gzip
    OTEL_EXPORTER_OTLP_PROTOCOL: http/protobuf
    OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE: delta
    OTEL_TRACES_SAMPLER: traceidratio
    OTEL_TRACES_SAMPLER_ARG: 0.5

ingressnginx:
    enabled: true
    host: sandbox-api.bannerflow.com
    annotations:
        nginx.ingress.kubernetes.io/proxy-connect-timeout: '300'
    paths:
        - path: /preview/(.*)
          pathType: ImplementationSpecific

networkpolicy:
    enabled: true
    policyname: allow-incoming
    cidr: *********/19 #Always static cidr, belongs to Azure app gateway subnet
    nginxcidr: *********/19 #Always static cidr, belongs to Azure vnet backend subnet
    accessvianamespaces: true
    namespaces:
        - studio
        - creative-generation
        - publishing
        - generation

serviceAccount:
    create: true

service:
    type: ClusterIP
    port: 8080

labels:
    enabled: true
    label:
        azure.workload.identity/use: 'true'

nodeSelector:
    scaling: dynamic

resources:
    requests:
        cpu: 200m
        memory: 200Mi
    limits:
        cpu: 300m
        memory: 600Mi

autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 3
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
    behavior:
        scaleDown:
            stabilizationWindowSeconds: 300
            policies:
                - type: Pods
                  value: 1
                  periodSeconds: 60
        scaleUp:
            stabilizationWindowSeconds: 150
            policies:
                - type: Pods
                  value: 1
                  periodSeconds: 80
            selectPolicy: Max

securityContext:
    runAsUser: 100
    runAsGroup: 101
    allowPrivilegeEscalation: false
    privileged: false

lifecycle:
    prestopSeconds: 10
    terminationGracePeriodSeconds: 15
