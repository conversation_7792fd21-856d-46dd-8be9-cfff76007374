
### Successful test: check response status is 200 of health endpoint
GET https://{{base-url}}{{prefix-path}}{{base-path}}/health/ready

> {%
    client.test("Request executed successfully", function () {
        client.assert(response.status === 200, "Response status is not 200");
    });
%}

###

### Successful test: check response status is 401
GET https://{{base-url}}{{prefix-path}}{{base-path}}/preview-url

{
}

> {%
    client.test("Request executed successfully", function () {
        client.assert(response.status === 401, "Response status is not 401");
    });
%}