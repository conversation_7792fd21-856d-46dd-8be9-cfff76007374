import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { ServeStaticModule } from '@nestjs/serve-static';
import { AzureModule } from '@shared/server/azure';
import { HealthModule } from '@shared/server/health';
import { RequestInterceptor, TrackingModule } from '@shared/server/monitoring';
import { PreviewModule } from '@shared/server/preview';
import { DebugController } from '@shared/server/utils';
import { join } from 'path';
import options from '../environments/options';
import { AppController } from './app.controller';

@Module({
    imports: [
        HttpModule.register({
            timeout: 15000
        }),
        ConfigModule.forRoot({
            isGlobal: true,
            load: [options]
        }),
        ServeStaticModule.forRoot({
            rootPath: join(__dirname, 'assets'),
            serveRoot: '/static'
        }),
        HealthModule.withEndpoints({
            configModule: ConfigModule,
            configService: ConfigService,
            useFactory: (configService: ConfigService) => {
                return configService.get('health.readyEndpoints', { infer: true })!;
            }
        }),
        TrackingModule,
        AzureModule,
        PreviewModule
    ],
    controllers: [AppController, DebugController],
    providers: [
        {
            provide: APP_INTERCEPTOR,
            useClass: RequestInterceptor
        }
    ]
})
export class AppModule {}
