import { NewRelicConfig } from '@domain/acg/monitoring';
import { IBuildInfo } from '@domain/environment';
import {
    IAuth0ClientOptions,
    IAzureBlobServiceOptions,
    IAzureOptions,
    IServerOptions,
    ITokenCacheOptions,
    ServerStage
} from '@domain/server';
import { ServerOrigins } from '@shared/server/environment';
import { HealthReadyEndpoint } from '@shared/server/health';
import { FeatureFlagConfig } from '@shared/server/utils';

export interface ICreativePreviewServiceOptions extends IServerOptions, FeatureFlagConfig {
    origins: ServerOrigins;
    stage: ServerStage;
    creativePreviewSecret: string;
    build: IBuildInfo;
    azureStorage: IAzureBlobServiceOptions;
    azure: IAzureOptions;
    auth0: IAuth0ClientOptions;
    caching: {
        brand: boolean;
        creativeset: boolean;
        snapshot: boolean;
        widget: boolean;
    };
    serviceHostName: string;
    creativePreviewNewRelic: NewRelicConfig;
    health: {
        readyEndpoints: HealthReadyEndpoint[];
    };
    tokenCache: ITokenCacheOptions;
}
