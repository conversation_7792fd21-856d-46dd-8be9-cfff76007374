import { Logger } from '@nestjs/common';
import { ICreativePreviewServiceOptions } from '../cps.types';
import { defaultOptions } from './options.dev';
import { productionOptions } from './options.production';
import { sandboxOptions } from './options.sandbox';

function getOptions(): ICreativePreviewServiceOptions {
    if (process.env.STAGE === 'production') {
        Logger.log('Using production config', 'Configuration');
        return productionOptions;
    }
    if (process.env.STAGE === 'sandbox') {
        Logger.log('Using sandbox config', 'Configuration');
        return sandboxOptions;
    }

    Logger.log('Using dev config', 'Configuration');
    return defaultOptions;
}

export default (): ICreativePreviewServiceOptions => {
    const options = getOptions();
    Logger.debug(options, 'Configuration');
    return options;
};
