import { devOrigins as origins, getPort, ServerOrigins } from '@shared/server/environment';
import { HealthReadyEndpoint } from '@shared/server/health';
import { ICreativePreviewServiceOptions } from '../cps.types';
import { build } from './build-info';

export const defaultOptions: ICreativePreviewServiceOptions = {
    serviceHostName: 'https://api.bannerflow.local/preview', // host name needs to include the protocol
    auth0: {
        uri: process.env.AUTH0_URI || 'https://local-login.bannerflow.com',
        audience: process.env.AUTH0_AUDIENCE || 'https://bannerflow.com/resources/',
        clientId: process.env.AUTH0_CLIENT_ID || 'tnrBOiliglqZWhuozpUOPk9ymU0RNtIx',
        clientSecret:
            process.env.AUTH0_CLIENT_SECRET ||
            'VK2Zr7jPXBtKmLw7BVuzJ79frd2wHXA-K3P89ZbSI4qtD6Ubm_HknKzUWZk2CYhU'
    },
    build,
    inProduction: false,
    inSandbox: false,
    stage: process.env.STAGE || 'local',
    port: getPort(),
    origins,
    azureStorage: {
        accountName: 'devstoreaccount1',
        accountAccessKey:
            'Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==',
        origin: 'http://storage-emulator:10000/devstoreaccount1'
    },
    creativePreviewSecret: process.env.CREATIVE_PREVIEW_SECRET || 'secret',
    caching: {
        brand: process.env.CACHE_BRAND ? process.env.CACHE_BRAND === 'true' : true,
        creativeset: process.env.CACHE_CREATIVESET ? process.env.CACHE_CREATIVESET === 'true' : false,
        snapshot: process.env.CACHE_SNAPSHOT ? process.env.CACHE_SNAPSHOT === 'true' : true,
        widget: process.env.CACHE_WIDGET ? process.env.CACHE_WIDGET === 'true' : true
    },
    azure: {
        appConfigUrl: 'https://bf-shared-sandbox-ac.azconfig.io',
        appConfigConnectionString:
            'Endpoint=https://bf-shared-sandbox-ac.azconfig.io;Id=+d6H-l8-s0:ZDsMi4u4B3bL3Y9VAexD;Secret=kycnl5jmUghCUDu70c/LdKSinLqeEtkZE08EvTEsLK4=',
        keyVaultUrl: 'https://bf-shared-sandbox-kv.vault.azure.net'
    },
    creativePreviewNewRelic: {
        enabled: false
    },
    health: {
        readyEndpoints: getHealthReadyEndpoints(origins)
    },
    tokenCache: {
        cacheKeyPrefix: 'cps',
        redis: {
            enabled: false
        }
    },
    unleash: {
        enabled: false,
        url: 'https://bf-feature-flags.azurewebsites.net/api',
        auth: 'default:development.unleash-insecure-api-token'
    }
};

export function getHealthReadyEndpoints(_origins: ServerOrigins): HealthReadyEndpoint[] {
    return [
        {
            name: 'SAPI',
            url: `${_origins.sapi.replace(/\/api$/, '')}/health/live`
        },
        {
            name: 'AAS',
            url: `${_origins.accountAccessService}/health/live`
        },
        {
            name: 'FM',
            url: `${_origins.fontManagerApi}/health/live`
        }
    ];
}
