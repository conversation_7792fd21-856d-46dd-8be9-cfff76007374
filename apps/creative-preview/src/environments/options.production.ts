import { productionOrigins as origins } from '@shared/server/environment';
import { ICreativePreviewServiceOptions } from '../cps.types';
import { defaultOptions, getHealthReadyEndpoints } from './options.dev';

export const productionOptions: ICreativePreviewServiceOptions = {
    ...defaultOptions,
    inProduction: true,
    serviceHostName: process.env.HOST_NAME || 'https://api.bannerflow.com/preview',
    azureStorage: {
        ...defaultOptions.azureStorage,
        accountName: process.env.AZ_STORAGE_NAME || 'bfstudio',
        origin: process.env.AZ_STORAGE_ORIGIN || 'https://bfstudio.blob.core.windows.net'
    },
    caching: {
        brand: true,
        creativeset: false,
        snapshot: true,
        widget: true
    },
    azure: {
        appConfigUrl: 'https://bf-shared-ac.azconfig.io',
        keyVaultUrl: 'https://bf-shared-kv.vault.azure.net',
        secrets: {
            'auth0.clientId': 'cps-auth0-client-id',
            'auth0.clientSecret': 'cps-auth0-client-secret',
            'azure.appConfigConnectionString': 'cps-appconfig-connectionstring',
            creativePreviewSecret: 'cps-creative-preview-secret'
        }
    },
    origins,
    auth0: {
        ...defaultOptions.auth0,
        uri: process.env.AUTH0_DOMAIN || 'https://login.bannerflow.com'
    },
    creativePreviewNewRelic: {
        enabled: true,
        accountID: '4122654',
        agentID: '*********',
        applicationID: '*********',
        trustKey: '4122654',
        licenseKey: 'NRJS-bd28b6acdc31b77b97c'
    },
    health: {
        readyEndpoints: getHealthReadyEndpoints(origins)
    },
    tokenCache: {
        ...defaultOptions.tokenCache,
        redis: {
            ...defaultOptions.tokenCache.redis,
            enabled: true,
            host:
                process.env.CLIENT_CREDENTIALS_REDIS_HOST ||
                'bf-identity-westeurope.westeurope.redisenterprise.cache.azure.net',
            port: 10000,
            password: process.env.CLIENTCREDENTIALS__REDIS__KEY
        }
    },
    unleash: {
        ...defaultOptions.unleash,
        enabled: false
    }
};
