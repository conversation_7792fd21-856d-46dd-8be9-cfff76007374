console.time('bootstrap');
console.time('OpenTelemetry');
// NOTE: OpenTelemetry has to be the first import
import '@shared/server/monitoring/instrumentation';
console.timeEnd('OpenTelemetry');

console.time('main.ts imports');
// rest of app imports
import { fastifyReplyFrom } from '@fastify/reply-from';
import fastifyView from '@fastify/view';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { ScriptService } from '@shared/server/scripts';
import { FastifyReply, FastifyRequest } from 'fastify';
import hbs from 'hbs';
import { join } from 'node:path';
import { AppModule } from './app/app.module';
console.timeEnd('main.ts imports');

async function bootstrap(): Promise<void> {
    console.timeEnd('bootstrap');

    Logger.log('creating App', 'Bootstrap');
    const app = await NestFactory.create<NestFastifyApplication>(
        AppModule,
        new FastifyAdapter({
            logger: process.env.STAGE === 'local' // disable in production to avoid spamming newrelic and making Tommy unhappy
        })
    );
    app.enableCors();

    const configService = app.get<ConfigService>(ConfigService);
    const scriptService = app.get<ScriptService>(ScriptService);
    await scriptService.initializeScripts();
    const cdn = configService.getOrThrow<string>('origins.cdn');
    const port = configService.getOrThrow<number>('port');

    await registerCdnRedirect(app, cdn);
    await registerTemplates(app);

    await app.listen(port, '::');
    Logger.log(`🚀 Creative Preview is running on: ${await app.getUrl()}`, 'Bootstrap');
}

bootstrap().catch(e => {
    Logger.error(`Failed to bootstrap, due to ${e}`, 'Bootstrap');
    process.exit(1);
});

async function registerCdnRedirect(app: NestFastifyApplication, cdn: string): Promise<void> {
    await app.register(fastifyReplyFrom, {
        base: cdn
    });
    app.getHttpAdapter().get('/scripts/*', (req: FastifyRequest, rep: FastifyReply) => {
        const path = (req.params as { '*': string })['*']; // extract the rest of the URL
        rep.from(`${cdn}/scripts/${path}`);
    });
}

async function registerTemplates(app: NestFastifyApplication): Promise<void> {
    await app.register(fastifyView, {
        engine: {
            handlebars: hbs
        },
        root: join(__dirname, 'templates'),
        viewExt: 'hbs'
    });

    hbs.registerPartials(join(__dirname, 'templates/partials'));
    hbs.registerHelper('json', (object: unknown) => JSON.stringify(object));
}
