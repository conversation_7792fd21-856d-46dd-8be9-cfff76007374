import type { IBannerflowResources } from '@domain/ad/bannerflow-window';
import type { ServerStage } from '@domain/server';

declare global {
    interface Window {
        _bannerflow?: IBannerflowResources;
        adTag: HTMLScriptElement;
    }

    interface Document {
        caretPositionFromPoint: CaretPosition;
    }

    namespace NodeJS {
        interface ProcessEnv {
            NODE_ENV: string;
            LOG_LEVEL?: string;
            STAGE?: ServerStage;

            [key: string]: string | undefined;

            // add more environment variables and their types here
        }
    }
}
