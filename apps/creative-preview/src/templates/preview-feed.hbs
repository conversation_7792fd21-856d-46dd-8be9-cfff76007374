<!DOCTYPE html>
<html>
<head>
    <title>Creative Preview - Feed</title>
    {{> meta meta=meta}}

    {{> styles }}

    {{> newrelic newrelic=newrelic}}

    {{> preview_init build=build environment=environment release=release}}

    {{> console_utils}}
    {{> cirs_utils}}
</head>
<body>

{{> ad src=src }}
<script>
    const injectedData = {{{json data}}};
    const dynamicProperties = injectedData.dynamicProperties;
    let adApi;

    adTag.addEventListener('render', function() {
        adApi = adTag.adApi;

        const bannerflowCreative = window._bannerflow.ads[0].creativeLoader.creative;
        if (!bannerflowCreative) {
            throw new Error('Bannerflow creative not found.');
        }
        console.log('dynamicProperties', dynamicProperties);

        if (dynamicProperties) {
            subscribeToConsole();
            loopOverDynamicProperties(dynamicProperties);
        }
    });

    adTag.addEventListener('error', function(event) {
        // event.data is emitted from the ad.js
        // If the event does not have data, it was a native script error, indicating something went wrong while loading
        const errorMessage = event.data?.event ?? 'Failed to load ad.js';
        const error = new Error(errorMessage);
        error.name = 'AdError';
        console.log('feed error handler', error);

        // TODO: what if we end up here while updating the dynamic property?
    });

    async function loopOverDynamicProperties(dynamicPropertiesCatalog) {
        console.log(`Dynamic data rendering started for ${dynamicPropertiesCatalog.length} items`);
        await waitForCIRSReady();

        for (let index = 0; index < dynamicPropertiesCatalog.length; index++) {
            const { id, values } = dynamicPropertiesCatalog[index];
            // 1. update creative with dynamic data
            try {
                console.groupCollapsed(`Loop i[${index}] [${id}] - length: ${values.length}`);

                performance.mark(`render-start-${id}`);
                validateDynamicProperties(values);
                await adApi.renderDynamicProperties(values);
                performance.measure(`render-${id}`, `render-start-${id}`);
            } catch (e) {
                console.warn('setLoop:', e);
                const error = new Error(e.message ?? 'Failed to update dynamic property');
                error.name = 'AdError';
                error.stack = e.stack;
                newrelic?.noticeError(error, { catalogId: id, message: e.message });
                notifyCIRSError(error.message, { feedItemId: id });
                console.groupEnd();
                continue;
            }

            // 2. wait 1 second for rendering to complete, then notify CIRS to take screenshot
            await new Promise(resolve => setTimeout(resolve, 1000));
            const warnings = getWarnings(values);
            console.log('warnings: ', warnings);
            notifyCIRSScreenshot({ feedItemId: id, warnings });

            // 3. wait until CIRS has taken the screenshot and is ready again
            try {
                performance.mark(`CIRS-wait-start-${index}`);
                await waitForCIRSReady();
                performance.measure(`CIRS-wait-${index}`, `CIRS-wait-start-${index}`);
            } catch (e) {
                console.warn('waitForCIRSReady:', e);
                e.name = 'CIRSError';
                newrelic?.noticeError(e, { catalogId: id, message: e.message });
                notifyCIRSError(e.message, { feedItemId: id });
                console.groupEnd();
                continue;
            }

            console.groupEnd();
        }

        console.log(`dynamic data rendering done`);

        // 4. inform CIRS that all screenshots are done
        notifyCIRSDone();

        console.log('stopping the ad');
        adApi.stop();

        // 5. print performance
        console.table(performance.getEntriesByType('measure'));
    }

    function validateDynamicProperties(dynamicProperties) {
        if (!Array.isArray(dynamicProperties)) {
            throw new Error('Dynamic properties must be an array');
        }
        if (dynamicProperties.length === 0) {
            throw new Error('Dynamic properties must not be empty');
        }
        if (dynamicProperties.some(prop => !prop?.id )) {
            throw new Error('Dynamic properties must have an id');
        }
    }

    function getWarnings(dynamicProperties) {
        return dynamicProperties.filter(property =>  !property.value ).map(property => {
            return {
                id: property.id,
                type: 'missingValue',
                message: 'Value is missing'
            };
        });
    }
</script>
</body>
</html>
