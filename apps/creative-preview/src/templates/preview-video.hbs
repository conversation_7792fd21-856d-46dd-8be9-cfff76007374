<!DOCTYPE html>
<html>
<head>
    <title>Creative Preview - Video Export</title>
    {{> meta meta=meta}}

    {{> styles }}

    {{> newrelic newrelic=newrelic}}

    {{> preview_init build=build environment=environment release=release}}

    <style>
        :root {
            --video-pixel-stopped: black;
            --video-pixel-playing: transparent;
            --pixel-size: 4px;
        }

        div.video-pixel {
            width: var(--pixel-size);
            height: var(--pixel-size);
            background-color: var(--video-pixel-stopped);
            display: none;
        }

        div.video-pixel.playing {
            background-color: var(--video-pixel-playing);
        }
    </style>
</head>
<body>
<div class="video-pixel"></div>

{{> ad src=src debug=debug }}

<script>
    const VRS_DELAY = parseInt(params.get('vrs-delay') || '15');

    function initVideoGenerator(adTag) {
        const root = document.querySelector(':root');

        if (params.get('vrs-pixel-size')) {
            const pixelSize = params.get('vrs-pixel-size');
            root.style.setProperty('--pixel-size', `${pixelSize}px`);
        }
        if (params.get('vrs-pixel-color-playing')) {
            const playingColor = params.get('vrs-pixel-color-playing');
            root.style.setProperty('--video-pixel-playing', playingColor);
        }
        if (params.get('vrs-pixel-color-stop')) {
            const stopColor = params.get('vrs-pixel-color-stop');
            root.style.setProperty('--video-pixel-stopped', stopColor);
        }

        const videoPixelElement = document.querySelector('.video-pixel');
        videoPixelElement.style.display = 'block';

        adTag.addEventListener('creativeload', () => {
            // unmute widgets
            adTag['adApi'].mute(false);
        });

        adTag.addEventListener('play', () => {
            videoPixelElement.classList.add('playing');
        });

        adTag.addEventListener('pause', () => {
            videoPixelElement.classList.remove('playing');
        });
    }

    function playCreative() {
        const videoPixelElement = document.querySelector('.video-pixel');
        videoPixelElement.classList.add('playing');
        setTimeout(() => {
            window._bannerflow.ads[0].creativeLoader.creative.animator.play();
        }, VRS_DELAY);
    }

    initVideoGenerator(document.querySelector(STUDIO_AD_SELECTOR));
</script>
</body>
</html>
