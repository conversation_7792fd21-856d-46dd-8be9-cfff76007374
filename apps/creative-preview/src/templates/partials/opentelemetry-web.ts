import { metrics, trace } from '@opentelemetry/api';
import { logs } from '@opentelemetry/api-logs';
import {
    CompositePropagator,
    W3CBaggagePropagator,
    W3CTraceContextPropagator
} from '@opentelemetry/core';
// using HTTP exporters as PROTO is very big atm
// https://github.com/open-telemetry/opentelemetry-js/issues/4817#issuecomment-2186027639
import { OTLPLogExporter } from '@opentelemetry/exporter-logs-otlp-http';
import { OTLPMetricExporter } from '@opentelemetry/exporter-metrics-otlp-http';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { registerInstrumentations } from '@opentelemetry/instrumentation';
import { DocumentLoadInstrumentation } from '@opentelemetry/instrumentation-document-load';
import { FetchInstrumentation } from '@opentelemetry/instrumentation-fetch';
import { resourceFromAttributes } from '@opentelemetry/resources';
import { BatchLogRecordProcessor, LoggerProvider } from '@opentelemetry/sdk-logs';
import { MeterProvider, PeriodicExportingMetricReader } from '@opentelemetry/sdk-metrics';
import {
    BatchSpanProcessor,
    ParentBasedSampler,
    TraceIdRatioBasedSampler
} from '@opentelemetry/sdk-trace-base';
import { WebTracerProvider } from '@opentelemetry/sdk-trace-web';
import { ATTR_SERVICE_NAME, ATTR_SERVICE_VERSION } from '@opentelemetry/semantic-conventions';
import { ConsoleInstrumentation } from './opentelemetry-instrumentation-console';
import { CreativeInstrumentation } from './opentelemetry-instrumentation-creative';

// @ts-expect-error - OT_API_KEY is global
const API_KEY = OT_API_KEY;
const samplingRate = 0.1;

// Describes the entity in NewRelic
const resourceSettings = resourceFromAttributes({
    [ATTR_SERVICE_NAME]: 'Creative Preview',
    [ATTR_SERVICE_VERSION]: '1.0.0'
});

const newRelicLogExporter = new OTLPLogExporter({
    url: 'https://otlp.eu01.nr-data.net/v1/logs',
    headers: {
        'api-key': API_KEY
    },
    concurrencyLimit: 1 // an optional limit on pending requests
});
const loggerProvider = new LoggerProvider({
    resource: resourceSettings
});
loggerProvider.addLogRecordProcessor(new BatchLogRecordProcessor(newRelicLogExporter));
logs.setGlobalLoggerProvider(loggerProvider);

const newRelicTraceExporter = new OTLPTraceExporter({
    url: 'https://otlp.eu01.nr-data.net/v1/traces',
    headers: {
        'api-key': API_KEY
    }
});
const tracerProvider = new WebTracerProvider({
    resource: resourceSettings,
    sampler: new ParentBasedSampler({
        root: new TraceIdRatioBasedSampler(samplingRate)
    }),
    spanProcessors: [
        new BatchSpanProcessor(
            newRelicTraceExporter,
            // Optional BatchSpanProcessor Configurations
            {
                // The maximum queue size. After the size is reached spans are dropped.
                maxQueueSize: 100,
                // The maximum batch size of every export. It must be smaller or equal to maxQueueSize.
                maxExportBatchSize: 50,
                // The interval between two consecutive exports
                scheduledDelayMillis: 500,
                // How long the export can run before it is cancelled
                exportTimeoutMillis: 15000
            }
        )
    ]
});

tracerProvider.register({
    // Configure the propagator to enable context propagation between services using the W3C Trace Headers
    propagator: new CompositePropagator({
        propagators: [new W3CBaggagePropagator(), new W3CTraceContextPropagator()]
    })
});
trace.setGlobalTracerProvider(tracerProvider);

const metricCollectorOptions = {
    url: 'https://otlp.eu01.nr-data.net/v1/metrics',
    headers: {
        'api-key': API_KEY
    }
};

const meterProvider = new MeterProvider({
    resource: resourceSettings,
    readers: [
        new PeriodicExportingMetricReader({
            exporter: new OTLPMetricExporter(metricCollectorOptions)
        })
    ]
});
metrics.setGlobalMeterProvider(meterProvider);

// flush all metric when browser is closed
document.onvisibilitychange = (): void => {
    if (document.visibilityState === 'hidden') {
        meterProvider.shutdown();
    }
};

// Uncomment this to enable debugging using consoleExporter
// diag.setLogger(new DiagConsoleLogger());
// loggerProvider.addLogRecordProcessor(new SimpleLogRecordProcessor(new ConsoleLogRecordExporter()));
// tracerProvider.addSpanProcessor(new SimpleSpanProcessor(new ConsoleSpanExporter()));

registerInstrumentations({
    loggerProvider,
    tracerProvider,
    meterProvider,
    instrumentations: [
        new ConsoleInstrumentation(),
        new DocumentLoadInstrumentation({
            applyCustomAttributesOnSpan: {
                documentLoad: (span): void => {
                    // add all query params to document load span
                    const params = new URLSearchParams(window.location.search);
                    params.forEach((value, key) => {
                        if (key === 'snapshot') {
                            span.setAttribute('bf.creative_set.snapshot.id', value);
                        } else {
                            span.setAttribute(`query.${key}`, value);
                        }
                    });
                }
            }
        }),
        new FetchInstrumentation({
            clearTimingResources: true,
            propagateTraceHeaderCorsUrls: [/.+/g]
        }),
        new CreativeInstrumentation({ adSelector: '.studio-ad' })
    ]
});
