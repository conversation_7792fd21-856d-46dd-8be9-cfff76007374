<script>
    let consoleMessageList = [];

    function subscribeToConsole() {
        const consoleLog = console.log;
        console.log = function(message) {
            consoleLog.apply(console, arguments);
            consoleMessageList.push(message);
        };
    }

    function waitForConsoleMsg(expectedMessage, timeout = 10000) {
        console.log('Waiting for:', expectedMessage);
        return new Promise((resolve, reject) => {
            if (consoleMessageList.includes(expectedMessage)) {
                removeMsgFromList(expectedMessage);
                resolve();
                return;
            }

            let timeoutId;

            const intervalId = setInterval(() => {
                if (consoleMessageList.includes(expectedMessage)) {
                    removeMsgFromList(expectedMessage);
                    clearInterval(intervalId);
                    clearTimeout(timeoutId);
                    resolve();
                }
            }, 50);

            timeoutId = setTimeout(() => {
                clearInterval(intervalId);
                reject(new Error(`Message "${expectedMessage}" timed out after ${timeout}ms`));
            }, timeout);
        });
    }

    function removeMsgFromList(message) {
        consoleMessageList = consoleMessageList.filter(msg => msg !== message);
    }
</script>
