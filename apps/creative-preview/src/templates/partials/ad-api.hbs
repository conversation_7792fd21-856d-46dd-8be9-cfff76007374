<script type="application/javascript">
    function registerAdApi(adScript) {
        adScript.addEventListener('error', event => {
            const errorMessage = event.data?.event ?? 'Failed to load ad.js';
            const error = new Error(errorMessage);
            error.name = 'AdError';
            window.parent.postMessage({
                event: 'error',
                data: error
            }, '*');
        });

        adScript.addEventListener('render', () => {
            const adApi = adScript.adApi;
            window.parent.postMessage({
                event: 'render'
            }, '*');

            window.addEventListener('message', event => {
                const message = event.data;
                const action = message.cmd?.action;

                if (message.type !== 'cmd' || !action) {
                    return;
                }

                switch (action) {
                    case 'play':
                        adApi.play();
                        break;
                    case 'pause':
                        adApi.pause();
                        break;
                    case 'stop':
                        adApi.stop();
                        break;
                    case 'seek':
                        if (message.cmd.hasOwnProperty('time')) {
                            adApi.seek(message.cmd.time);
                        }
                        break;
                    case 'mute':
                        if (message.cmd.hasOwnProperty('isMuted')) {
                            adApi.mute(message.cmd.isMuted);
                        }
                        break;
                    case 'renderDynamicProperties':
                        if (message.cmd.hasOwnProperty('dynamicProperties')) {
                            adApi.renderDynamicProperties(message.cmd.dynamicProperties);
                        }
                        break;

                    default:
                        console.warn('Unknown ad Api action:', action);
                }
            });
        });
    }
</script>
