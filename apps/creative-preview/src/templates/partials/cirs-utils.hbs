<script>
    const CIRS_READY_CMD = 'CirsReady';
    const CIRS_READY_TIMEOUT = 10000;

    function waitForCIRSReady() {
        return waitForConsoleMsg(CIRS_READY_CMD, CIRS_READY_TIMEOUT);
    }

    function notifyCIRSScreenshot(context = {}) {
        notifyCIRS('Screenshot', context);
    }

    function notifyCIRSError( error, context = {}) {
        notifyCIRS('ScreenshotError', { error, ...context });
    }

    function notifyCIRSDone() {
        notifyCIRS('ScreenshotsDone');
    }

    function notifyCIRS(cmd, context = {}) {
        const message = {
            cmd,
            ...context
        };
        console.log(JSON.stringify(message));
    }
</script>
