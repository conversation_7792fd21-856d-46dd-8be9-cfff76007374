<!-- script>
    // Uncomment this for allowing setting the pixel size from URL param
    function changePixelSize(){
        const newVideoPixelSize = params.get('video-pixel-size');
        if (newVideoPixelSize) {
            const videoPixelElement = document.querySelector('div.video-pixel');
            videoPixelElement.style.setProperty('--pixel-size', newVideoPixelSize);
        }
    }
    changePixelSize();
</script-->
<!-- <script>
            // Uncomment to test race condition for initialization
            setTimeout(() => {
                document.querySelector('.ad-wrapper').innerHTML = '';
            });
        </script> -->

<!-- Iframe for <head> injection testing -->
<!--         <iframe style="border: none;" width="creative-width" height="creative-height" class="iframe" srcdoc='<html><head><script class="studio-ad" src="{{{src}}}" async></script></head><body><div>not empty body</div></body></html>'></iframe>-->

<!-- Sandboxed iframe for Google Ads testing -->
<!-- This means that _inSandboxedIframe === true -->
<!-- <iframe class="iframe" srcdoc='<html><body><script class="studio-ad" src="{{{src}}}" async></script></body></html<>' width="300" height="250" scrolling="no" sandbox="allow-scripts"></iframe> -->

<!-- TCF (GDPR API) iframe test -->
<!-- <iframe class="iframe" srcdoc='<iframe name="__tcfapiLocator" width="0" height="0" frameborder="0" scrolling="no"></iframe><script class="studio-ad" src="{{{src}}}" async></script>' width="300" height="250" scrolling="no"></iframe> -->

<!-- Adform iframe -->
<!-- <iframe
            class="iframe"
            width="300"
            height="250"
            src="javascript:'';"
            onload="eval(this.getAttribute('data-onload'))"
            allowtransparency="true"
            webkitallowfullscreen=""
            mozallowfullscreen=""
            allowfullscreen=""
            marginwidth="0"
            marginheight="0"
            hspace="0"
            vspace="0"
            frameborder="0"
            scrolling="no"
            data-onload="(function C(a,c){function d(){c?g.open():g.open(&quot;text/html&quot;,&quot;replace&quot;);g.write(b);f.__rendered__=!0}var b=a.getAttribute(&quot;data-contents&quot;),f=a.contentWindow,g=f.document,p=f.setTimeout;-1==a.offsetHeight||f.__rendered__||(f.__rendered__=!0,c?d():p(d,0))})(this,false)"
            data-contents="<!DOCTYPE html><head><title>ad</title></head><body><script class='studio-ad' src='http://api.bannerflow.local/acg{{{src}}}'></script></body>">
        </iframe> -->


<script>
    // "use strict";
    // var AdScript = /** @class */ (function () {
    //     console.log('--- CUSTOM AD SCRIPT INITIALIZED ---')
    //     function AdScript(ad) {
    //         ad.once('click', function (e) {
    //             console.log('customEvent.click', e)
    //         });
    //         ad.once('creativeload', function (e) {
    //             console.log('creativeload', e)
    //         });
    //     }

    //     AdScript.prototype.overrideOpen = function(targetUrl, ad) {
    //         console.log('open occurred');
    //         return false;
    //     };

    //     AdScript.prototype.overrideRedirect = function (redirectUrl, targetUrl, ad) {
    //         console.log('AdScript.overrideRedirect', redirectUrl, targetUrl)
    //         return redirectUrl;
    //     };
    //     console.log(AdScript.prototype)
    //     return AdScript;
    // }());
    // window._bannerflow = window._bannerflow || {};
    // window._bannerflow.adScripts = window._bannerflow.adScripts || [];
    // window._bannerflow.adScripts.push(AdScript);
</script>

<script>
    // var newDynamicContent = [
    //     {
    //         ProductId: {
    //             value: 1,
    //             targetUrl: "https://www.myphones.com/product/1"
    //         },
    //         Price: {
    //             value: 7999,
    //             targetUrl: "https://www.myphones.com/product/1"
    //         },
    //         Image: {
    //             value: "https://play.bannerflow.com/images/phone1.png",
    //             targetUrl: "https://www.myphones.com/product/1"
    //         },
    //         Product: {
    //             value: "Apple iPhone 8",
    //             targetUrl: "https://www.myphones.com/product/1"
    //         }
    //     },
    //     {
    //         ProductId: {
    //             value: 2,
    //             targetUrl: "https://www.myphones.com/product/2"
    //         },
    //         Price: {
    //             value: "$399",
    //             targetUrl: "https://www.myphones.com/product/2"
    //         },
    //         Image: {
    //             value: "https://play.bannerflow.com/images/phone4.png",
    //             targetUrl: "https://www.myphones.com/product/2"
    //         },
    //         Product: {
    //             value: "Samsung Galaxy Note 8",
    //             targetUrl: "https://www.myphones.com/product/2"
    //         }
    //     }
    // ];
    // Uncomment to test e.g DCO
    // window._bannerflow = { overriddenFeedData: newDynamicContent };


    window.__tcfapi = function(method, tcfVersionId, callback, vendorIds) {
        callback({
            'cmpId': 23
        }, true);
    };

    // Emulates a CMP frame
    window.addEventListener('message', (event) => {
        let json;

        try {
            json = event.data && event.data.__tcfapiCall;
        } catch (_) {
        }

        if (!json || !json.callId) {
            return;
        }

        const tcfapiReturn = {
            __tcfapiReturn: {
                callId: json.callId,
                returnValue: {
                    'cmpId': 23
                },
                success: true
            }
        };

        event.source.postMessage(tcfapiReturn, '*');
    }, false);

</script>

<style>
    /* To test _isPixelVisible */
    /* @media (max-width: 300px) {
        body {
            display: none;
        }
    } */
</style>