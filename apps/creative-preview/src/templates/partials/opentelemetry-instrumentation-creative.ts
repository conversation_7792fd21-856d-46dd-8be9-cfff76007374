import {
    Attributes,
    context,
    Counter,
    propagation,
    ROOT_CONTEXT,
    Span,
    SpanKind,
    SpanStatusCode,
    trace
} from '@opentelemetry/api';
import { InstrumentationBase, InstrumentationConfig } from '@opentelemetry/instrumentation';
import {
    findEntryInEntries,
    getPerformanceCreativeEntries,
    getTraceParent,
    performanceCreativeNames
} from './utils';

/**
 * CreativePlugin Config
 */
export interface CreativeInstrumentationConfig extends InstrumentationConfig {
    adSelector: string;
}

// inspiration from:
// https://github.com/open-telemetry/opentelemetry-js-contrib/blob/main/plugins/web/opentelemetry-instrumentation-document-load/src/instrumentation.ts#L112
export class CreativeInstrumentation extends InstrumentationBase<CreativeInstrumentationConfig> {
    private _adTag: HTMLScriptElement | null;
    private _adCounter: Counter<Attributes>;

    constructor(config: CreativeInstrumentationConfig) {
        super('@bannerflow/instrumentation-creative', '0.1.0', config);

        this._adCounter = this.meter.createCounter('Ad/renders', {
            description: 'Ad render count'
        });
    }

    // eslint-disable-next-line @typescript-eslint/no-empty-function
    override init(): void {}

    override enable(): void {
        this._init();
    }

    override disable(): void {
        this._adTag?.removeEventListener('render', this._onAdRendered);
        this._adTag?.removeEventListener('error', this._onAdError);
    }

    /**
     * callback to be executed when page is loaded
     */
    private _onAdRendered(): void {
        this._diag.verbose('ad rendered');
        this._adCounter.add(1);
        this._collectPerformance();
    }

    private _onAdError(err): void {
        this._diag.verbose('ad error', err);
        const traceparent = getTraceParent();
        context.with(propagation.extract(ROOT_CONTEXT, { traceparent }), () => {
            const errorSpan = this._startSpan('AdError');
            if (!errorSpan) {
                return;
            }

            // If the event does not have data, it was a native script error, indicating something went wrong while loading
            const errorMessage = err.data?.event ?? 'Failed to load ad.js';
            const error = new Error(errorMessage);
            error.name = 'AdError';

            errorSpan.setStatus({ code: SpanStatusCode.ERROR, message: errorMessage });
            errorSpan.recordException(error);
            errorSpan.end();
        });
    }

    /**
     * Collects information about performance and creates appropriate spans
     */
    private _collectPerformance(): void {
        const entries = getPerformanceCreativeEntries();
        const traceparent = getTraceParent();
        context.with(propagation.extract(ROOT_CONTEXT, { traceparent }), () => {
            const rootSpan = this._startTimedSpan('AdRender', 'ad:tag', entries);
            if (!rootSpan) {
                return;
            }

            const creative = window._bannerflow!.ads![0].selectedCreative;
            rootSpan.setAttribute('bf.brand.id', creative.brand.id);
            rootSpan.setAttribute('bf.creative_set.id', creative.creativeset.id);
            rootSpan.setAttribute('bf.creative_set.name', creative.creativeset.name!);
            rootSpan.setAttribute('bf.creative.id', creative.id);
            rootSpan.setAttribute('bf.design.id', creative.design.id);
            rootSpan.setAttribute('bf.size.id', creative.size.id);
            rootSpan.setAttribute('bf.version.id', creative.version.id);

            this._addCreativePerformanceSpans(rootSpan, entries);

            this._endSpan(rootSpan, 'ad:render', entries);
        });
    }

    /**
     * Creates and ends a span with creative information about performance added as timed events
     * @param parentSpan
     * @param performanceEntries
     */
    private _addCreativePerformanceSpans(
        parentSpan: Span,
        performanceEntries: PerformanceEntryList
    ): void {
        performanceCreativeNames.forEach(eventName => {
            const span = this._startTimedSpan(
                eventName,
                `${eventName}:start`,
                performanceEntries,
                parentSpan
            );
            if (span) {
                this._endSpan(span, `${eventName}:end`, performanceEntries);
            }
        });
    }

    /**
     * Helper function for starting a span
     * @param spanName name of span
     * @param performanceName name of performance entry for time start
     * @param entries
     * @param parentSpan
     */
    private _startTimedSpan(
        spanName: string,
        performanceName: string,
        entries: PerformanceEntryList,
        parentSpan?: Span
    ): Span | undefined {
        const entry = findEntryInEntries(entries, performanceName);
        if (entry) {
            const span = this.tracer.startSpan(
                spanName,
                {
                    kind: SpanKind.CLIENT,
                    startTime: entry.startTime
                },
                parentSpan ? trace.setSpan(context.active(), parentSpan) : undefined
            );
            return span;
        }
        return undefined;
    }

    private _startSpan(spanName: string, parentSpan?: Span): Span | undefined {
        return this.tracer.startSpan(
            spanName,
            {
                kind: SpanKind.CLIENT
            },
            parentSpan ? trace.setSpan(context.active(), parentSpan) : undefined
        );
    }

    /**
     * Helper function for ending span
     * @param span
     * @param performanceName
     * @param entries
     */
    private _endSpan(
        span: Span | undefined,
        performanceName: string,
        entries: PerformanceEntryList
    ): void {
        // span can be undefined when entries are missing the certain performance - the span will not be created
        if (span) {
            const entry = findEntryInEntries(entries, performanceName);
            if (entry) {
                span.end(entry.startTime);
            } else {
                // just end span
                span.end();
            }
        }
    }

    /**
     * Registers 'render' and 'error' event listeners on the ad tag
     * executes callback {_onAdRendered} when the page is loaded
     */
    private _initAdEventListeners(): void {
        const hasAdRendered = !!document.querySelector('script[data-rendered="true"]');
        this._onAdRendered = this._onAdRendered.bind(this);
        this._onAdError = this._onAdError.bind(this);

        if (hasAdRendered) {
            this._onAdRendered();
        } else {
            this._adTag?.addEventListener('render', this._onAdRendered);
        }

        this._adTag?.addEventListener('error', this._onAdError);
    }

    private _init(): void {
        this._diag.info('_init');

        this._adTag = document.querySelector<HTMLScriptElement>(this._config.adSelector);
        if (!this._adTag) {
            this._diag.error('Ad tag not found');
            return;
        }
        // remove previously attached load to avoid adding the same event twice
        // in case of multiple enable calling.
        this._adTag.removeEventListener('render', this._onAdRendered);
        this._adTag.removeEventListener('error', this._onAdError);

        this._initAdEventListeners();
    }
}
