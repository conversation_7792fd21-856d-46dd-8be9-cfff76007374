import { TRACE_PARENT_HEADER } from '@opentelemetry/core';

export const getPerformanceCreativeEntries = (): PerformanceEntryList => {
    const performanceCreativeTiming = performance.getEntriesByType('mark');
    return performanceCreativeTiming;
};

export const performanceCreativeNames = [
    'ad:init',
    'cl:load-modules',
    'cl:init-creative',
    'ac:init',
    'ac:preload:fonts',
    'ac:preload:feeds',
    'ac:init:load:features',
    'ac:init:load:elements',
    'ac:init:load:widgets',
    'ac:init:render',
    'ac:init:rerender'
];

export function findEntryInEntries(
    entries: PerformanceEntryList,
    name: string
): PerformanceEntry | undefined {
    return entries.find(entry => entry.name === name);
}

export function getTraceParent(): string {
    const metaElement = Array.from(document.getElementsByTagName('meta')).find(
        e => e.getAttribute('name') === TRACE_PARENT_HEADER
    );
    return (metaElement && metaElement.content) || '';
}
