import type { Attributes, Counter, SpanContext } from '@opentelemetry/api';
import { SeverityNumber } from '@opentelemetry/api-logs';
import { parseTraceParent } from '@opentelemetry/core';
import { InstrumentationBase, InstrumentationConfig } from '@opentelemetry/instrumentation';
import { getTraceParent } from './utils';

/**
 * ConsolePlugin Config
 */
export interface ConsoleInstrumentationConfig extends InstrumentationConfig {
    logLevels: ConsoleMethod[];
}

type ConsoleMethod = 'log' | 'warn' | 'error' | 'info' | 'debug';
type ConsoleMethodSignature = (...args: any[]) => void;

/**
 * Console instrumentation for logging
 * @see https://github.com/open-telemetry/opentelemetry-js/blob/main/experimental/examples/logs/index.ts
 * inspiration https://github.com/open-telemetry/opentelemetry-js/pull/5050/files
 */
export class ConsoleInstrumentation extends InstrumentationBase<ConsoleInstrumentationConfig> {
    private _originalConsole: Console = { ...console };
    private _errorMeter: Counter<Attributes>;

    constructor(config: ConsoleInstrumentationConfig = { logLevels: ['log', 'warn', 'error'] }) {
        super('@bannerflow/instrumentation-console', '0.1.0', config);

        this._errorMeter = this.meter.createCounter('PreviewErrors/all', {
            description: 'Count of preview errors'
        });
    }

    override enable(): void {
        const traceparent = parseTraceParent(getTraceParent());
        this._config.logLevels.forEach(method => {
            this._wrap(console, method, this._patchConsoleMethod(method, traceparent));
        });
    }

    override disable(): void {
        this._config.logLevels.forEach(method => {
            this._unwrap(console, method);
        });
    }

    override init(): void {
        this.enable();
    }

    private _patchConsoleMethod(
        method: ConsoleMethod,
        traceparent: SpanContext | null
    ): (original) => ConsoleMethodSignature {
        const instrumentation = this;
        return function consoleMethod(original) {
            return function patchConsoleMethod(...args: any[]): ConsoleMethodSignature {
                try {
                    // const context = propagation.extract(ROOT_CONTEXT, { traceparent });
                    const msg = args[0];
                    instrumentation.logger.emit({
                        body:
                            typeof msg === 'object'
                                ? (msg.message ?? JSON.stringify(msg))
                                : String(msg),
                        severityText: method,
                        severityNumber: getLogSeverityNumber(method as keyof Console),
                        attributes: {
                            'trace.id': traceparent?.traceId,
                            'span.id': traceparent?.spanId
                        }
                        // context
                    });
                } catch (e) {
                    // Fallback if logging fails
                    instrumentation._originalConsole.error('Logging interceptor error', e);
                }

                // if error, increment error metrics
                if (method === 'error') {
                    instrumentation._errorMeter.add(1, {
                        errorType: arguments[0]?.constructor?.name || 'unknown',
                        message: String(arguments[0]).substring(0, 100) // truncate long messages
                    });
                }

                return original.apply(this, arguments);
            };
        };
    }
}

function getLogSeverityNumber(method: keyof Console): number {
    switch (method) {
        case 'error':
            return SeverityNumber.ERROR;
        case 'warn':
            return SeverityNumber.WARN;
        case 'info':
            return SeverityNumber.INFO;
        case 'debug':
            return SeverityNumber.DEBUG;
        default:
            return 9; // Default to info
    }
}
