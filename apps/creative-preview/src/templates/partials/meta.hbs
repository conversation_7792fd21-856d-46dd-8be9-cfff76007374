<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="format-detection" content="telephone=no">
<meta name="robots" content="noindex, nofollow">
<meta name="description" content="Creative Preview">
<meta property="og:title" content="Creative Preview">
<meta property="og:image"
      content="https://www.bannerflow.com/app/themes/bannerflow-theme/assets/images/favicon/favicon-32x32.png">

<link rel="icon" type="image/x-icon" href="{{meta.origin}}/static/favicon.ico">
<link rel="preconnect" href="{{meta.cdn}}">

<!--
    https://www.w3.org/TR/trace-context/
    Set the `traceparent` in the server's HTML template code. It should be
    dynamically generated server side to have the server's request trace Id,
    a parent span Id that was set on the server's request span, and the trace
    flags to indicate the server's sampling decision
    (01 = sampled, 00 = notsampled).
    '{version}-{traceId}-{spanId}-{sampleDecision}'

    <meta name="traceparent" content="00-ab42124a3c573678d4d8b21ba52df3bf-d21f7bc17caa5aba-01">
-->
{{#if meta.spanContext}}
    <meta
        name="traceparent"
        content="00-{{meta.spanContext.traceId}}-{{meta.spanContext.spanId}}-0{{meta.spanContext.traceFlags}}">
{{/if}}