<script>
    const ENVIRONMENT = "{{ environment }}";
    const RELEASE = "{{ release }}";
    const CREATIVE_RENDER_TIMEOUT = 10;
    const SUCCESS_ATTRIBUTE = 'data-rendered';
    const params = new URLSearchParams(window.location.search);
    const STUDIO_AD_SELECTOR = '.studio-ad';
    let adError;

    window.ENVIRONMENT = ENVIRONMENT;
    window.build = {{{json build }}}

    if (ENVIRONMENT !== 'local') {
        // Report timeout error
        setTimeout(function() {
            const scriptTag = document.querySelector(STUDIO_AD_SELECTOR);
            if (!scriptTag) {
                newrelic?.noticeError('Could not locate ad tag');
            } else if (!scriptTag.getAttribute(SUCCESS_ATTRIBUTE) && !adError) {
                newrelic?.noticeError('Creative failed to render (timeout ' + CREATIVE_RENDER_TIMEOUT + 's)');
            }
        }, CREATIVE_RENDER_TIMEOUT * 1000);
    }

    function measure(name, start, end) {
        try {
            return performance.measure(name, start, end);
        } catch (_) {
        }
    }

    function printPerformance() {
        measure('Navigation start to creative render', undefined, 'ad:render');
        measure('Navigation start to creative success', undefined, 'ad:success');

        measure('Ad tag insertion', 'ad:tag');
        measure('Ad creation to render', 'ad:creation', 'ad:render');
        measure('Ad creation to success', 'ad:creation', 'ad:success');
        measure('Ad init', 'ad:init:start', 'ad:init:end');
        measure('Ad create container', 'ad:container:start', 'ad:container:end');
        measure('Ad loading creative', 'ad:load:start', 'ad:load:end');
        measure('Ad to AC init', 'ad:init:start', 'ac:init:start');
        measure('Ad to show iframe', 'ad:init:start', 'ac:iframe:show');

        measure('CL loads modules', 'cl:load-modules:start', 'cl:load-modules:end');
        measure('CL init creative', 'cl:init-creative:start', 'cl:init-creative:end');

        measure('AC init', 'ac:init:start', 'ac:init:end');
        measure('AC loading features', 'ac:init:load:features:start', 'ac:init:load:features:end');
        measure('AC loading feeds', 'ac:init:load:feeds:start', 'ac:init:load:feeds:end');
        measure('AC 1s rendering', 'ac:init:render:start', 'ac:init:render:end');
        measure('AC loading fonts', 'ac:preload:fonts:start', 'ac:preload:fonts:end');
        measure('AC 2nd rerender', 'ac:init:rerender:start', 'ac:init:rerender:end');
        measure('AC preloading elements', 'ac:init:load:elements:start', 'ac:init:load:elements:end');
        measure('AC loading widgets', 'ac:init:load:widgets:start', 'ac:init:load:widgets:end');
        measure('AC visibility check', 'ac:init:visibility:check', 'ac:iframe:show');

        console.table(performance.getEntriesByType('measure'));
    }

    function onAdTagLoad() {
        if (!window._bannerflow?.ads.length) {
            return;
        }
        // Set the title and meta tags
        const creative = window._bannerflow.ads[0].selectedCreative;
        const { width, height } = creative.size;
        document.title = `${width}x${height} - ${creative.creativeset.name} - Creative Preview - BannerFlow`;

        const metaTitle = document.querySelector('meta[property="og:title"]');
        metaTitle.setAttribute('content', document.title);
        const description = `creative: ${creative.id}, size: ${width}x${height}, version: ${creative.version.name}-${creative.version.localization.name}, targetUrl: ${creative.targetUrl}, creativeset: ${creative.creativeset.id}-${creative.creativeset.name}, brand: ${_bannerflow.ads[0].data.brand.id}`;
        document.querySelector('meta[name="description"]').setAttribute('content', description);

        const newRelic = window.newrelic;
        if (!newRelic) {
            return;
        }
        newRelic.setCustomAttribute('bf.brand.id', creative.brand.id);
        newRelic.setCustomAttribute('bf.creative_set.id', creative.creativeset.id);
        newRelic.setCustomAttribute('bf.creative_set.name', creative.creativeset.name);
        newRelic.setCustomAttribute('bf.creative.id', creative.id);
        newRelic.setCustomAttribute('bf.design.id', creative.design.id);
        newRelic.setCustomAttribute('bf.size.id', creative.size.id);
        newRelic.setCustomAttribute('bf.version.id', creative.version.id);
    }

    function initMonitoring() {
        window.newrelic?.setCustomAttribute('url.full', window.location.href);
        // add all query params to newrelic
        params.forEach((value, key) => {
            if (key === 'snapshot'){
                window.newrelic?.setCustomAttribute(`bf.creative_set.snapshot.id`, value);
            } else {
                window.newrelic?.setCustomAttribute(`query.${key}`, value);
            }
        });
    }

    function registerAdListeners(adTag) {
        initMonitoring();
        adTag.addEventListener('render', function() {
            const renderDuration = measure('Navigation to Ad render')?.duration.toFixed(2);
            const c = renderDuration > 5000 ? 'color:red;' : renderDuration > 2500 ? 'color:orange;' : 'color:#3CB371;';
            console.info(`Ad rendered: %c${renderDuration}ms`, c);

            window.newrelic?.addPageAction('CreativeRendered', {
                duration: renderDuration
            });
        });


        adTag.addEventListener('error', function(event) {
            // event.data is emitted from the ad.js
            // If the event does not have data, it was a native script error, indicating something went wrong while loading
            const errorMessage = event.data?.event ?? 'Failed to load ad.js';
            const error = new Error(errorMessage);
            error.name = 'AdError';
            console.error(error);

            adError = true;

            // if the ad script loading errored, we can't get the brand
            if (window._bannerflow?.ads?.[0]) {
                const brandId = window._bannerflow.ads[0].data.brand.id;
                window.newrelic?.setCustomAttribute('bf.brand.id', brandId);
            }
            if (event.data) {
                window.newrelic?.setCustomAttribute('error', event.data);
            }

            window.newrelic?.noticeError(error);
        });
    }
</script>
