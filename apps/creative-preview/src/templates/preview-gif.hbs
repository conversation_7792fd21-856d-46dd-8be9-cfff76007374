<!DOCTYPE html>
<html>
<head>
    <title>Creative Preview - GIFs</title>
    {{> meta meta=meta}}

    {{> styles }}

    {{> newrelic newrelic=newrelic}}

    {{> preview_init build=build environment=environment release=release}}

    {{> console_utils}}
    {{> cirs_utils}}
</head>
<body>

{{> ad src=src }}

<script>
    const gif = {{{json data}}};
    let adApi;

    adTag.addEventListener('render', function() {
        adApi = adTag.adApi;
        const bannerflowCreative = window._bannerflow.ads[0].creativeLoader.creative;
        if (!bannerflowCreative) {
            throw new Error('Bannerflow creative not found.');
        }

        console.log('Gif:', gif);
        if (gif) {
            subscribeToConsole();
            loopOverFrames(gif);
        }
    });

    async function loopOverFrames(gif) {
        const totalFrames = gif.frames.length;
        console.log(`Gif frames rendering started for ${totalFrames} items, with ${gif.mode} mode`);
        await waitForCIRSReady();

        for (let index = 0; index < totalFrames; index++) {
            console.groupCollapsed(`Frame ${index + 1} of ${totalFrames}`);
            const { time, duration } = gif.frames[index];
            // 1. Seek to frame
            try {
                adApi.seek(time);
            } catch (error) {
                notifyCIRSError(error, {
                    frame: `${index + 1}/${totalFrames}`
                });
            }

            // 2. Notify CIRS to take screenshot
            notifyCIRSScreenshot({
                frame: `${index + 1}/${totalFrames}`,
                duration: duration * 1000 // convert to ms
            });

            // 3. wait until CIRS has taken the screenshot and is ready again
            try {
                performance.mark(`CIRS-wait-start-${index}`);
                await waitForCIRSReady();
                performance.measure(`CIRS-wait-${index}`, `CIRS-wait-start-${index}`);
            } catch (e) {
                console.warn('waitForCIRSReady:', e);
                e.name = 'CIRSError';
                newrelic?.noticeError(e, { catalogId: id, message: e.message });
                notifyCIRSError(e.message, { index });
                console.groupEnd();
                continue;
            }

            console.groupEnd();
        }
        console.log('GIF frames rendering done');

        // 4. inform CIRS that all screenshots are done
        notifyCIRSDone();

        console.log('stopping the ad');
        adApi.stop();

        // 5. print performance
        console.table(performance.getEntriesByType('measure'));
    }
</script>
</body>
</html>
