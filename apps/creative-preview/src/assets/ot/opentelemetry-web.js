(()=>{"use strict";var t,e,r,n,o,i,a,u,s,c,l,f,p,h,d,y,_,v,g,m,b,E,T,S,w={592:function(t){function e(t){return"function"==typeof t}var r=console.error.bind(console);function n(t,e,r){var n=!!t[e]&&t.propertyIsEnumerable(e);Object.defineProperty(t,e,{configurable:!0,enumerable:n,writable:!0,value:r})}function o(t){t&&t.logger&&(e(t.logger)?r=t.logger:r("new logger isn't a function, not replacing"))}function i(t,o,i){if(!t||!t[o]){r("no original function "+o+" to wrap");return}if(!i){r("no wrapper function"),r(Error().stack);return}if(!e(t[o])||!e(i)){r("original object and wrapper must be functions");return}var a=t[o],u=i(a,o);return n(u,"__original",a),n(u,"__unwrap",function(){t[o]===u&&n(t,o,a)}),n(u,"__wrapped",!0),n(t,o,u),u}function a(t,e){if(!t||!t[e]){r("no function to unwrap."),r(Error().stack);return}if(t[e].__unwrap)return t[e].__unwrap();r("no original to unwrap to -- has "+e+" already been unwrapped?")}o.wrap=i,o.massWrap=function(t,e,n){if(t)!Array.isArray(t)&&(t=[t]);else{r("must provide one or more modules to patch"),r(Error().stack);return}if(!(e&&Array.isArray(e))){r("must provide one or more functions to wrap on modules");return}t.forEach(function(t){e.forEach(function(e){i(t,e,n)})})},o.unwrap=a,o.massUnwrap=function(t,e){if(t)!Array.isArray(t)&&(t=[t]);else{r("must provide one or more modules to patch"),r(Error().stack);return}if(!(e&&Array.isArray(e))){r("must provide one or more functions to unwrap on modules");return}t.forEach(function(t){e.forEach(function(e){a(t,e)})})},t.exports=o}},O={};function A(t){var e=O[t];if(void 0!==e)return e.exports;var r=O[t]={exports:{}};return w[t](r,r.exports,A),r.exports}function L(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}A.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(t){if("object"==typeof window)return window}}(),A.rv=function(){return"1.0.5"},A.ruid="bundler=rspack@1.0.5";var R="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof A.g?A.g:{},x="1.9.0",P=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/,C=function(t){var e=new Set([t]),r=new Set,n=t.match(P);if(!n)return function(){return!1};var o={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=o.prerelease)return function(e){return e===t};function i(t){return r.add(t),!1}function a(t){return e.add(t),!0}return function(t){if(e.has(t))return!0;if(r.has(t))return!1;var n,a,u=t.match(P);if(!u)return i(t);var s={major:+u[1],minor:+u[2],patch:+u[3],prerelease:u[4]};if(null!=s.prerelease||o.major!==s.major)return i(t);if(0===o.major){if(o.minor===s.minor&&o.patch<=s.patch){;return n=t,e.add(n),!0}return i(t)}if(o.minor<=s.minor){;return a=t,e.add(a),!0}return i(t)}}(x),N=Symbol.for("opentelemetry.js.api."+x.split(".")[0]);function M(t,e,r,n){void 0===n&&(n=!1);var o,i=R[N]=null!==(o=R[N])&&void 0!==o?o:{version:x};if(!n&&i[t]){var a=Error("@opentelemetry/api: Attempted duplicate registration of API: "+t);return r.error(a.stack||a.message),!1}if(i.version!==x){var a=Error("@opentelemetry/api: Registration of version v"+i.version+" for "+t+" does not match previously registered API v"+x);return r.error(a.stack||a.message),!1}return i[t]=e,r.debug("@opentelemetry/api: Registered a global for "+t+" v"+x+"."),!0}function I(t){var e,r,n=null===(e=R[N])||void 0===e?void 0:e.version;if(!!n&&!!C(n))return null===(r=R[N])||void 0===r?void 0:r[t]}function U(t,e){e.debug("@opentelemetry/api: Unregistering a global for "+t+" v"+x+".");var r=R[N];r&&delete r[t]}function k(t){return Symbol.for(t)}var D=new function t(e){var r=this;r._currentContext=e?new Map(e):new Map,r.getValue=function(t){return r._currentContext.get(t)},r.setValue=function(e,n){var o=new t(r._currentContext);return o._currentContext.set(e,n),o},r.deleteValue=function(e){var n=new t(r._currentContext);return n._currentContext.delete(e),n}},B=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},j=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)(n||!(o in e))&&(!n&&(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},F=function(){function t(){}return t.prototype.active=function(){return D},t.prototype.with=function(t,e,r){for(var n=[],o=3;o<arguments.length;o++)n[o-3]=arguments[o];return e.call.apply(e,j([r],B(n),!1))},t.prototype.bind=function(t,e){return e},t.prototype.enable=function(){return this},t.prototype.disable=function(){return this},t}(),G=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},V=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)(n||!(o in e))&&(!n&&(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},H=function(){function t(t){this._namespace=t.namespace||"DiagComponentLogger"}return t.prototype.debug=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return z("debug",this._namespace,t)},t.prototype.error=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return z("error",this._namespace,t)},t.prototype.info=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return z("info",this._namespace,t)},t.prototype.warn=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return z("warn",this._namespace,t)},t.prototype.verbose=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return z("verbose",this._namespace,t)},t}();function z(t,e,r){var n=I("diag");if(!!n)return r.unshift(e),n[t].apply(n,V([],G(r),!1))}!function(t){t[t.NONE=0]="NONE",t[t.ERROR=30]="ERROR",t[t.WARN=50]="WARN",t[t.INFO=60]="INFO",t[t.DEBUG=70]="DEBUG",t[t.VERBOSE=80]="VERBOSE",t[t.ALL=9999]="ALL"}(t||(t={}));var X=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},W=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)(n||!(o in e))&&(!n&&(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},q=function(){function e(){function e(t){return function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var n=I("diag");if(n)return n[t].apply(n,W([],X(e),!1))}}var r=this;r.setLogger=function(e,n){if(void 0===n&&(n={logLevel:t.INFO}),e===r){var o,i,a,u=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return r.error(null!==(o=u.stack)&&void 0!==o?o:u.message),!1}"number"==typeof n&&(n={logLevel:n});var s=I("diag"),c=function(e,r){function n(t,n){var o=r[t];return"function"==typeof o&&e>=n?o.bind(r):function(){}}return e<t.NONE?e=t.NONE:e>t.ALL&&(e=t.ALL),r=r||{},{error:n("error",t.ERROR),warn:n("warn",t.WARN),info:n("info",t.INFO),debug:n("debug",t.DEBUG),verbose:n("verbose",t.VERBOSE)}}(null!==(i=n.logLevel)&&void 0!==i?i:t.INFO,e);if(s&&!n.suppressOverrideMessage){var l=null!==(a=Error().stack)&&void 0!==a?a:"<failed to generate stacktrace>";s.warn("Current logger will be overwritten from "+l),c.warn("Current logger will overwrite one already registered from "+l)}return M("diag",c,r,!0)},r.disable=function(){U("diag",r)},r.createComponentLogger=function(t){return new H(t)},r.verbose=e("verbose"),r.debug=e("debug"),r.info=e("info"),r.warn=e("warn"),r.error=e("error")}return e.instance=function(){return!this._instance&&(this._instance=new e),this._instance},e}(),Y=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},Q=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)(n||!(o in e))&&(!n&&(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},K="context",J=new F,Z=function(){function t(){}return t.getInstance=function(){return!this._instance&&(this._instance=new t),this._instance},t.prototype.setGlobalContextManager=function(t){return M(K,t,q.instance())},t.prototype.active=function(){return this._getContextManager().active()},t.prototype.with=function(t,e,r){for(var n,o=[],i=3;i<arguments.length;i++)o[i-3]=arguments[i];return(n=this._getContextManager()).with.apply(n,Q([t,e,r],Y(o),!1))},t.prototype.bind=function(t,e){return this._getContextManager().bind(t,e)},t.prototype._getContextManager=function(){return I(K)||J},t.prototype.disable=function(){this._getContextManager().disable(),U(K,q.instance())},t}();!function(t){t[t.NONE=0]="NONE",t[t.SAMPLED=1]="SAMPLED"}(e||(e={}));var $="0000000000000000",tt="00000000000000000000000000000000",te={traceId:tt,spanId:$,traceFlags:e.NONE},tr=function(){function t(t){void 0===t&&(t=te),this._spanContext=t}return t.prototype.spanContext=function(){return this._spanContext},t.prototype.setAttribute=function(t,e){return this},t.prototype.setAttributes=function(t){return this},t.prototype.addEvent=function(t,e){return this},t.prototype.addLink=function(t){return this},t.prototype.addLinks=function(t){return this},t.prototype.setStatus=function(t){return this},t.prototype.updateName=function(t){return this},t.prototype.end=function(t){},t.prototype.isRecording=function(){return!1},t.prototype.recordException=function(t,e){},t}(),tn=k("OpenTelemetry Context Key SPAN");function to(t){return t.getValue(tn)||void 0}function ti(){return to(Z.getInstance().active())}function ta(t,e){return t.setValue(tn,e)}function tu(t){return t.deleteValue(tn)}function ts(t,e){return ta(t,new tr(e))}function tc(t){var e;return null===(e=to(t))||void 0===e?void 0:e.spanContext()}var tl=/^([0-9a-f]{32})$/i,tf=/^[0-9a-f]{16}$/i;function tp(t){return tl.test(t)&&t!==tt}function th(t){var e;return tp(t.traceId)&&(e=t.spanId,tf.test(e)&&e!==$)}function td(t){return new tr(t)}var ty=Z.getInstance(),t_=function(){function t(){}return t.prototype.startSpan=function(t,e,r){if(void 0===r&&(r=ty.active()),null==e?void 0:e.root)return new tr;var n=r&&tc(r);return function(t){return"object"==typeof t&&"string"==typeof t.spanId&&"string"==typeof t.traceId&&"number"==typeof t.traceFlags}(n)&&th(n)?new tr(n):new tr},t.prototype.startActiveSpan=function(t,e,r,n){if(!(arguments.length<2)){2==arguments.length?a=e:3==arguments.length?(o=e,a=r):(o=e,i=r,a=n);var o,i,a,u=null!=i?i:ty.active(),s=this.startSpan(t,o,u),c=ta(u,s);return ty.with(c,a,void 0,s)}},t}(),tv=new t_,tg=function(){function t(t,e,r,n){this._provider=t,this.name=e,this.version=r,this.options=n}return t.prototype.startSpan=function(t,e,r){return this._getTracer().startSpan(t,e,r)},t.prototype.startActiveSpan=function(t,e,r,n){var o=this._getTracer();return Reflect.apply(o.startActiveSpan,o,arguments)},t.prototype._getTracer=function(){if(this._delegate)return this._delegate;var t=this._provider.getDelegateTracer(this.name,this.version,this.options);return t?(this._delegate=t,this._delegate):tv},t}(),tm=new(function(){function t(){}return t.prototype.getTracer=function(t,e,r){return new t_},t}()),tb=function(){function t(){}return t.prototype.getTracer=function(t,e,r){var n;return null!==(n=this.getDelegateTracer(t,e,r))&&void 0!==n?n:new tg(this,t,e,r)},t.prototype.getDelegate=function(){var t;return null!==(t=this._delegate)&&void 0!==t?t:tm},t.prototype.setDelegate=function(t){this._delegate=t},t.prototype.getDelegateTracer=function(t,e,r){var n;return null===(n=this._delegate)||void 0===n?void 0:n.getTracer(t,e,r)},t}(),tE="trace",tT=(function(){function t(){this._proxyTracerProvider=new tb,this.wrapSpanContext=td,this.isSpanContextValid=th,this.deleteSpan=tu,this.getSpan=to,this.getActiveSpan=ti,this.getSpanContext=tc,this.setSpan=ta,this.setSpanContext=ts}return t.getInstance=function(){return!this._instance&&(this._instance=new t),this._instance},t.prototype.setGlobalTracerProvider=function(t){var e=M(tE,this._proxyTracerProvider,q.instance());return e&&this._proxyTracerProvider.setDelegate(t),e},t.prototype.getTracerProvider=function(){return I(tE)||this._proxyTracerProvider},t.prototype.getTracer=function(t,e){return this.getTracerProvider().getTracer(t,e)},t.prototype.disable=function(){U(tE,q.instance()),this._proxyTracerProvider=new tb},t})().getInstance(),tS=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),tw=function(){function t(){}return t.prototype.createGauge=function(t,e){return tk},t.prototype.createHistogram=function(t,e){return tD},t.prototype.createCounter=function(t,e){return tU},t.prototype.createUpDownCounter=function(t,e){return tB},t.prototype.createObservableGauge=function(t,e){return tF},t.prototype.createObservableCounter=function(t,e){return tj},t.prototype.createObservableUpDownCounter=function(t,e){return tG},t.prototype.addBatchObservableCallback=function(t,e){},t.prototype.removeBatchObservableCallback=function(t){},t}(),tO=function(){},tA=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return tS(e,t),e.prototype.add=function(t,e){},e}(tO),tL=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return tS(e,t),e.prototype.add=function(t,e){},e}(tO),tR=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return tS(e,t),e.prototype.record=function(t,e){},e}(tO),tx=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return tS(e,t),e.prototype.record=function(t,e){},e}(tO),tP=function(){function t(){}return t.prototype.addCallback=function(t){},t.prototype.removeCallback=function(t){},t}(),tC=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return tS(e,t),e}(tP),tN=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return tS(e,t),e}(tP),tM=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return tS(e,t),e}(tP),tI=new tw,tU=new tA,tk=new tR,tD=new tx,tB=new tL,tj=new tC,tF=new tN,tG=new tM,tV=new(function(){function t(){}return t.prototype.getMeter=function(t,e,r){return tI},t}()),tH="metrics",tz=(function(){function t(){}return t.getInstance=function(){return!this._instance&&(this._instance=new t),this._instance},t.prototype.setGlobalMeterProvider=function(t){return M(tH,t,q.instance())},t.prototype.getMeterProvider=function(){return I(tH)||tV},t.prototype.getMeter=function(t,e,r){return this.getMeterProvider().getMeter(t,e,r)},t.prototype.disable=function(){U(tH,q.instance())},t})().getInstance(),tX="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof A.g?A.g:{},tW=Symbol.for("io.opentelemetry.js.api.logs"),tq=function(){function t(){}return t.prototype.emit=function(t){},t}(),tY=new tq,tQ=new(function(){function t(){}return t.prototype.getLogger=function(t,e,r){return new tq},t}()),tK=function(){function t(t,e,r,n){this._provider=t,this.name=e,this.version=r,this.options=n}return t.prototype.emit=function(t){this._getLogger().emit(t)},t.prototype._getLogger=function(){if(this._delegate)return this._delegate;var t=this._provider.getDelegateLogger(this.name,this.version,this.options);return t?(this._delegate=t,this._delegate):tY},t}(),tJ=function(){function t(){}return t.prototype.getLogger=function(t,e,r){var n;return null!==(n=this.getDelegateLogger(t,e,r))&&void 0!==n?n:new tK(this,t,e,r)},t.prototype.getDelegate=function(){var t;return null!==(t=this._delegate)&&void 0!==t?t:tQ},t.prototype.setDelegate=function(t){this._delegate=t},t.prototype.getDelegateLogger=function(t,e,r){var n;return null===(n=this._delegate)||void 0===n?void 0:n.getLogger(t,e,r)},t}(),tZ=(function(){function t(){this._proxyLoggerProvider=new tJ}return t.getInstance=function(){return!this._instance&&(this._instance=new t),this._instance},t.prototype.setGlobalLoggerProvider=function(t){var e,r;if(tX[tW])return this.getLoggerProvider();return tX[tW]=(e=t,r=tQ,function(t){return 1===t?e:r}),this._proxyLoggerProvider.setDelegate(t),t},t.prototype.getLoggerProvider=function(){var t,e;return null!==(e=null===(t=tX[tW])||void 0===t?void 0:t.call(tX,1))&&void 0!==e?e:this._proxyLoggerProvider},t.prototype.getLogger=function(t,e,r){return this.getLoggerProvider().getLogger(t,e,r)},t.prototype.disable=function(){delete tX[tW],this._proxyLoggerProvider=new tJ},t})().getInstance(),t$=q.instance(),t0=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},t1=function(){function t(t){var e;void 0===t&&(t={}),this._propagators=null!==(e=t.propagators)&&void 0!==e?e:[],this._fields=Array.from(new Set(this._propagators.map(function(t){return"function"==typeof t.fields?t.fields():[]}).reduce(function(t,e){return t.concat(e)},[])))}return t.prototype.inject=function(t,e,r){var n,o;try{for(var i=t0(this._propagators),a=i.next();!a.done;a=i.next()){var u=a.value;try{u.inject(t,e,r)}catch(t){t$.warn("Failed to inject with "+u.constructor.name+". Err: "+t.message)}}}catch(t){n={error:t}}finally{try{a&&!a.done&&(o=i.return)&&o.call(i)}finally{if(n)throw n.error}}},t.prototype.extract=function(t,e,r){return this._propagators.reduce(function(t,n){try{return n.extract(t,e,r)}catch(t){t$.warn("Failed to extract with "+n.constructor.name+". Err: "+t.message)}return t},t)},t.prototype.fields=function(){return this._fields.slice()},t}(),t2=function(){function t(){}return t.prototype.inject=function(t,e){},t.prototype.extract=function(t,e){return t},t.prototype.fields=function(){return[]},t}(),t3={get:function(t,e){if(null!=t)return t[e]},keys:function(t){return null==t?[]:Object.keys(t)}},t4={set:function(t,e,r){if(null!=t)t[e]=r}},t5=k("OpenTelemetry Baggage Key");function t6(t){return t.getValue(t5)||void 0}function t8(){return t6(Z.getInstance().active())}function t9(t,e){return t.setValue(t5,e)}function t7(t){return t.deleteValue(t5)}var et=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},ee=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},er=function(){function t(t){this._entries=t?new Map(t):new Map}return t.prototype.getEntry=function(t){var e=this._entries.get(t);if(!!e)return Object.assign({},e)},t.prototype.getAllEntries=function(){return Array.from(this._entries.entries()).map(function(t){var e=et(t,2);return[e[0],e[1]]})},t.prototype.setEntry=function(e,r){var n=new t(this._entries);return n._entries.set(e,r),n},t.prototype.removeEntry=function(e){var r=new t(this._entries);return r._entries.delete(e),r},t.prototype.removeEntries=function(){for(var e,r,n=[],o=0;o<arguments.length;o++)n[o]=arguments[o];var i=new t(this._entries);try{for(var a=ee(n),u=a.next();!u.done;u=a.next()){var s=u.value;i._entries.delete(s)}}catch(t){e={error:t}}finally{try{u&&!u.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return i},t.prototype.clear=function(){return new t},t}(),en=Symbol("BaggageEntryMetadata"),eo=q.instance();function ei(t){return void 0===t&&(t={}),new er(new Map(Object.entries(t)))}function ea(t){return"string"!=typeof t&&(eo.error("Cannot create baggage metadata from unknown type: "+typeof t),t=""),{__TYPE__:en,toString:function(){return t}}}var eu="propagation",es=new t2,ec=(function(){function t(){this.createBaggage=ei,this.getBaggage=t6,this.getActiveBaggage=t8,this.setBaggage=t9,this.deleteBaggage=t7}return t.getInstance=function(){return!this._instance&&(this._instance=new t),this._instance},t.prototype.setGlobalPropagator=function(t){return M(eu,t,q.instance())},t.prototype.inject=function(t,e,r){return void 0===r&&(r=t4),this._getGlobalPropagator().inject(t,e,r)},t.prototype.extract=function(t,e,r){return void 0===r&&(r=t3),this._getGlobalPropagator().extract(t,e,r)},t.prototype.fields=function(){return this._getGlobalPropagator().fields()},t.prototype.disable=function(){U(eu,q.instance())},t.prototype._getGlobalPropagator=function(){return I(eu)||es},t})().getInstance(),el=k("OpenTelemetry SDK Context Key SUPPRESS_TRACING");function ef(t){return!0===t.getValue(el)}var ep="baggage",eh=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a};function ed(t){var e,r=t.split(";");if(r.length<=0)return;var n=r.shift();if(!!n){var o=n.indexOf("=");if(!(o<=0)){var i=decodeURIComponent(n.substring(0,o).trim()),a=decodeURIComponent(n.substring(o+1).trim());return r.length>0&&(e=ea(r.join(";"))),{key:i,value:a,metadata:e}}}}var ey=function(){function t(){}return t.prototype.inject=function(t,e,r){var n=ec.getBaggage(t);if(!(!n||ef(t))){var o=n.getAllEntries().map(function(t){var e=eh(t,2),r=e[0],n=e[1],o=encodeURIComponent(r)+"="+encodeURIComponent(n.value);return void 0!==n.metadata&&(o+=";"+n.metadata.toString()),o}).filter(function(t){return t.length<=4096}).slice(0,180).reduce(function(t,e){var r=""+t+(""!==t?",":"")+e;return r.length>8192?t:r},"");o.length>0&&r.set(e,ep,o)}},t.prototype.extract=function(t,e,r){var n=r.get(e,ep),o=Array.isArray(n)?n.join(","):n;if(!o)return t;var i={};return 0===o.length?t:(o.split(",").forEach(function(t){var e=ed(t);if(e){var r={value:e.value};e.metadata&&(r.metadata=e.metadata),i[e.key]=r}}),0===Object.entries(i).length)?t:ec.setBaggage(t,ec.createBaggage(i))},t.prototype.fields=function(){return[ep]},t}(),e_="[_0-9a-z-*/]",ev=RegExp("^(?:[a-z]"+e_+"{0,255}|"+("[a-z0-9]"+e_+"{0,240}@[a-z]"+e_)+"{0,13})$"),eg=/^[ -~]{0,255}[!-~]$/,em=/,|=/,eb=function(){function t(t){this._internalState=new Map,t&&this._parse(t)}return t.prototype.set=function(t,e){var r=this._clone();return r._internalState.has(t)&&r._internalState.delete(t),r._internalState.set(t,e),r},t.prototype.unset=function(t){var e=this._clone();return e._internalState.delete(t),e},t.prototype.get=function(t){return this._internalState.get(t)},t.prototype.serialize=function(){var t=this;return this._keys().reduce(function(e,r){return e.push(r+"="+t.get(r)),e},[]).join(",")},t.prototype._parse=function(t){!(t.length>512)&&(this._internalState=t.split(",").reverse().reduce(function(t,e){var r=e.trim(),n=r.indexOf("=");if(-1!==n){var o,i,a=r.slice(0,n),u=r.slice(n+1,e.length);if(o=a,ev.test(o)&&(i=u,eg.test(i)&&!em.test(i)))t.set(a,u)}return t},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))},t.prototype._keys=function(){return Array.from(this._internalState.keys()).reverse()},t.prototype._clone=function(){var e=new t;return e._internalState=new Map(this._internalState),e},t}(),eE="traceparent",eT="tracestate",eS=RegExp("^\\s?((?!ff)[\\da-f]{2})-((?![0]{32})[\\da-f]{32})-((?![0]{16})[\\da-f]{16})-([\\da-f]{2})(-.*)?\\s?$");function ew(t){var e=eS.exec(t);return!e||"00"===e[1]&&e[5]?null:{traceId:e[2],spanId:e[3],traceFlags:parseInt(e[4],16)}}var eO=function(){function t(){}return t.prototype.inject=function(t,r,n){var o=tT.getSpanContext(t);if(!(!o||ef(t))&&th(o)){var i="00-"+o.traceId+"-"+o.spanId+"-0"+Number(o.traceFlags||e.NONE).toString(16);n.set(r,eE,i),o.traceState&&n.set(r,eT,o.traceState.serialize())}},t.prototype.extract=function(t,e,r){var n=r.get(e,eE);if(!n)return t;var o=Array.isArray(n)?n[0]:n;if("string"!=typeof o)return t;var i=ew(o);if(!i)return t;i.isRemote=!0;var a=r.get(e,eT);if(a){var u=Array.isArray(a)?a.join(","):a;i.traceState=new eb("string"==typeof u?u:void 0)}return tT.setSpanContext(t,i)},t.prototype.fields=function(){return[eE,eT]},t}(),eA=function(){function t(t){this._delegate=t}return t.prototype.export=function(t,e){this._delegate.export(t,e)},t.prototype.forceFlush=function(){return this._delegate.forceFlush()},t.prototype.shutdown=function(){return this._delegate.shutdown()},t}(),eL=performance;function eR(t){return[Math.trunc(t/1e3),Math.round(t%1e3*1e6)]}function ex(){var t=eL.timeOrigin;return"number"!=typeof t&&(t=eL.timing&&eL.timing.fetchStart),t}function eP(t){return ek(eR(ex()),eR("number"==typeof t?t:eL.now()))}function eC(t){if(eI(t))return t;if("number"==typeof t)return t<ex()?eP(t):eR(t);if(t instanceof Date)return eR(t.getTime());else throw TypeError("Invalid input type")}function eN(t){return 1e9*t[0]+t[1]}function eM(t){return 1e6*t[0]+t[1]/1e3}function eI(t){return Array.isArray(t)&&2===t.length&&"number"==typeof t[0]&&"number"==typeof t[1]}function eU(t){return eI(t)||"number"==typeof t||t instanceof Date}function ek(t,e){var r=[t[0]+e[0],t[1]+e[1]];return r[1]>=1e9&&(r[1]-=1e9,r[0]+=1),r}function eD(t){return t>=48&&t<=57?t-48:t>=97&&t<=102?t-87:t-55}function eB(t){for(var e=new Uint8Array(t.length/2),r=0,n=0;n<t.length;n+=2){var o=eD(t.charCodeAt(n)),i=eD(t.charCodeAt(n+1));e[r++]=o<<4|i}return e}function ej(t){var e=BigInt(1e9);return BigInt(t[0])*e+BigInt(t[1])}function eF(t){var e;return{low:Number(BigInt.asUintN(32,e=ej(t))),high:Number(BigInt.asUintN(32,e>>BigInt(32)))}}var eG="undefined"!=typeof BigInt?function(t){return ej(t).toString()}:eN;function eV(t){return t}function eH(t){if(void 0!==t)return eB(t)}var ez={encodeHrTime:eF,encodeSpanContext:eB,encodeOptionalSpanContext:eH};function eX(t){if(void 0===t)return ez;var e,r,n=null===(e=t.useLongBits)||void 0===e||e,o=null!==(r=t.useHex)&&void 0!==r&&r;return{encodeHrTime:n?eF:eG,encodeSpanContext:o?eV:eB,encodeOptionalSpanContext:o?eV:eH}}var eW=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a};function eq(t){return{attributes:eQ(t.attributes),droppedAttributesCount:0}}function eY(t){return{name:t.name,version:t.version}}function eQ(t){return Object.keys(t).map(function(e){return eK(e,t[e])})}function eK(t,e){return{key:t,value:eJ(e)}}function eJ(t){var e=typeof t;if("string"===e)return{stringValue:t};if("number"===e)return Number.isInteger(t)?{intValue:t}:{doubleValue:t};return"boolean"===e?{boolValue:t}:t instanceof Uint8Array?{bytesValue:t}:Array.isArray(t)?{arrayValue:{values:t.map(eJ)}}:"object"===e&&null!=t?{kvlistValue:{values:Object.entries(t).map(function(t){var e=eW(t,2);return eK(e[0],e[1])})}}:{}}var eZ=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},e$=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},e0={serializeRequest:function(t){var e={resourceLogs:function(t,e){return Array.from(function(t){var e,r,n=new Map;try{for(var o=eZ(t),i=o.next();!i.done;i=o.next()){var a=i.value,u=a.resource,s=a.instrumentationScope,c=s.name,l=s.version,f=void 0===l?"":l,p=s.schemaUrl,h=void 0===p?"":p,d=n.get(u);!d&&(d=new Map,n.set(u,d));var y=c+"@"+f+":"+h,_=d.get(y);!_&&(_=[],d.set(y,_)),_.push(a)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return n}(t),function(t){var r=e$(t,2),n=r[0],o=r[1];return{resource:eq(n),scopeLogs:Array.from(o,function(t){var r=e$(t,2)[1];return{scope:eY(r[0].instrumentationScope),logRecords:r.map(function(t){return function(t,e){var r,n,o;return{timeUnixNano:e.encodeHrTime(t.hrTime),observedTimeUnixNano:e.encodeHrTime(t.hrTimeObserved),severityNumber:function(t){return t}(t.severityNumber),severityText:t.severityText,body:eJ(t.body),attributes:function(t){return Object.keys(t).map(function(e){return eK(e,t[e])})}(t.attributes),droppedAttributesCount:t.droppedAttributesCount,flags:null===(r=t.spanContext)||void 0===r?void 0:r.traceFlags,traceId:e.encodeOptionalSpanContext(null===(n=t.spanContext)||void 0===n?void 0:n.traceId),spanId:e.encodeOptionalSpanContext(null===(o=t.spanContext)||void 0===o?void 0:o.spanId)}}(t,e)}),schemaUrl:r[0].instrumentationScope.schemaUrl}}),schemaUrl:void 0}})}(t,eX({useHex:!0,useLongBits:!1}))};return new TextEncoder().encode(JSON.stringify(e))},deserializeResponse:function(t){return JSON.parse(new TextDecoder().decode(t))}},e1=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(t){i=[6,t],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},e2=function(){function t(t){this._transport=t}return t.prototype.retry=function(t,e,r){var n=this;return new Promise(function(o,i){setTimeout(function(){n._transport.send(t,e).then(o,i)},r)})},t.prototype.send=function(t,e){var r,n,o,i,a;return n=this,o=void 0,i=void 0,a=function(){var n,o,i,a,u,s,c;return e1(this,function(l){switch(l.label){case 0:return n=Date.now()+e,[4,this._transport.send(t,e)];case 1:o=l.sent(),i=5,a=1e3,l.label=2;case 2:if(!("retryable"===o.status&&i>0))return[3,4];if(i--,u=Math.max(Math.min(a,5e3)+(.4*Math.random()-.2),0),a*=1.5,s=null!==(r=o.retryInMillis)&&void 0!==r?r:u,s>(c=n-Date.now()))return[2,o];return[4,this.retry(t,c,s)];case 3:return o=l.sent(),[3,2];case 4:return[2,o]}})},new(i||(i=Promise))(function(t,e){function r(t){try{s(a.next(t))}catch(t){e(t)}}function u(t){try{s(a.throw(t))}catch(t){e(t)}}function s(e){var n;e.done?t(e.value):((n=e.value)instanceof i?n:new i(function(t){t(n)})).then(r,u)}s((a=a.apply(n,o||[])).next())})},t.prototype.shutdown=function(){return this._transport.shutdown()},t}();function e3(t){return new e2(t.transport)}var e4=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},e5=function(){function t(t){this._parameters=t}return t.prototype.send=function(t,e){var r=this;return new Promise(function(n){var o=new XMLHttpRequest;o.timeout=e,o.open("POST",r._parameters.url),Object.entries(r._parameters.headers()).forEach(function(t){var e=e4(t,2),r=e[0],n=e[1];o.setRequestHeader(r,n)}),o.ontimeout=function(t){n({status:"failure",error:Error("XHR request timed out")})},o.onreadystatechange=function(){if(o.status>=200&&o.status<=299)t$.debug("XHR success"),n({status:"success"});else if(o.status&&[429,502,503,504].includes(o.status))n({status:"retryable",retryInMillis:function(t){if(null!=t){var e=Number.parseInt(t,10);if(Number.isInteger(e))return e>0?1e3*e:-1;var r=new Date(t).getTime()-Date.now();return r>=0?r:0}}(o.getResponseHeader("Retry-After"))});else 0!==o.status&&n({status:"failure",error:Error("XHR request failed with non-retryable status")})},o.onabort=function(){n({status:"failure",error:Error("XHR request aborted")})},o.onerror=function(){n({status:"failure",error:Error("XHR request errored")})},o.send(t)})},t.prototype.shutdown=function(){},t}(),e6=function(){function t(t){this._params=t}return t.prototype.send=function(t){var e=this;return new Promise(function(r){navigator.sendBeacon(e._params.url,new Blob([t],{type:e._params.blobType}))?(t$.debug("SendBeacon success"),r({status:"success"})):r({status:"failure",error:Error("SendBeacon failed")})})},t.prototype.shutdown=function(){},t}(),e8=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(t){i=[6,t],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},e9=function(){function t(t){this._sendingPromises=[],this._concurrencyLimit=t}return t.prototype.pushPromise=function(t){var e=this;if(this.hasReachedLimit())throw Error("Concurrency Limit reached");this._sendingPromises.push(t);var r=function(){var r=e._sendingPromises.indexOf(t);e._sendingPromises.splice(r,1)};t.then(r,r)},t.prototype.hasReachedLimit=function(){return this._sendingPromises.length>=this._concurrencyLimit},t.prototype.awaitAll=function(){var t,e,r,n;return t=this,e=void 0,r=void 0,n=function(){return e8(this,function(t){switch(t.label){case 0:return[4,Promise.all(this._sendingPromises)];case 1:return t.sent(),[2]}})},new(r||(r=Promise))(function(o,i){function a(t){try{s(n.next(t))}catch(t){i(t)}}function u(t){try{s(n.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,u)}s((n=n.apply(t,e||[])).next())})},t}();!function(t){t[t.SUCCESS=0]="SUCCESS",t[t.FAILED=1]="FAILED"}(r||(r={}));var e7=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),rt=function(t){function e(e,r,n){var o=t.call(this,e)||this;return o.name="OTLPExporterError",o.data=n,o.code=r,o}return e7(e,t),e}(Error),re=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(t){i=[6,t],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},rr=function(){function t(t,e,r,n,o){this._transport=t,this._serializer=e,this._responseHandler=r,this._promiseQueue=n,this._timeout=o,this._diagLogger=t$.createComponentLogger({namespace:"OTLPExportDelegate"})}return t.prototype.export=function(t,e){var n=this;if(this._diagLogger.debug("items to be sent",t),this._promiseQueue.hasReachedLimit()){e({code:r.FAILED,error:Error("Concurrent export limit reached")});return}var o=this._serializer.serializeRequest(t);if(null==o){e({code:r.FAILED,error:Error("Nothing to send")});return}this._promiseQueue.pushPromise(this._transport.send(o,this._timeout).then(function(t){if("success"===t.status){if(null!=t.data)try{n._responseHandler.handleResponse(n._serializer.deserializeResponse(t.data))}catch(e){n._diagLogger.warn("Export succeeded but could not deserialize response - is the response specification compliant?",e,t.data)}e({code:r.SUCCESS});return}if("failure"===t.status&&t.error){e({code:r.FAILED,error:t.error});return}"retryable"===t.status?e({code:r.FAILED,error:new rt("Export failed with retryable status")}):e({code:r.FAILED,error:new rt("Export failed with unknown error")})},function(t){return e({code:r.FAILED,error:t})}))},t.prototype.forceFlush=function(){return this._promiseQueue.awaitAll()},t.prototype.shutdown=function(){var t,e,r,n;return t=this,e=void 0,r=void 0,n=function(){return re(this,function(t){switch(t.label){case 0:return this._diagLogger.debug("shutdown started"),[4,this.forceFlush()];case 1:return t.sent(),this._transport.shutdown(),[2]}})},new(r||(r=Promise))(function(o,i){function a(t){try{s(n.next(t))}catch(t){i(t)}}function u(t){try{s(n.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,u)}s((n=n.apply(t,e||[])).next())})},t}();function rn(t,e,r){var n,o;return n={transport:r,serializer:e,promiseHandler:new e9(t.concurrencyLimit)},o={timeout:t.timeoutMillis},new rr(n.transport,n.serializer,{handleResponse:function(t){var e;if(null!=t&&(e=t,!!Object.prototype.hasOwnProperty.call(e,"partialSuccess"))&&null!=t.partialSuccess&&0!==Object.keys(t.partialSuccess).length)t$.warn("Received Partial Success response:",JSON.stringify(t.partialSuccess))}},n.promiseHandler,o.timeout)}var ro=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},ri=function(){return(ri=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r],e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)};function ra(t,e,r,n){var o,i,a,u,s,c,l,f,p,h,d,y,_,v,g,m,b,E,T,S,w,O,A,L,R,x,P,C=!!t.headers||"function"!=typeof navigator.sendBeacon;var N=(o=t,i=r,a=n,c={url:o.url,timeoutMillis:o.timeoutMillis,headers:function(t){if(null!=t)return function(){return t}}(o.headers),concurrencyLimit:o.concurrencyLimit},l={},f=(u=a,s=i,ri(ri({},{timeoutMillis:1e4,concurrencyLimit:30,compression:"none"}),{headers:function(){return u},url:"http://localhost:4318/"+s,agentOptions:{keepAlive:!0}})),ri(ri({},(_=c,v=l,g=f,{timeoutMillis:function(t){if(!Number.isNaN(t)&&Number.isFinite(t)&&t>0)return t;throw Error("Configuration: timeoutMillis is invalid, expected number greater than 0 (actual: '"+t+"')")}(null!==(b=null!==(m=_.timeoutMillis)&&void 0!==m?m:v.timeoutMillis)&&void 0!==b?b:g.timeoutMillis),concurrencyLimit:null!==(T=null!==(E=_.concurrencyLimit)&&void 0!==E?E:v.concurrencyLimit)&&void 0!==T?T:g.concurrencyLimit,compression:null!==(w=null!==(S=_.compression)&&void 0!==S?S:v.compression)&&void 0!==w?w:g.compression})),{headers:(A=(O=c.headers,function(){var t,e={};return Object.entries(null!==(t=null==O?void 0:O())&&void 0!==t?t:{}).forEach(function(t){var r=ro(t,2),n=r[0],o=r[1];void 0!==o?e[n]=String(o):t$.warn('Header "'+n+'" has invalid value ('+o+") and will be ignored")}),e}),L=l.headers,R=f.headers,x=ri({},R()),P={},function(){return null!=L&&Object.assign(P,L()),null!=A&&Object.assign(P,A()),Object.assign(P,x)}),url:null!==(h=null!==(p=function(t){if(null!=t)try{return new URL(t),t}catch(e){throw Error("Configuration: Could not parse user-provided export URL: '"+t+"'")}}(c.url))&&void 0!==p?p:l.url)&&void 0!==h?h:f.url,agentOptions:null!==(y=null!==(d=c.agentOptions)&&void 0!==d?d:l.agentOptions)&&void 0!==y?y:f.agentOptions}));return C?function(t,e){return rn(t,e,e3({transport:new e5(t)}))}(N,e):function(t,e){return rn(t,e,e3({transport:new e6({url:t.url,blobType:t.headers()["Content-Type"]})}))}(N,e)}var ru=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),rs=function(t){function e(e){return void 0===e&&(e={}),t.call(this,ra(e,e0,"v1/logs",{"Content-Type":"application/json"}))||this}return ru(e,t),e}(eA);!function(t){t.AlwaysOff="always_off",t.AlwaysOn="always_on",t.ParentBasedAlwaysOff="parentbased_always_off",t.ParentBasedAlwaysOn="parentbased_always_on",t.ParentBasedTraceIdRatio="parentbased_traceidratio",t.TraceIdRatio="traceidratio"}(n||(n={}));var rc=["OTEL_SDK_DISABLED"],rl=["OTEL_BSP_EXPORT_TIMEOUT","OTEL_BSP_MAX_EXPORT_BATCH_SIZE","OTEL_BSP_MAX_QUEUE_SIZE","OTEL_BSP_SCHEDULE_DELAY","OTEL_BLRP_EXPORT_TIMEOUT","OTEL_BLRP_MAX_EXPORT_BATCH_SIZE","OTEL_BLRP_MAX_QUEUE_SIZE","OTEL_BLRP_SCHEDULE_DELAY","OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_ATTRIBUTE_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT","OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT","OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT","OTEL_SPAN_EVENT_COUNT_LIMIT","OTEL_SPAN_LINK_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT","OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT","OTEL_EXPORTER_OTLP_TIMEOUT","OTEL_EXPORTER_OTLP_TRACES_TIMEOUT","OTEL_EXPORTER_OTLP_METRICS_TIMEOUT","OTEL_EXPORTER_OTLP_LOGS_TIMEOUT","OTEL_EXPORTER_JAEGER_AGENT_PORT"],rf=["OTEL_NO_PATCH_MODULES","OTEL_PROPAGATORS","OTEL_SEMCONV_STABILITY_OPT_IN"],rp=1/0,rh={OTEL_SDK_DISABLED:!1,CONTAINER_NAME:"",ECS_CONTAINER_METADATA_URI_V4:"",ECS_CONTAINER_METADATA_URI:"",HOSTNAME:"",KUBERNETES_SERVICE_HOST:"",NAMESPACE:"",OTEL_BSP_EXPORT_TIMEOUT:3e4,OTEL_BSP_MAX_EXPORT_BATCH_SIZE:512,OTEL_BSP_MAX_QUEUE_SIZE:2048,OTEL_BSP_SCHEDULE_DELAY:5e3,OTEL_BLRP_EXPORT_TIMEOUT:3e4,OTEL_BLRP_MAX_EXPORT_BATCH_SIZE:512,OTEL_BLRP_MAX_QUEUE_SIZE:2048,OTEL_BLRP_SCHEDULE_DELAY:5e3,OTEL_EXPORTER_JAEGER_AGENT_HOST:"",OTEL_EXPORTER_JAEGER_AGENT_PORT:6832,OTEL_EXPORTER_JAEGER_ENDPOINT:"",OTEL_EXPORTER_JAEGER_PASSWORD:"",OTEL_EXPORTER_JAEGER_USER:"",OTEL_EXPORTER_OTLP_ENDPOINT:"",OTEL_EXPORTER_OTLP_TRACES_ENDPOINT:"",OTEL_EXPORTER_OTLP_METRICS_ENDPOINT:"",OTEL_EXPORTER_OTLP_LOGS_ENDPOINT:"",OTEL_EXPORTER_OTLP_HEADERS:"",OTEL_EXPORTER_OTLP_TRACES_HEADERS:"",OTEL_EXPORTER_OTLP_METRICS_HEADERS:"",OTEL_EXPORTER_OTLP_LOGS_HEADERS:"",OTEL_EXPORTER_OTLP_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_TRACES_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_METRICS_TIMEOUT:1e4,OTEL_EXPORTER_OTLP_LOGS_TIMEOUT:1e4,OTEL_EXPORTER_ZIPKIN_ENDPOINT:"http://localhost:9411/api/v2/spans",OTEL_LOG_LEVEL:t.INFO,OTEL_NO_PATCH_MODULES:[],OTEL_PROPAGATORS:["tracecontext","baggage"],OTEL_RESOURCE_ATTRIBUTES:"",OTEL_SERVICE_NAME:"",OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT:rp,OTEL_ATTRIBUTE_COUNT_LIMIT:128,OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT:rp,OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT:128,OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT:rp,OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT:128,OTEL_SPAN_EVENT_COUNT_LIMIT:128,OTEL_SPAN_LINK_COUNT_LIMIT:128,OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT:128,OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT:128,OTEL_TRACES_EXPORTER:"",OTEL_TRACES_SAMPLER:n.ParentBasedAlwaysOn,OTEL_TRACES_SAMPLER_ARG:"",OTEL_LOGS_EXPORTER:"",OTEL_EXPORTER_OTLP_INSECURE:"",OTEL_EXPORTER_OTLP_TRACES_INSECURE:"",OTEL_EXPORTER_OTLP_METRICS_INSECURE:"",OTEL_EXPORTER_OTLP_LOGS_INSECURE:"",OTEL_EXPORTER_OTLP_CERTIFICATE:"",OTEL_EXPORTER_OTLP_TRACES_CERTIFICATE:"",OTEL_EXPORTER_OTLP_METRICS_CERTIFICATE:"",OTEL_EXPORTER_OTLP_LOGS_CERTIFICATE:"",OTEL_EXPORTER_OTLP_COMPRESSION:"",OTEL_EXPORTER_OTLP_TRACES_COMPRESSION:"",OTEL_EXPORTER_OTLP_METRICS_COMPRESSION:"",OTEL_EXPORTER_OTLP_LOGS_COMPRESSION:"",OTEL_EXPORTER_OTLP_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_TRACES_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_METRICS_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_LOGS_CLIENT_KEY:"",OTEL_EXPORTER_OTLP_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_TRACES_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_METRICS_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_LOGS_CLIENT_CERTIFICATE:"",OTEL_EXPORTER_OTLP_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_TRACES_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_METRICS_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_LOGS_PROTOCOL:"http/protobuf",OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE:"cumulative",OTEL_SEMCONV_STABILITY_OPT_IN:[]},rd={ALL:t.ALL,VERBOSE:t.VERBOSE,DEBUG:t.DEBUG,INFO:t.INFO,WARN:t.WARN,ERROR:t.ERROR,NONE:t.NONE};function ry(t){var e={};for(var r in rh)if("OTEL_LOG_LEVEL"===r)!function(t,e,r){var n=r[t];if("string"==typeof n){var o=rd[n.toUpperCase()];null!=o&&(e[t]=o)}}(r,e,t);else{;if(n=r,rc.indexOf(n)>-1)!function(t,e,r){if(void 0!==r[t]){var n=String(r[t]);e[t]="true"===n.toLowerCase()}}(r,e,t);else{if(o=r,rl.indexOf(o)>-1)!function(t,e,r,n,o){if(void 0===n&&(n=-1/0),void 0===o&&(o=1/0),void 0!==r[t]){var i=Number(r[t]);!isNaN(i)&&(i<n?e[t]=n:i>o?e[t]=o:e[t]=i)}}(r,e,t);else{;if(i=r,rf.indexOf(i)>-1)!function(t,e,r,n){void 0===n&&(n=",");var o=r[t];"string"==typeof o&&(e[t]=o.split(n).map(function(t){return t.trim()}))}(r,e,t);else{var n,o,i,a=t[r];null!=a&&(e[r]=String(a))}}}}return e}var r_="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof A.g?A.g:{};function rv(){return Object.assign({},rh,ry(r_))}function rg(){return ry(r_)}!function(t){t[t.DELTA=0]="DELTA",t[t.CUMULATIVE=1]="CUMULATIVE"}(o||(o={})),!function(t){t[t.INT=0]="INT",t[t.DOUBLE=1]="DOUBLE"}(i||(i={}));var rm=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),rb=function(t,e,r,n){return new(r||(r=Promise))(function(o,i){function a(t){try{s(n.next(t))}catch(t){i(t)}}function u(t){try{s(n.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,u)}s((n=n.apply(t,e||[])).next())})},rE=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(t){i=[6,t],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},rT=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},rS=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)(n||!(o in e))&&(!n&&(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},rw=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};function rO(t){return null!=t}function rA(t){var e=Object.keys(t);return 0===e.length?"":JSON.stringify((e=e.sort()).map(function(e){return[e,t[e]]}))}var rL=function(t){function e(r){var n=t.call(this,r)||this;return Object.setPrototypeOf(n,e.prototype),n}return rm(e,t),e}(Error);function rR(t,e){var r;return Promise.race([t,new Promise(function(t,n){r=setTimeout(function(){n(new rL("Operation timed out."))},e)})]).then(function(t){return clearTimeout(r),t},function(t){throw clearTimeout(r),t})}function rx(t){return"rejected"===t.status}function rP(t,e){var r=[];return t.forEach(function(t){r.push.apply(r,rS([],rT(e(t)),!1))}),r}function rC(t,e,r){var n,o,a,u;return!function(t){return null!=t.match(rN)}(t)&&t$.warn('Invalid metric name: "'+t+'". The metric name should be a ASCII string with a length no greater than 255 characters.'),{name:t,type:e,description:null!==(n=null==r?void 0:r.description)&&void 0!==n?n:"",unit:null!==(o=null==r?void 0:r.unit)&&void 0!==o?o:"",valueType:null!==(a=null==r?void 0:r.valueType)&&void 0!==a?a:i.DOUBLE,advice:null!==(u=null==r?void 0:r.advice)&&void 0!==u?u:{}}}!function(t){t.COUNTER="COUNTER",t.GAUGE="GAUGE",t.HISTOGRAM="HISTOGRAM",t.UP_DOWN_COUNTER="UP_DOWN_COUNTER",t.OBSERVABLE_COUNTER="OBSERVABLE_COUNTER",t.OBSERVABLE_GAUGE="OBSERVABLE_GAUGE",t.OBSERVABLE_UP_DOWN_COUNTER="OBSERVABLE_UP_DOWN_COUNTER"}(a||(a={}));var rN=/^[a-z][a-z0-9_.\-/]{0,254}$/i;!function(t){t[t.DROP=0]="DROP",t[t.SUM=1]="SUM",t[t.LAST_VALUE=2]="LAST_VALUE",t[t.HISTOGRAM=3]="HISTOGRAM",t[t.EXPONENTIAL_HISTOGRAM=4]="EXPONENTIAL_HISTOGRAM"}(u||(u={}));var rM=function(){function t(){this.kind=u.DROP}return t.prototype.createAccumulation=function(){},t.prototype.merge=function(t,e){},t.prototype.diff=function(t,e){},t.prototype.toMetricData=function(t,e,r,n){},t}();!function(t){t[t.HISTOGRAM=0]="HISTOGRAM",t[t.EXPONENTIAL_HISTOGRAM=1]="EXPONENTIAL_HISTOGRAM",t[t.GAUGE=2]="GAUGE",t[t.SUM=3]="SUM"}(s||(s={}));var rI=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},rU=function(){function t(t,e,r,n){void 0===r&&(r=0),void 0===n&&(n=!1),this.startTime=t,this.monotonic=e,this._current=r,this.reset=n}return t.prototype.record=function(t){if(!this.monotonic||!(t<0))this._current+=t},t.prototype.setStartTime=function(t){this.startTime=t},t.prototype.toPointValue=function(){return this._current},t}(),rk=function(){function t(t){this.monotonic=t,this.kind=u.SUM}return t.prototype.createAccumulation=function(t){return new rU(t,this.monotonic)},t.prototype.merge=function(t,e){var r=t.toPointValue(),n=e.toPointValue();return e.reset?new rU(e.startTime,this.monotonic,n,e.reset):new rU(t.startTime,this.monotonic,r+n)},t.prototype.diff=function(t,e){var r=t.toPointValue(),n=e.toPointValue();return this.monotonic&&r>n?new rU(e.startTime,this.monotonic,n,!0):new rU(e.startTime,this.monotonic,n-r)},t.prototype.toMetricData=function(t,e,r,n){return{descriptor:t,aggregationTemporality:e,dataPointType:s.SUM,dataPoints:r.map(function(t){var e=rI(t,2),r=e[0],o=e[1];return{attributes:r,startTime:o.startTime,endTime:n,value:o.toPointValue()}}),isMonotonic:this.monotonic}},t}(),rD=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},rB=function(){function t(t,e,r){void 0===e&&(e=0),void 0===r&&(r=[0,0]),this.startTime=t,this._current=e,this.sampleTime=r}return t.prototype.record=function(t){this._current=t,this.sampleTime=eR(Date.now())},t.prototype.setStartTime=function(t){this.startTime=t},t.prototype.toPointValue=function(){return this._current},t}(),rj=function(){function t(){this.kind=u.LAST_VALUE}return t.prototype.createAccumulation=function(t){return new rB(t)},t.prototype.merge=function(t,e){var r=eM(e.sampleTime)>=eM(t.sampleTime)?e:t;return new rB(t.startTime,r.toPointValue(),r.sampleTime)},t.prototype.diff=function(t,e){var r=eM(e.sampleTime)>=eM(t.sampleTime)?e:t;return new rB(e.startTime,r.toPointValue(),r.sampleTime)},t.prototype.toMetricData=function(t,e,r,n){return{descriptor:t,aggregationTemporality:e,dataPointType:s.GAUGE,dataPoints:r.map(function(t){var e=rD(t,2),r=e[0],o=e[1];return{attributes:r,startTime:o.startTime,endTime:n,value:o.toPointValue()}})}},t}(),rF=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},rG=function(){function t(t,e,r,n){if(void 0===r&&(r=!0),void 0===n){var o,i;(i=(o=e).map(function(){return 0})).push(0),n={buckets:{boundaries:o,counts:i},sum:0,count:0,hasMinMax:!1,min:1/0,max:-1/0}}this.startTime=t,this._boundaries=e,this._recordMinMax=r,this._current=n}return t.prototype.record=function(t){if(!Number.isNaN(t)){this._current.count+=1,this._current.sum+=t,this._recordMinMax&&(this._current.min=Math.min(t,this._current.min),this._current.max=Math.max(t,this._current.max),this._current.hasMinMax=!0);var e=function(t,e){for(var r=0,n=t.length-1,o=t.length;n>=r;){var i=r+Math.trunc((n-r)/2);t[i]<e?r=i+1:(o=i,n=i-1)}return o}(this._boundaries,t);this._current.buckets.counts[e]+=1}},t.prototype.setStartTime=function(t){this.startTime=t},t.prototype.toPointValue=function(){return this._current},t}(),rV=function(){function t(t,e){this._boundaries=t,this._recordMinMax=e,this.kind=u.HISTOGRAM}return t.prototype.createAccumulation=function(t){return new rG(t,this._boundaries,this._recordMinMax)},t.prototype.merge=function(t,e){for(var r=t.toPointValue(),n=e.toPointValue(),o=r.buckets.counts,i=n.buckets.counts,a=Array(o.length),u=0;u<o.length;u++)a[u]=o[u]+i[u];var s=1/0,c=-1/0;return this._recordMinMax&&(r.hasMinMax&&n.hasMinMax?(s=Math.min(r.min,n.min),c=Math.max(r.max,n.max)):r.hasMinMax?(s=r.min,c=r.max):n.hasMinMax&&(s=n.min,c=n.max)),new rG(t.startTime,r.buckets.boundaries,this._recordMinMax,{buckets:{boundaries:r.buckets.boundaries,counts:a},count:r.count+n.count,sum:r.sum+n.sum,hasMinMax:this._recordMinMax&&(r.hasMinMax||n.hasMinMax),min:s,max:c})},t.prototype.diff=function(t,e){for(var r=t.toPointValue(),n=e.toPointValue(),o=r.buckets.counts,i=n.buckets.counts,a=Array(o.length),u=0;u<o.length;u++)a[u]=i[u]-o[u];return new rG(e.startTime,r.buckets.boundaries,this._recordMinMax,{buckets:{boundaries:r.buckets.boundaries,counts:a},count:n.count-r.count,sum:n.sum-r.sum,hasMinMax:!1,min:1/0,max:-1/0})},t.prototype.toMetricData=function(t,e,r,n){return{descriptor:t,aggregationTemporality:e,dataPointType:s.HISTOGRAM,dataPoints:r.map(function(e){var r=rF(e,2),o=r[0],i=r[1],u=i.toPointValue(),s=t.type===a.GAUGE||t.type===a.UP_DOWN_COUNTER||t.type===a.OBSERVABLE_GAUGE||t.type===a.OBSERVABLE_UP_DOWN_COUNTER;return{attributes:o,startTime:i.startTime,endTime:n,value:{min:u.hasMinMax?u.min:void 0,max:u.hasMinMax?u.max:void 0,sum:s?void 0:u.sum,buckets:u.buckets,count:u.count}}})}},t}(),rH=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},rz=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)(n||!(o in e))&&(!n&&(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},rX=function(){function t(t,e,r,n){void 0===t&&(t=new rW),void 0===e&&(e=0),void 0===r&&(r=0),void 0===n&&(n=0),this.backing=t,this.indexBase=e,this.indexStart=r,this.indexEnd=n}return Object.defineProperty(t.prototype,"offset",{get:function(){return this.indexStart},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"length",{get:function(){return 0===this.backing.length||this.indexEnd===this.indexStart&&0===this.at(0)?0:this.indexEnd-this.indexStart+1},enumerable:!1,configurable:!0}),t.prototype.counts=function(){var t=this;return Array.from({length:this.length},function(e,r){return t.at(r)})},t.prototype.at=function(t){var e=this.indexBase-this.indexStart;return t<e&&(t+=this.backing.length),t-=e,this.backing.countAt(t)},t.prototype.incrementBucket=function(t,e){this.backing.increment(t,e)},t.prototype.decrementBucket=function(t,e){this.backing.decrement(t,e)},t.prototype.trim=function(){for(var t=0;t<this.length;t++){if(0!==this.at(t)){this.indexStart+=t;break}if(t===this.length-1){this.indexStart=this.indexEnd=this.indexBase=0;return}}for(var t=this.length-1;t>=0;t--)if(0!==this.at(t)){this.indexEnd-=this.length-t-1;break}this._rotate()},t.prototype.downscale=function(t){this._rotate();for(var e=1+this.indexEnd-this.indexStart,r=1<<t,n=0,o=0,i=this.indexStart;i<=this.indexEnd;){var a=i%r;a<0&&(a+=r);for(var u=a;u<r&&n<e;u++)this._relocateBucket(o,n),n++,i++;o++}this.indexStart>>=t,this.indexEnd>>=t,this.indexBase=this.indexStart},t.prototype.clone=function(){return new t(this.backing.clone(),this.indexBase,this.indexStart,this.indexEnd)},t.prototype._rotate=function(){var t=this.indexBase-this.indexStart;if(0!==t){t>0?(this.backing.reverse(0,this.backing.length),this.backing.reverse(0,t),this.backing.reverse(t,this.backing.length)):(this.backing.reverse(0,this.backing.length),this.backing.reverse(0,this.backing.length+t));this.indexBase=this.indexStart}},t.prototype._relocateBucket=function(t,e){if(t!==e)this.incrementBucket(t,this.backing.emptyBucket(e))},t}(),rW=function(){function t(t){void 0===t&&(t=[0]),this._counts=t}return Object.defineProperty(t.prototype,"length",{get:function(){return this._counts.length},enumerable:!1,configurable:!0}),t.prototype.countAt=function(t){return this._counts[t]},t.prototype.growTo=function(t,e,r){var n=Array(t).fill(0);n.splice.apply(n,rz([r,this._counts.length-e],rH(this._counts.slice(e)),!1)),n.splice.apply(n,rz([0,e],rH(this._counts.slice(0,e)),!1)),this._counts=n},t.prototype.reverse=function(t,e){for(var r=Math.floor((t+e)/2)-t,n=0;n<r;n++){var o=this._counts[t+n];this._counts[t+n]=this._counts[e-n-1],this._counts[e-n-1]=o}},t.prototype.emptyBucket=function(t){var e=this._counts[t];return this._counts[t]=0,e},t.prototype.increment=function(t,e){this._counts[t]+=e},t.prototype.decrement=function(t,e){this._counts[t]>=e?this._counts[t]-=e:this._counts[t]=0},t.prototype.clone=function(){return new t(rz([],rH(this._counts),!1))},t}(),rq=-1022;function rY(t){var e=new DataView(new ArrayBuffer(8));e.setFloat64(0,t);var r=e.getUint32(0);return((2146435072&r)>>20)-1023}function rQ(t){var e=new DataView(new ArrayBuffer(8));e.setFloat64(0,t);var r=e.getUint32(0),n=e.getUint32(4);return(1048575&r)*4294967296+n}function rK(t,e){return 0===t||t===Number.POSITIVE_INFINITY||t===Number.NEGATIVE_INFINITY||Number.isNaN(t)?t:t*Math.pow(2,e)}var rJ=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),rZ=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return rJ(e,t),e}(Error),r$=function(){function t(t){this._shift=-t}return t.prototype.mapToIndex=function(t){return t<22250738585072014e-324?this._minNormalLowerBoundaryIndex():rY(t)+this._rightShift(rQ(t)-1,52)>>this._shift},t.prototype.lowerBoundary=function(t){var e=this._minNormalLowerBoundaryIndex();if(t<e)throw new rZ("underflow: "+t+" is < minimum lower boundary: "+e);var r=this._maxNormalLowerBoundaryIndex();if(t>r)throw new rZ("overflow: "+t+" is > maximum lower boundary: "+r);return rK(1,t<<this._shift)},Object.defineProperty(t.prototype,"scale",{get:function(){return 0===this._shift?0:-this._shift},enumerable:!1,configurable:!0}),t.prototype._minNormalLowerBoundaryIndex=function(){var t=rq>>this._shift;return this._shift<2&&t--,t},t.prototype._maxNormalLowerBoundaryIndex=function(){return 1023>>this._shift},t.prototype._rightShift=function(t,e){return Math.floor(t*Math.pow(2,-e))},t}(),r0=function(){function t(t){this._scale=t,this._scaleFactor=rK(Math.LOG2E,t),this._inverseFactor=rK(Math.LN2,-t)}return t.prototype.mapToIndex=function(t){if(t<=22250738585072014e-324)return this._minNormalLowerBoundaryIndex()-1;if(0===rQ(t))return(rY(t)<<this._scale)-1;var e=Math.floor(Math.log(t)*this._scaleFactor),r=this._maxNormalLowerBoundaryIndex();return e>=r?r:e},t.prototype.lowerBoundary=function(t){var e=this._maxNormalLowerBoundaryIndex();if(t>=e){if(t===e)return 2*Math.exp((t-(1<<this._scale))/this._scaleFactor);throw new rZ("overflow: "+t+" is > maximum lower boundary: "+e)}var r=this._minNormalLowerBoundaryIndex();if(t<=r){if(t===r)return 22250738585072014e-324;if(t===r-1)return Math.exp((t+(1<<this._scale))/this._scaleFactor)/2;throw new rZ("overflow: "+t+" is < minimum lower boundary: "+r)}return Math.exp(t*this._inverseFactor)},Object.defineProperty(t.prototype,"scale",{get:function(){return this._scale},enumerable:!1,configurable:!0}),t.prototype._minNormalLowerBoundaryIndex=function(){return rq<<this._scale},t.prototype._maxNormalLowerBoundaryIndex=function(){return(1024<<this._scale)-1},t}(),r1=Array.from({length:31},function(t,e){return e>10?new r0(e-10):new r$(e-10)});function r2(t){if(t>20||t<-10)throw new rZ("expected scale >= -10 && <= 20, got: "+t);return r1[t+10]}var r3=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},r4=function(){function t(t,e){this.low=t,this.high=e}return t.combine=function(e,r){return new t(Math.min(e.low,r.low),Math.max(e.high,r.high))},t}(),r5=function(){function t(t,e,r,n,o,i,a,u,s,c,l){void 0===e&&(e=160),void 0===r&&(r=!0),void 0===n&&(n=0),void 0===o&&(o=0),void 0===i&&(i=0),void 0===a&&(a=Number.POSITIVE_INFINITY),void 0===u&&(u=Number.NEGATIVE_INFINITY),void 0===s&&(s=new rX),void 0===c&&(c=new rX),void 0===l&&(l=r2(20)),this.startTime=t,this._maxSize=e,this._recordMinMax=r,this._sum=n,this._count=o,this._zeroCount=i,this._min=a,this._max=u,this._positive=s,this._negative=c,this._mapping=l,this._maxSize<2&&(t$.warn("Exponential Histogram Max Size set to "+this._maxSize+",                 changing to the minimum size of: 2"),this._maxSize=2)}return t.prototype.record=function(t){this.updateByIncrement(t,1)},t.prototype.setStartTime=function(t){this.startTime=t},t.prototype.toPointValue=function(){return{hasMinMax:this._recordMinMax,min:this.min,max:this.max,sum:this.sum,positive:{offset:this.positive.offset,bucketCounts:this.positive.counts()},negative:{offset:this.negative.offset,bucketCounts:this.negative.counts()},count:this.count,scale:this.scale,zeroCount:this.zeroCount}},Object.defineProperty(t.prototype,"sum",{get:function(){return this._sum},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"min",{get:function(){return this._min},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"max",{get:function(){return this._max},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"count",{get:function(){return this._count},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"zeroCount",{get:function(){return this._zeroCount},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"scale",{get:function(){return this._count===this._zeroCount?0:this._mapping.scale},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"positive",{get:function(){return this._positive},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"negative",{get:function(){return this._negative},enumerable:!1,configurable:!0}),t.prototype.updateByIncrement=function(t,e){if(!Number.isNaN(t)){if(t>this._max&&(this._max=t),t<this._min&&(this._min=t),this._count+=e,0===t){this._zeroCount+=e;return}this._sum+=t*e,t>0?this._updateBuckets(this._positive,t,e):this._updateBuckets(this._negative,-t,e)}},t.prototype.merge=function(t){0===this._count?(this._min=t.min,this._max=t.max):0!==t.count&&(t.min<this.min&&(this._min=t.min),t.max>this.max&&(this._max=t.max)),this.startTime=t.startTime,this._sum+=t.sum,this._count+=t.count,this._zeroCount+=t.zeroCount;var e=this._minScale(t);this._downscale(this.scale-e),this._mergeBuckets(this.positive,t,t.positive,e),this._mergeBuckets(this.negative,t,t.negative,e)},t.prototype.diff=function(t){this._min=1/0,this._max=-1/0,this._sum-=t.sum,this._count-=t.count,this._zeroCount-=t.zeroCount;var e=this._minScale(t);this._downscale(this.scale-e),this._diffBuckets(this.positive,t,t.positive,e),this._diffBuckets(this.negative,t,t.negative,e)},t.prototype.clone=function(){return new t(this.startTime,this._maxSize,this._recordMinMax,this._sum,this._count,this._zeroCount,this._min,this._max,this.positive.clone(),this.negative.clone(),this._mapping)},t.prototype._updateBuckets=function(t,e,r){var n=this._mapping.mapToIndex(e),o=!1,i=0,a=0;if(0===t.length?(t.indexStart=n,t.indexEnd=t.indexStart,t.indexBase=t.indexStart):n<t.indexStart&&t.indexEnd-n>=this._maxSize?(o=!0,a=n,i=t.indexEnd):n>t.indexEnd&&n-t.indexStart>=this._maxSize&&(o=!0,a=t.indexStart,i=n),o){var u=this._changeScale(i,a);this._downscale(u),n=this._mapping.mapToIndex(e)}this._incrementIndexBy(t,n,r)},t.prototype._incrementIndexBy=function(t,e,r){if(0!==r){if(0===t.length&&(t.indexStart=t.indexEnd=t.indexBase=e),e<t.indexStart){var n=t.indexEnd-e;n>=t.backing.length&&this._grow(t,n+1),t.indexStart=e}else if(e>t.indexEnd){var n=e-t.indexStart;n>=t.backing.length&&this._grow(t,n+1),t.indexEnd=e}var o=e-t.indexBase;o<0&&(o+=t.backing.length),t.incrementBucket(o,r)}},t.prototype._grow=function(t,e){var r,n=t.backing.length,o=t.indexBase-t.indexStart;var i=(r=e,r--,r|=r>>1,r|=r>>2,r|=r>>4,r|=r>>8,r|=r>>16,++r);i>this._maxSize&&(i=this._maxSize);var a=i-o;t.backing.growTo(i,n-o,a)},t.prototype._changeScale=function(t,e){for(var r=0;t-e>=this._maxSize;)t>>=1,e>>=1,r++;return r},t.prototype._downscale=function(t){if(0!==t){if(t<0)throw Error("impossible change of scale: "+this.scale);var e=this._mapping.scale-t;this._positive.downscale(t),this._negative.downscale(t),this._mapping=r2(e)}},t.prototype._minScale=function(t){var e=Math.min(this.scale,t.scale),r=r4.combine(this._highLowAtScale(this.positive,this.scale,e),this._highLowAtScale(t.positive,t.scale,e)),n=r4.combine(this._highLowAtScale(this.negative,this.scale,e),this._highLowAtScale(t.negative,t.scale,e));return Math.min(e-this._changeScale(r.high,r.low),e-this._changeScale(n.high,n.low))},t.prototype._highLowAtScale=function(t,e,r){if(0===t.length)return new r4(0,-1);var n=e-r;return new r4(t.indexStart>>n,t.indexEnd>>n)},t.prototype._mergeBuckets=function(t,e,r,n){for(var o=r.offset,i=e.scale-n,a=0;a<r.length;a++)this._incrementIndexBy(t,o+a>>i,r.at(a))},t.prototype._diffBuckets=function(t,e,r,n){for(var o=r.offset,i=e.scale-n,a=0;a<r.length;a++){var u=(o+a>>i)-t.indexBase;u<0&&(u+=t.backing.length),t.decrementBucket(u,r.at(a))}t.trim()},t}(),r6=function(){function t(t,e){this._maxSize=t,this._recordMinMax=e,this.kind=u.EXPONENTIAL_HISTOGRAM}return t.prototype.createAccumulation=function(t){return new r5(t,this._maxSize,this._recordMinMax)},t.prototype.merge=function(t,e){var r=e.clone();return r.merge(t),r},t.prototype.diff=function(t,e){var r=e.clone();return r.diff(t),r},t.prototype.toMetricData=function(t,e,r,n){return{descriptor:t,aggregationTemporality:e,dataPointType:s.EXPONENTIAL_HISTOGRAM,dataPoints:r.map(function(e){var r=r3(e,2),o=r[0],i=r[1],u=i.toPointValue(),s=t.type===a.GAUGE||t.type===a.UP_DOWN_COUNTER||t.type===a.OBSERVABLE_GAUGE||t.type===a.OBSERVABLE_UP_DOWN_COUNTER;return{attributes:o,startTime:i.startTime,endTime:n,value:{min:u.hasMinMax?u.min:void 0,max:u.hasMinMax?u.max:void 0,sum:s?void 0:u.sum,positive:{offset:u.positive.offset,bucketCounts:u.positive.bucketCounts},negative:{offset:u.negative.offset,bucketCounts:u.negative.bucketCounts},count:u.count,scale:u.scale,zeroCount:u.zeroCount}}})}},t}(),r8=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),r9=function(){function t(){}return t.Drop=function(){return na},t.Sum=function(){return nu},t.LastValue=function(){return ns},t.Histogram=function(){return nc},t.ExponentialHistogram=function(){return nl},t.Default=function(){return nf},t}(),r7=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r8(e,t),e.prototype.createAggregator=function(t){return e.DEFAULT_INSTANCE},e.DEFAULT_INSTANCE=new rM,e}(r9),nt=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r8(e,t),e.prototype.createAggregator=function(t){switch(t.type){case a.COUNTER:case a.OBSERVABLE_COUNTER:case a.HISTOGRAM:return e.MONOTONIC_INSTANCE;default:return e.NON_MONOTONIC_INSTANCE}},e.MONOTONIC_INSTANCE=new rk(!0),e.NON_MONOTONIC_INSTANCE=new rk(!1),e}(r9),ne=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r8(e,t),e.prototype.createAggregator=function(t){return e.DEFAULT_INSTANCE},e.DEFAULT_INSTANCE=new rj,e}(r9),nr=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r8(e,t),e.prototype.createAggregator=function(t){return e.DEFAULT_INSTANCE},e.DEFAULT_INSTANCE=new rV([0,5,10,25,50,75,100,250,500,750,1e3,2500,5e3,7500,1e4],!0),e}(r9),nn=function(t){function e(e,r){void 0===r&&(r=!0);var n=t.call(this)||this;if(n._recordMinMax=r,null==e)throw Error("ExplicitBucketHistogramAggregation should be created with explicit boundaries, if a single bucket histogram is required, please pass an empty array");var o=(e=(e=e.concat()).sort(function(t,e){return t-e})).lastIndexOf(-1/0),i=e.indexOf(1/0);return -1===i&&(i=void 0),n._boundaries=e.slice(o+1,i),n}return r8(e,t),e.prototype.createAggregator=function(t){return new rV(this._boundaries,this._recordMinMax)},e}(r9),no=function(t){function e(e,r){void 0===e&&(e=160),void 0===r&&(r=!0);var n=t.call(this)||this;return n._maxSize=e,n._recordMinMax=r,n}return r8(e,t),e.prototype.createAggregator=function(t){return new r6(this._maxSize,this._recordMinMax)},e}(r9),ni=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r8(e,t),e.prototype._resolve=function(t){switch(t.type){case a.COUNTER:case a.UP_DOWN_COUNTER:case a.OBSERVABLE_COUNTER:case a.OBSERVABLE_UP_DOWN_COUNTER:return nu;case a.GAUGE:case a.OBSERVABLE_GAUGE:return ns;case a.HISTOGRAM:if(t.advice.explicitBucketBoundaries)return new nn(t.advice.explicitBucketBoundaries);return nc}return t$.warn("Unable to recognize instrument type: "+t.type),na},e.prototype.createAggregator=function(t){return this._resolve(t).createAggregator(t)},e}(r9),na=new r7,nu=new nt,ns=new ne,nc=new nr,nl=new no,nf=new ni;!function(t){t[t.DELTA=0]="DELTA",t[t.CUMULATIVE=1]="CUMULATIVE",t[t.LOWMEMORY=2]="LOWMEMORY"}(c||(c={}));var np=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),nh=function(){return o.CUMULATIVE},nd=function(t){switch(t){case a.COUNTER:case a.OBSERVABLE_COUNTER:case a.GAUGE:case a.HISTOGRAM:case a.OBSERVABLE_GAUGE:return o.DELTA;case a.UP_DOWN_COUNTER:case a.OBSERVABLE_UP_DOWN_COUNTER:return o.CUMULATIVE}},ny=function(t){switch(t){case a.COUNTER:case a.HISTOGRAM:return o.DELTA;case a.GAUGE:case a.UP_DOWN_COUNTER:case a.OBSERVABLE_UP_DOWN_COUNTER:case a.OBSERVABLE_COUNTER:case a.OBSERVABLE_GAUGE:return o.CUMULATIVE}},n_=function(t){function e(e,r){var n,o=t.call(this,e)||this;return o._aggregationSelector=(null==(n=r)?void 0:n.aggregationPreference)?n.aggregationPreference:function(t){return r9.Default()},o._aggregationTemporalitySelector=function(t){var e,r;if(null!=t)return t===c.DELTA?nd:t===c.LOWMEMORY?ny:nh;return"cumulative"===(r=(e=rv()).OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE.trim().toLowerCase())?nh:"delta"===r?nd:"lowmemory"===r?ny:(t$.warn("OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE is set to '"+e.OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE+"', but only 'cumulative' and 'delta' are allowed. Using default ('cumulative') instead."),nh)}(null==r?void 0:r.temporalityPreference),o}return np(e,t),e.prototype.selectAggregation=function(t){return this._aggregationSelector(t)},e.prototype.selectAggregationTemporality=function(t){return this._aggregationTemporalitySelector(t)},e}(eA);function nv(t,e){return t.dataPoints.map(function(r){return function(t,e,r){var n={attributes:eQ(t.attributes),startTimeUnixNano:r.encodeHrTime(t.startTime),timeUnixNano:r.encodeHrTime(t.endTime)};switch(e){case i.INT:n.asInt=t.value;break;case i.DOUBLE:n.asDouble=t.value}return n}(r,t.descriptor.valueType,e)})}var ng={serializeRequest:function(t){var e,r=(e={useLongBits:!1},{resourceMetrics:[t].map(function(t){var r,n;return r=t,n=eX(e),{resource:eq(r.resource),schemaUrl:void 0,scopeMetrics:function(t,e){return Array.from(t.map(function(t){return{scope:eY(t.scope),metrics:t.metrics.map(function(t){return function(t,e){var r={name:t.descriptor.name,description:t.descriptor.description,unit:t.descriptor.unit},n=function(t){switch(t){case o.DELTA:return 1;case o.CUMULATIVE:return 2}}(t.aggregationTemporality);switch(t.dataPointType){case s.SUM:r.sum={aggregationTemporality:n,isMonotonic:t.isMonotonic,dataPoints:nv(t,e)};break;case s.GAUGE:r.gauge={dataPoints:nv(t,e)};break;case s.HISTOGRAM:r.histogram={aggregationTemporality:n,dataPoints:function(t,e){return t.dataPoints.map(function(t){var r=t.value;return{attributes:eQ(t.attributes),bucketCounts:r.buckets.counts,explicitBounds:r.buckets.boundaries,count:r.count,sum:r.sum,min:r.min,max:r.max,startTimeUnixNano:e.encodeHrTime(t.startTime),timeUnixNano:e.encodeHrTime(t.endTime)}})}(t,e)};break;case s.EXPONENTIAL_HISTOGRAM:r.exponentialHistogram={aggregationTemporality:n,dataPoints:function(t,e){return t.dataPoints.map(function(t){var r=t.value;return{attributes:eQ(t.attributes),count:r.count,min:r.min,max:r.max,sum:r.sum,positive:{offset:r.positive.offset,bucketCounts:r.positive.bucketCounts},negative:{offset:r.negative.offset,bucketCounts:r.negative.bucketCounts},scale:r.scale,zeroCount:r.zeroCount,startTimeUnixNano:e.encodeHrTime(t.startTime),timeUnixNano:e.encodeHrTime(t.endTime)}})}(t,e)}}return r}(t,e)}),schemaUrl:t.scope.schemaUrl}}))}(r.scopeMetrics,n)}})});return new TextEncoder().encode(JSON.stringify(r))},deserializeResponse:function(t){return JSON.parse(new TextDecoder().decode(t))}},nm=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),nb=function(t){function e(e){return t.call(this,ra(null!=e?e:{},ng,"v1/metrics",{"Content-Type":"application/json"}))||this}return nm(e,t),e}(n_),nE=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},nT=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},nS={serializeRequest:function(t){var e={resourceSpans:function(t,e){for(var r=function(t){var e,r,n=new Map;try{for(var o=nE(t),i=o.next();!i.done;i=o.next()){var a=i.value,u=n.get(a.resource);!u&&(u=new Map,n.set(a.resource,u));var s=a.instrumentationLibrary.name+"@"+(a.instrumentationLibrary.version||"")+":"+(a.instrumentationLibrary.schemaUrl||""),c=u.get(s);!c&&(c=[],u.set(s,c)),c.push(a)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return n}(t),n=[],o=r.entries(),i=o.next();!i.done;){for(var a=nT(i.value,2),u=a[0],s=a[1],c=[],l=s.values(),f=l.next();!f.done;){var p=f.value;if(p.length>0){var h=p.map(function(t){return function(t,e){var r,n=t.spanContext(),o=t.status;return{traceId:e.encodeSpanContext(n.traceId),spanId:e.encodeSpanContext(n.spanId),parentSpanId:e.encodeOptionalSpanContext(t.parentSpanId),traceState:null===(r=n.traceState)||void 0===r?void 0:r.serialize(),name:t.name,kind:null==t.kind?0:t.kind+1,startTimeUnixNano:e.encodeHrTime(t.startTime),endTimeUnixNano:e.encodeHrTime(t.endTime),attributes:eQ(t.attributes),droppedAttributesCount:t.droppedAttributesCount,events:t.events.map(function(t){return function(t,e){return{attributes:t.attributes?eQ(t.attributes):[],name:t.name,timeUnixNano:e.encodeHrTime(t.time),droppedAttributesCount:t.droppedAttributesCount||0}}(t,e)}),droppedEventsCount:t.droppedEventsCount,status:{code:o.code,message:o.message},links:t.links.map(function(t){return function(t,e){var r;return{attributes:t.attributes?eQ(t.attributes):[],spanId:e.encodeSpanContext(t.context.spanId),traceId:e.encodeSpanContext(t.context.traceId),traceState:null===(r=t.context.traceState)||void 0===r?void 0:r.serialize(),droppedAttributesCount:t.droppedAttributesCount||0}}(t,e)}),droppedLinksCount:t.droppedLinksCount}}(t,e)});c.push({scope:eY(p[0].instrumentationLibrary),spans:h,schemaUrl:p[0].instrumentationLibrary.schemaUrl})}f=l.next()}var d={resource:eq(u),scopeSpans:c,schemaUrl:void 0};n.push(d),i=o.next()}return n}(t,eX({useHex:!0,useLongBits:!1}))};return new TextEncoder().encode(JSON.stringify(e))},deserializeResponse:function(t){return JSON.parse(new TextDecoder().decode(t))}},nw=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),nO=function(t){function e(e){return void 0===e&&(e={}),t.call(this,ra(e,nS,"v1/traces",{"Content-Type":"application/json"}))||this}return nw(e,t),e}(eA),nA="object"==typeof globalThis?globalThis:"object"==typeof self?self:"object"==typeof window?window:"object"==typeof A.g?A.g:{},nL=Symbol.for("io.opentelemetry.js.api.logs"),nR=function(){function t(){}return t.prototype.emit=function(t){},t}(),nx=new nR,nP=new(function(){function t(){}return t.prototype.getLogger=function(t,e,r){return new nR},t}()),nC=function(){function t(t,e,r,n){this._provider=t,this.name=e,this.version=r,this.options=n}return t.prototype.emit=function(t){this._getLogger().emit(t)},t.prototype._getLogger=function(){if(this._delegate)return this._delegate;var t=this._provider.getDelegateLogger(this.name,this.version,this.options);return t?(this._delegate=t,this._delegate):nx},t}(),nN=function(){function t(){}return t.prototype.getLogger=function(t,e,r){var n;return null!==(n=this.getDelegateLogger(t,e,r))&&void 0!==n?n:new nC(this,t,e,r)},t.prototype.getDelegate=function(){var t;return null!==(t=this._delegate)&&void 0!==t?t:nP},t.prototype.setDelegate=function(t){this._delegate=t},t.prototype.getDelegateLogger=function(t,e,r){var n;return null===(n=this._delegate)||void 0===n?void 0:n.getLogger(t,e,r)},t}(),nM=(function(){function t(){this._proxyLoggerProvider=new nN}return t.getInstance=function(){return!this._instance&&(this._instance=new t),this._instance},t.prototype.setGlobalLoggerProvider=function(t){var e,r;if(nA[nL])return this.getLoggerProvider();return nA[nL]=(e=t,r=nP,function(t){return 1===t?e:r}),this._proxyLoggerProvider.setDelegate(t),t},t.prototype.getLoggerProvider=function(){var t,e;return null!==(e=null===(t=nA[nL])||void 0===t?void 0:t.call(nA,1))&&void 0!==e?e:this._proxyLoggerProvider},t.prototype.getLogger=function(t,e,r){return this.getLoggerProvider().getLogger(t,e,r)},t.prototype.disable=function(){delete nA[nL],this._proxyLoggerProvider=new nN},t})().getInstance(),nI=Z.getInstance(),nU=k("OpenTelemetry SDK Context Key SUPPRESS_TRACING");function nk(t){return t.setValue(nU,!0)}function nD(t){return!0===t.getValue(nU)}var nB="[_0-9a-z-*/]",nj=RegExp("^(?:[a-z]"+nB+"{0,255}|"+("[a-z0-9]"+nB+"{0,240}@[a-z]"+nB)+"{0,13})$"),nF=/^[ -~]{0,255}[!-~]$/,nG=/,|=/,nV=function(){function t(t){this._internalState=new Map,t&&this._parse(t)}return t.prototype.set=function(t,e){var r=this._clone();return r._internalState.has(t)&&r._internalState.delete(t),r._internalState.set(t,e),r},t.prototype.unset=function(t){var e=this._clone();return e._internalState.delete(t),e},t.prototype.get=function(t){return this._internalState.get(t)},t.prototype.serialize=function(){var t=this;return this._keys().reduce(function(e,r){return e.push(r+"="+t.get(r)),e},[]).join(",")},t.prototype._parse=function(t){!(t.length>512)&&(this._internalState=t.split(",").reverse().reduce(function(t,e){var r=e.trim(),n=r.indexOf("=");if(-1!==n){var o,i,a=r.slice(0,n),u=r.slice(n+1,e.length);if(o=a,nj.test(o)&&(i=u,nF.test(i)&&!nG.test(i)))t.set(a,u)}return t},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))},t.prototype._keys=function(){return Array.from(this._internalState.keys()).reverse()},t.prototype._clone=function(){var e=new t;return e._internalState=new Map(this._internalState),e},t}(),nH="traceparent",nz="tracestate",nX=RegExp("^\\s?((?!ff)[\\da-f]{2})-((?![0]{32})[\\da-f]{32})-((?![0]{16})[\\da-f]{16})-([\\da-f]{2})(-.*)?\\s?$"),nW=function(){function t(){}return t.prototype.inject=function(t,r,n){var o=tT.getSpanContext(t);if(!(!o||nD(t))&&th(o)){var i="00-"+o.traceId+"-"+o.spanId+"-0"+Number(o.traceFlags||e.NONE).toString(16);n.set(r,nH,i),o.traceState&&n.set(r,nz,o.traceState.serialize())}},t.prototype.extract=function(t,e,r){var n,o,i=r.get(e,nH);if(!i)return t;var a=Array.isArray(i)?i[0]:i;if("string"!=typeof a)return t;var u=(n=a,(o=nX.exec(n))&&("00"!==o[1]||!o[5])?{traceId:o[2],spanId:o[3],traceFlags:parseInt(o[4],16)}:null);if(!u)return t;u.isRemote=!0;var s=r.get(e,nz);if(s){var c=Array.isArray(s)?s.join(","):s;u.traceState=new nV("string"==typeof c?c:void 0)}return tT.setSpanContext(t,u)},t.prototype.fields=function(){return[nH,nz]},t}();!function(t){t.CONNECT_END="connectEnd",t.CONNECT_START="connectStart",t.DECODED_BODY_SIZE="decodedBodySize",t.DOM_COMPLETE="domComplete",t.DOM_CONTENT_LOADED_EVENT_END="domContentLoadedEventEnd",t.DOM_CONTENT_LOADED_EVENT_START="domContentLoadedEventStart",t.DOM_INTERACTIVE="domInteractive",t.DOMAIN_LOOKUP_END="domainLookupEnd",t.DOMAIN_LOOKUP_START="domainLookupStart",t.ENCODED_BODY_SIZE="encodedBodySize",t.FETCH_START="fetchStart",t.LOAD_EVENT_END="loadEventEnd",t.LOAD_EVENT_START="loadEventStart",t.NAVIGATION_START="navigationStart",t.REDIRECT_END="redirectEnd",t.REDIRECT_START="redirectStart",t.REQUEST_START="requestStart",t.RESPONSE_END="responseEnd",t.RESPONSE_START="responseStart",t.SECURE_CONNECTION_START="secureConnectionStart",t.UNLOAD_EVENT_END="unloadEventEnd",t.UNLOAD_EVENT_START="unloadEventStart"}(l||(l={}));var nq=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};function nY(t,e){return"string"==typeof e?t===e:!!t.match(e)}var nQ="exception.type",nK="exception.message",nJ="http.url",nZ="http.user_agent";function n$(t,e){return e in t}function n0(t,e,r,n){var o=void 0,i=void 0;if(e in r&&"number"==typeof r[e])o=r[e];var a=n||l.FETCH_START;if(a in r&&"number"==typeof r[a])i=r[a];if(void 0!==o&&void 0!==i&&o>=i)return t.addEvent(e,o),t}function n1(t,e,r){if(void 0===r&&(r=!1),!r){if(n0(t,l.FETCH_START,e),n0(t,l.DOMAIN_LOOKUP_START,e),n0(t,l.DOMAIN_LOOKUP_END,e),n0(t,l.CONNECT_START,e),"name"in e&&e.name.startsWith("https:"))n0(t,l.SECURE_CONNECTION_START,e);n0(t,l.CONNECT_END,e),n0(t,l.REQUEST_START,e),n0(t,l.RESPONSE_START,e),n0(t,l.RESPONSE_END,e)}var n=e[l.ENCODED_BODY_SIZE];void 0!==n&&t.setAttribute("http.response_content_length",n);var o=e[l.DECODED_BODY_SIZE];void 0!==o&&n!==o&&t.setAttribute("http.response_content_length_uncompressed",o)}function n2(){return"undefined"!=typeof location?location.origin:void 0}function n3(t){if("function"==typeof URL)return new URL(t,"undefined"!=typeof document?document.baseURI:"undefined"!=typeof location?location.href:void 0);var e=(!f&&(f=document.createElement("a")),f);return e.href=t,e}var n4=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(t){i=[6,t],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}};function n5(t,e,r){var n,o;try{o=t()}catch(t){n=t}finally{if(e(n,o),n&&!r)throw n;return o}}var n6=A("592"),n8=function(){return(n8=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r],e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},n9=function(){function t(t,e,r){this.instrumentationName=t,this.instrumentationVersion=e,this._config={},this._wrap=n6.wrap,this._unwrap=n6.unwrap,this._massWrap=n6.massWrap,this._massUnwrap=n6.massUnwrap,this.setConfig(r),this._diag=t$.createComponentLogger({namespace:t}),this._tracer=tT.getTracer(t,e),this._meter=tz.getMeter(t,e),this._logger=nM.getLogger(t,e),this._updateMetricInstruments()}return Object.defineProperty(t.prototype,"meter",{get:function(){return this._meter},enumerable:!1,configurable:!0}),t.prototype.setMeterProvider=function(t){this._meter=t.getMeter(this.instrumentationName,this.instrumentationVersion),this._updateMetricInstruments()},Object.defineProperty(t.prototype,"logger",{get:function(){return this._logger},enumerable:!1,configurable:!0}),t.prototype.setLoggerProvider=function(t){this._logger=t.getLogger(this.instrumentationName,this.instrumentationVersion)},t.prototype.getModuleDefinitions=function(){var t,e=null!==(t=this.init())&&void 0!==t?t:[];return Array.isArray(e)?e:[e]},t.prototype._updateMetricInstruments=function(){},t.prototype.getConfig=function(){return this._config},t.prototype.setConfig=function(t){this._config=n8({enabled:!0},t)},t.prototype.setTracerProvider=function(t){this._tracer=t.getTracer(this.instrumentationName,this.instrumentationVersion)},Object.defineProperty(t.prototype,"tracer",{get:function(){return this._tracer},enumerable:!1,configurable:!0}),t.prototype._runSpanCustomizationHook=function(t,e,r,n){if(!!t)try{t(r,n)}catch(t){this._diag.error("Error running span customization hook due to exception in handler",{triggerName:e},t)}},t}(),n7=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),ot=function(t){function e(e,r,n){var o=t.call(this,e,r,n)||this;return o._config.enabled&&o.enable(),o}return n7(e,t),e}(n9);!function(t){t.DOCUMENT_LOAD="documentLoad",t.DOCUMENT_FETCH="documentFetch",t.RESOURCE_FETCH="resourceFetch"}(p||(p={}));!function(t){t.FIRST_PAINT="firstPaint",t.FIRST_CONTENTFUL_PAINT="firstContentfulPaint"}(h||(h={}));var oe=function(){var t,e={},r=null===(t=eL.getEntriesByType)||void 0===t?void 0:t.call(eL,"navigation")[0];if(r){var n=Object.values(l);n.forEach(function(t){if(t in r){var n=r[t];"number"==typeof n&&(e[t]=n)}})}else{var o=eL.timing;if(o){var n=Object.values(l);n.forEach(function(t){if(t in o){var r=o[t];"number"==typeof r&&(e[t]=r)}})}}return e},or={"first-paint":h.FIRST_PAINT,"first-contentful-paint":h.FIRST_CONTENTFUL_PAINT},on=function(t){var e,r=null===(e=eL.getEntriesByType)||void 0===e?void 0:e.call(eL,"paint");r&&r.forEach(function(e){var r=e.name,n=e.startTime;if(r in or)t.addEvent(or[r],n)})},oo=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),oi=function(t){function e(e){void 0===e&&(e={});var r=t.call(this,"@opentelemetry/instrumentation-document-load","0.44.0",e)||this;return r.component="document-load",r.version="1",r.moduleName=r.component,r}return oo(e,t),e.prototype.init=function(){},e.prototype._onDocumentLoaded=function(){var t=this;window.setTimeout(function(){t._collectPerformance()})},e.prototype._addResourcesSpans=function(t){var e,r=this,n=null===(e=eL.getEntriesByType)||void 0===e?void 0:e.call(eL,"resource");n&&n.forEach(function(e){r._initResourceSpan(e,t)})},e.prototype._collectPerformance=function(){var t=this,e=Array.from(document.getElementsByTagName("meta")).find(function(t){return t.getAttribute("name")===nH}),r=oe(),n=e&&e.content||"";nI.with(ec.extract(D,{traceparent:n}),function(){var e,n=t._startSpan(p.DOCUMENT_LOAD,l.FETCH_START,r);if(!!n)nI.with(tT.setSpan(nI.active(),n),function(){var e=t._startSpan(p.DOCUMENT_FETCH,l.FETCH_START,r);e&&(e.setAttribute(nJ,location.href),nI.with(tT.setSpan(nI.active(),e),function(){var n;!t.getConfig().ignoreNetworkEvents&&n1(e,r),t._addCustomAttributesOnSpan(e,null===(n=t.getConfig().applyCustomAttributesOnSpan)||void 0===n?void 0:n.documentFetch),t._endSpan(e,l.RESPONSE_END,r)}))}),n.setAttribute(nJ,location.href),n.setAttribute(nZ,navigator.userAgent),t._addResourcesSpans(n),!t.getConfig().ignoreNetworkEvents&&(n0(n,l.FETCH_START,r),n0(n,l.UNLOAD_EVENT_START,r),n0(n,l.UNLOAD_EVENT_END,r),n0(n,l.DOM_INTERACTIVE,r),n0(n,l.DOM_CONTENT_LOADED_EVENT_START,r),n0(n,l.DOM_CONTENT_LOADED_EVENT_END,r),n0(n,l.DOM_COMPLETE,r),n0(n,l.LOAD_EVENT_START,r),n0(n,l.LOAD_EVENT_END,r)),!t.getConfig().ignorePerformancePaintEvents&&on(n),t._addCustomAttributesOnSpan(n,null===(e=t.getConfig().applyCustomAttributesOnSpan)||void 0===e?void 0:e.documentLoad),t._endSpan(n,l.LOAD_EVENT_END,r)})},e.prototype._endSpan=function(t,e,r){if(t){if(e in r)t.end(r[e]);else t.end()}},e.prototype._initResourceSpan=function(t,e){var r,n=this._startSpan(p.RESOURCE_FETCH,l.FETCH_START,t,e);n&&(n.setAttribute(nJ,t.name),!this.getConfig().ignoreNetworkEvents&&n1(n,t),this._addCustomAttributesOnResourceSpan(n,t,null===(r=this.getConfig().applyCustomAttributesOnSpan)||void 0===r?void 0:r.resourceFetch),this._endSpan(n,l.RESPONSE_END,t))},e.prototype._startSpan=function(t,e,r,n){if(e in r&&"number"==typeof r[e])return this.tracer.startSpan(t,{startTime:r[e]},n?tT.setSpan(nI.active(),n):void 0)},e.prototype._waitForPageLoad=function(){"complete"===window.document.readyState?this._onDocumentLoaded():(this._onDocumentLoaded=this._onDocumentLoaded.bind(this),window.addEventListener("load",this._onDocumentLoaded))},e.prototype._addCustomAttributesOnSpan=function(t,e){var r=this;e&&n5(function(){return e(t)},function(t){if(!!t)r._diag.error("addCustomAttributesOnSpan",t)},!0)},e.prototype._addCustomAttributesOnResourceSpan=function(t,e,r){var n=this;r&&n5(function(){return r(t,e)},function(t){if(!!t)n._diag.error("addCustomAttributesOnResourceSpan",t)},!0)},e.prototype.enable=function(){window.removeEventListener("load",this._onDocumentLoaded),this._waitForPageLoad()},e.prototype.disable=function(){window.removeEventListener("load",this._onDocumentLoaded)},e}(ot);!function(t){t[t.INTERNAL=0]="INTERNAL",t[t.SERVER=1]="SERVER",t[t.CLIENT=2]="CLIENT",t[t.PRODUCER=3]="PRODUCER",t[t.CONSUMER=4]="CONSUMER"}(d||(d={})),!function(t){t.COMPONENT="component",t.HTTP_ERROR_NAME="http.error_name",t.HTTP_STATUS_TEXT="http.status_text"}(y||(y={}));var oa=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(t){i=[6,t],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},ou=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},os=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},oc=t$.createComponentLogger({namespace:"@opentelemetry/opentelemetry-instrumentation-fetch/utils"});function ol(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(t[0]instanceof URL||"string"==typeof t[0]){var r=t[1];if(!(null==r?void 0:r.body))return Promise.resolve();if(!(r.body instanceof ReadableStream))return Promise.resolve(function(t){return"undefined"!=typeof Document&&t instanceof Document?new XMLSerializer().serializeToString(document).length:t instanceof Blob?t.size:void 0!==t.byteLength?t.byteLength:t instanceof FormData?function(t){var e,r,n=0;try{for(var o=ou(t.entries()),i=o.next();!i.done;i=o.next()){var a=os(i.value,2),u=a[0],s=a[1];n+=u.length,s instanceof Blob?n+=s.size:n+=s.length}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return n}(t):t instanceof URLSearchParams?op(t.toString()):"string"==typeof t?op(t):void oc.warn("unknown body type")}(r.body));var n=function(t){if(!t.pipeThrough)return oc.warn("Platform has ReadableStream but not pipeThrough!"),{body:t,length:Promise.resolve(void 0)};var e,r=0,n=new Promise(function(t){e=t}),o=new TransformStream({start:function(){},transform:function(t,e){var n,o,i,a;return n=this,o=void 0,i=void 0,a=function(){var n;return oa(this,function(o){switch(o.label){case 0:return[4,t];case 1:return n=o.sent(),r+=n.byteLength,e.enqueue(t),[2]}})},new(i||(i=Promise))(function(t,e){function r(t){try{s(a.next(t))}catch(t){e(t)}}function u(t){try{s(a.throw(t))}catch(t){e(t)}}function s(e){var n;e.done?t(e.value):((n=e.value)instanceof i?n:new i(function(t){t(n)})).then(r,u)}s((a=a.apply(n,o||[])).next())})},flush:function(){e(r)}});return{body:t.pipeThrough(o),length:n}}(r.body),o=n.body,i=n.length;return r.body=o,i}var a=t[0];return(null==a?void 0:a.body)?a.clone().text().then(function(t){return op(t)}):Promise.resolve()}var of=new TextEncoder;function op(t){return of.encode(t).byteLength}var oh="0.57.0",od=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),oy=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},o_=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)(n||!(o in e))&&(!n&&(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},ov="object"==typeof process&&(null===(_=process.release)||void 0===_?void 0:_.name)==="node",og=function(t){function e(e){void 0===e&&(e={});var r=t.call(this,"@opentelemetry/instrumentation-fetch",oh,e)||this;return r.component="fetch",r.version=oh,r.moduleName=r.component,r._usedResources=new WeakSet,r._tasksCount=0,r}return od(e,t),e.prototype.init=function(){},e.prototype._addChildSpan=function(t,e){var r=this.tracer.startSpan("CORS Preflight",{startTime:e[l.FETCH_START]},tT.setSpan(nI.active(),t));n1(r,e,this.getConfig().ignoreNetworkEvents),r.end(e[l.RESPONSE_END])},e.prototype._addFinalSpanAttributes=function(t,e){var r=n3(e.url);t.setAttribute("http.status_code",e.status),null!=e.statusText&&t.setAttribute(y.HTTP_STATUS_TEXT,e.statusText),t.setAttribute("http.host",r.host),t.setAttribute("http.scheme",r.protocol.replace(":","")),"undefined"!=typeof navigator&&t.setAttribute(nZ,navigator.userAgent)},e.prototype._addHeaders=function(t,e){if(r=e,("string"==typeof(n=this.getConfig().propagateTraceHeaderCorsUrls||[])||n instanceof RegExp)&&(n=[n]),!(n3(r).origin===n2()||n.some(function(t){return nY(r,t)}))){var r,n,o={};ec.inject(nI.active(),o),Object.keys(o).length>0&&this._diag.debug("headers inject skipped due to CORS policy");return}if(t instanceof Request)ec.inject(nI.active(),t.headers,{set:function(t,e,r){return t.set(e,"string"==typeof r?r:String(r))}});else if(t.headers instanceof Headers)ec.inject(nI.active(),t.headers,{set:function(t,e,r){return t.set(e,"string"==typeof r?r:String(r))}});else if(t.headers instanceof Map)ec.inject(nI.active(),t.headers,{set:function(t,e,r){return t.set(e,"string"==typeof r?r:String(r))}});else{var o={};ec.inject(nI.active(),o),t.headers=Object.assign({},o,t.headers||{})}},e.prototype._clearResources=function(){0===this._tasksCount&&this.getConfig().clearTimingResources&&(performance.clearResourceTimings(),this._usedResources=new WeakSet)},e.prototype._createSpan=function(t,e){if(void 0===e&&(e={}),function(t,e){var r,n;if(!e)return!1;try{for(var o=nq(e),i=o.next();!i.done;i=o.next()){var a=i.value;if(nY(t,a))return!0}}catch(t){r={error:t}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return!1}(t,this.getConfig().ignoreUrls)){this._diag.debug("ignoring span as url matches ignored url");return}var r,n=(e.method||"GET").toUpperCase(),o="HTTP "+n;return this.tracer.startSpan(o,{kind:d.CLIENT,attributes:((r={})[y.COMPONENT]=this.moduleName,r["http.method"]=n,r[nJ]=t,r)})},e.prototype._findResourceAndAddNetworkEvents=function(t,e,r){var n=e.entries;if(!n.length){if(!performance.getEntriesByType)return;n=performance.getEntriesByType("resource")}var o=function(t,e,r,n,o,i){void 0===o&&(o=new WeakSet);var a=n3(t),u=function(t,e,r,n,o,i){var a=eN(e),u=eN(r),s=n.filter(function(e){var r=eN(eC(e[l.FETCH_START])),n=eN(eC(e[l.RESPONSE_END]));return e.initiatorType.toLowerCase()===(i||"xmlhttprequest")&&e.name===t&&r>=a&&n<=u});return s.length>0&&(s=s.filter(function(t){return!o.has(t)})),s}(t=a.toString(),e,r,n,o,i);if(0===u.length)return{mainRequest:void 0};if(1===u.length)return{mainRequest:u[0]};var s=u.slice().sort(function(t,e){var r=t[l.FETCH_START],n=e[l.FETCH_START];return r>n?1:r<n?-1:0});if(a.origin===n2()||!(s.length>1))return{mainRequest:u[0]};var c=s[0],f=function(t,e,r){for(var n,o=eN(r),i=eN(eC(e)),a=t[1],u=t.length,s=1;s<u;s++){var c=t[s],f=eN(eC(c[l.FETCH_START])),p=o-eN(eC(c[l.RESPONSE_END]));f>=i&&(!n||p<n)&&(n=p,a=c)}return a}(s,c[l.RESPONSE_END],r),p=c[l.RESPONSE_END];return f[l.FETCH_START]<p&&(f=c,c=void 0),{corsPreFlightRequest:c,mainRequest:f}}(e.spanUrl,e.startTime,r,n,this._usedResources,"fetch");if(o.mainRequest){var i=o.mainRequest;this._markResourceAsUsed(i);var a=o.corsPreFlightRequest;a&&(this._addChildSpan(t,a),this._markResourceAsUsed(a)),n1(t,i,this.getConfig().ignoreNetworkEvents)}},e.prototype._markResourceAsUsed=function(t){this._usedResources.add(t)},e.prototype._endSpan=function(t,e,r){var n=this,o=eR(Date.now()),i=eP();this._addFinalSpanAttributes(t,r),setTimeout(function(){var r;null===(r=e.observer)||void 0===r||r.disconnect(),n._findResourceAndAddNetworkEvents(t,e,i),n._tasksCount--,n._clearResources(),t.end(o)},300)},e.prototype._patchConstructor=function(){var t=this;return function(e){return function(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];var o=this,i=n3(r[0]instanceof Request?r[0].url:String(r[0])).href,a=r[0]instanceof Request?r[0]:r[1]||{},u=t._createSpan(i,a);if(!u)return e.apply(this,r);var s=t._prepareSpanData(i);function c(e,r){t._applyAttributesAfterFetch(e,a,r),t._endSpan(e,s,{status:r.status||0,statusText:r.message,url:i})}function l(e,r){t._applyAttributesAfterFetch(e,a,r),r.status>=200&&r.status<400?t._endSpan(e,s,r):t._endSpan(e,s,{status:r.status,statusText:r.statusText,url:i})}function f(t,e,r){try{var n=r.clone(),o=r.clone(),i=n.body;if(i){var a=i.getReader(),u=function(){a.read().then(function(e){e.done?l(t,o):u()},function(e){c(t,e)})};u()}else l(t,r)}finally{e(r)}}function p(t,e,r){try{c(t,r)}finally{e(r)}}return t.getConfig().measureRequestSize&&ol.apply(void 0,o_([],oy(r),!1)).then(function(t){t&&u.setAttribute("http.request_content_length_uncompressed",t)}).catch(function(e){t._diag.warn("getFetchBodyLength",e)}),new Promise(function(r,n){return nI.with(tT.setSpan(nI.active(),u),function(){return t._addHeaders(a,i),t._tasksCount++,e.apply(o,a instanceof Request?[a]:[i,a]).then(f.bind(o,u,r),p.bind(o,u,n))})})}}},e.prototype._applyAttributesAfterFetch=function(t,e,r){var n=this,o=this.getConfig().applyCustomAttributesOnSpan;o&&n5(function(){return o(t,e,r)},function(t){if(!!t)n._diag.error("applyCustomAttributesOnSpan",t)},!0)},e.prototype._prepareSpanData=function(t){var e=eP(),r=[];if("function"!=typeof PerformanceObserver)return{entries:r,startTime:e,spanUrl:t};var n=new PerformanceObserver(function(e){e.getEntries().forEach(function(e){"fetch"===e.initiatorType&&e.name===t&&r.push(e)})});return n.observe({entryTypes:["resource"]}),{entries:r,observer:n,startTime:e,spanUrl:t}},e.prototype.enable=function(){var t;if(ov){this._diag.warn("this instrumentation is intended for web usage only, it does not instrument Node.js's fetch()");return}if("function"==typeof(t=fetch)&&"function"==typeof t.__original&&"function"==typeof t.__unwrap&&!0===t.__wrapped)this._unwrap(r_,"fetch"),this._diag.debug("removing previous patch for constructor");this._wrap(r_,"fetch",this._patchConstructor())},e.prototype.disable=function(){if(!ov)this._unwrap(r_,"fetch"),this._usedResources=new WeakSet},e}(ot),om="telemetry.sdk.name",ob="telemetry.sdk.language",oE="telemetry.sdk.version",oT=((v={})[om]="opentelemetry",v["process.runtime.name"]="browser",v[ob]="webjs",v[oE]="1.30.0",v),oS=function(){return(oS=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r],e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},ow=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(t){i=[6,t],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},oO=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},oA=function(){function t(t,e){var r,n=this;this._attributes=t,this.asyncAttributesPending=null!=e,this._syncAttributes=null!==(r=this._attributes)&&void 0!==r?r:{},this._asyncAttributesPromise=null==e?void 0:e.then(function(t){return n._attributes=Object.assign({},n._attributes,t),n.asyncAttributesPending=!1,t},function(t){return t$.debug("a resource's async attributes promise rejected: %s",t),n.asyncAttributesPending=!1,{}})}return t.empty=function(){return t.EMPTY},t.default=function(){var e;return new t(((e={})["service.name"]="unknown_service",e[ob]=oT[ob],e[om]=oT[om],e[oE]=oT[oE],e))},Object.defineProperty(t.prototype,"attributes",{get:function(){var t;return this.asyncAttributesPending&&t$.error("Accessing resource attributes before async attributes settled"),null!==(t=this._attributes)&&void 0!==t?t:{}},enumerable:!1,configurable:!0}),t.prototype.waitForAsyncAttributes=function(){var t,e,r,n;return t=this,e=void 0,r=void 0,n=function(){return ow(this,function(t){switch(t.label){case 0:if(!this.asyncAttributesPending)return[3,2];return[4,this._asyncAttributesPromise];case 1:t.sent(),t.label=2;case 2:return[2]}})},new(r||(r=Promise))(function(o,i){function a(t){try{s(n.next(t))}catch(t){i(t)}}function u(t){try{s(n.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,u)}s((n=n.apply(t,e||[])).next())})},t.prototype.merge=function(e){var r,n=this;if(!e)return this;var o=oS(oS({},this._syncAttributes),null!==(r=e._syncAttributes)&&void 0!==r?r:e.attributes);return this._asyncAttributesPromise||e._asyncAttributesPromise?new t(o,Promise.all([this._asyncAttributesPromise,e._asyncAttributesPromise]).then(function(t){var r,o=oO(t,2),i=o[0],a=o[1];return oS(oS(oS(oS({},n._syncAttributes),i),null!==(r=e._syncAttributes)&&void 0!==r?r:e.attributes),a)})):new t(o)},t.EMPTY=new t({}),t}(),oL=Function.prototype.toString,oR=oL.call(Object),ox=function(t,e){return function(r){return t(e(r))}}(Object.getPrototypeOf,Object),oP=Object.prototype,oC=oP.hasOwnProperty,oN=Symbol?Symbol.toStringTag:void 0,oM=oP.toString;function oI(t){if(!function(t){return null!=t&&"object"==typeof t}(t)||"[object Object]"!==function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":oN&&oN in Object(t)?function(t){var e=oC.call(t,oN),r=t[oN],n=!1;try{t[oN]=void 0,n=!0}catch(t){}var o=oM.call(t);return n&&(e?t[oN]=r:delete t[oN]),o}(t):function(t){return oM.call(t)}(t)}(t))return!1;var e=ox(t);if(null===e)return!0;var r=oC.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&oL.call(r)===oR}function oU(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var r=t.shift(),n=new WeakMap;t.length>0;)r=function t(e,r,n,o){if(void 0===n&&(n=0),!(n>20)){if(n++,oG(e)||oG(r)||oj(r))i=ok(r);else if(oB(e)){if(i=e.slice(),oB(r))for(var i,a=0,u=r.length;a<u;a++)i.push(ok(r[a]));else if(oF(r)){for(var s=Object.keys(r),a=0,u=s.length;a<u;a++){var c=s[a];i[c]=ok(r[c])}}}else if(oF(e)){if(oF(r)){if(!function(t,e){return!!(oI(t)&&oI(e))||!1}(e,r))return r;i=Object.assign({},e);for(var s=Object.keys(r),a=0,u=s.length;a<u;a++){var c=s[a],l=r[c];if(oG(l))void 0===l?delete i[c]:i[c]=l;else{var f=i[c];if(oD(e,c,o)||oD(r,c,o))delete i[c];else{if(oF(f)&&oF(l)){var p=o.get(f)||[],h=o.get(l)||[];p.push({obj:e,key:c}),h.push({obj:r,key:c}),o.set(f,p),o.set(l,h)}i[c]=t(i[c],l,n,o)}}}}else i=r}return i}}(r,t.shift(),0,n);return r}function ok(t){return oB(t)?t.slice():t}function oD(t,e,r){for(var n=r.get(t[e])||[],o=0,i=n.length;o<i;o++){var a=n[o];if(a.key===e&&a.obj===t)return!0}return!1}function oB(t){return Array.isArray(t)}function oj(t){return"function"==typeof t}function oF(t){return!oG(t)&&!oB(t)&&!oj(t)&&"object"==typeof t}function oG(t){return"string"==typeof t||"number"==typeof t||"boolean"==typeof t||void 0===t||t instanceof Date||t instanceof RegExp||null===t}var oV=function(){function t(){var t=this;this._promise=new Promise(function(e,r){t._resolve=e,t._reject=r})}return Object.defineProperty(t.prototype,"promise",{get:function(){return this._promise},enumerable:!1,configurable:!0}),t.prototype.resolve=function(t){this._resolve(t)},t.prototype.reject=function(t){this._reject(t)},t}(),oH=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},oz=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)(n||!(o in e))&&(!n&&(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},oX=function(){function t(t,e){this._callback=t,this._that=e,this._isCalled=!1,this._deferred=new oV}return Object.defineProperty(t.prototype,"isCalled",{get:function(){return this._isCalled},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"promise",{get:function(){return this._deferred.promise},enumerable:!1,configurable:!0}),t.prototype.call=function(){for(var t,e=this,r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];if(!this._isCalled){this._isCalled=!0;try{Promise.resolve((t=this._callback).call.apply(t,oz([this._that],oH(r),!1))).then(function(t){return e._deferred.resolve(t)},function(t){return e._deferred.reject(t)})}catch(t){this._deferred.reject(t)}}return this._deferred.promise},t}(),oW=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},oq=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a};function oY(t){var e,r,n={};if("object"!=typeof t||null==t)return n;try{for(var o=oW(Object.entries(t)),i=o.next();!i.done;i=o.next()){var a=oq(i.value,2),u=a[0],s=a[1];if(!function(t){return"string"==typeof t&&t.length>0}(u)){t$.warn("Invalid attribute key: "+u);continue}if(!oQ(s)){t$.warn("Invalid attribute value set for key: "+u);continue}Array.isArray(s)?n[u]=s.slice():n[u]=s}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return n}function oQ(t){return null==t||(Array.isArray(t)?function(t){try{for(var e,r,n,o=oW(t),i=o.next();!i.done;i=o.next()){var a=i.value;if(null!=a){if(!n){if(oK(a)){n=typeof a;continue}return!1}if(typeof a===n)continue;return!1}}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return!0}(t):oK(t))}function oK(t){switch(typeof t){case"number":case"boolean":case"string":return!0}return!1}var oJ=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},oZ=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},o$=function(){function t(t,e,r){this.attributes={},this.totalAttributesCount=0,this._isReadonly=!1;var n=r.timestamp,o=r.observedTimestamp,i=r.severityNumber,a=r.severityText,u=r.body,s=r.attributes,c=r.context,l=Date.now();if(this.hrTime=eC(null!=n?n:l),this.hrTimeObserved=eC(null!=o?o:l),c){var f=tT.getSpanContext(c);f&&th(f)&&(this.spanContext=f)}this.severityNumber=i,this.severityText=a,this.body=u,this.resource=t.resource,this.instrumentationScope=e,this._logRecordLimits=t.logRecordLimits,this.setAttributes(void 0===s?{}:s)}return Object.defineProperty(t.prototype,"severityText",{get:function(){return this._severityText},set:function(t){if(!this._isLogRecordReadonly())this._severityText=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"severityNumber",{get:function(){return this._severityNumber},set:function(t){if(!this._isLogRecordReadonly())this._severityNumber=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"body",{get:function(){return this._body},set:function(t){if(!this._isLogRecordReadonly())this._body=t},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"droppedAttributesCount",{get:function(){return this.totalAttributesCount-Object.keys(this.attributes).length},enumerable:!1,configurable:!0}),t.prototype.setAttribute=function(t,e){return this._isLogRecordReadonly()||null===e?this:0===t.length?(t$.warn("Invalid attribute key: "+t),this):oQ(e)||"object"==typeof e&&!Array.isArray(e)&&Object.keys(e).length>0?(this.totalAttributesCount+=1,Object.keys(this.attributes).length>=this._logRecordLimits.attributeCountLimit&&!Object.prototype.hasOwnProperty.call(this.attributes,t))?(1===this.droppedAttributesCount&&t$.warn("Dropping extra attributes."),this):(oQ(e)?this.attributes[t]=this._truncateToSize(e):this.attributes[t]=e,this):(t$.warn("Invalid attribute value set for key: "+t),this)},t.prototype.setAttributes=function(t){var e,r;try{for(var n=oJ(Object.entries(t)),o=n.next();!o.done;o=n.next()){var i=oZ(o.value,2),a=i[0],u=i[1];this.setAttribute(a,u)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}return this},t.prototype.setBody=function(t){return this.body=t,this},t.prototype.setSeverityNumber=function(t){return this.severityNumber=t,this},t.prototype.setSeverityText=function(t){return this.severityText=t,this},t.prototype._makeReadonly=function(){this._isReadonly=!0},t.prototype._truncateToSize=function(t){var e=this,r=this._logRecordLimits.attributeValueLengthLimit;return r<=0?(t$.warn("Attribute value limit must be positive, got "+r),t):"string"==typeof t?this._truncateToLimitUtil(t,r):Array.isArray(t)?t.map(function(t){return"string"==typeof t?e._truncateToLimitUtil(t,r):t}):t},t.prototype._truncateToLimitUtil=function(t,e){return t.length<=e?t:t.substring(0,e)},t.prototype._isLogRecordReadonly=function(){return this._isReadonly&&t$.warn("Can not execute the operation on emitted log record"),this._isReadonly},t}(),o0=function(){return(o0=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r],e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},o1=function(){function t(t,e){this.instrumentationScope=t,this._sharedState=e}return t.prototype.emit=function(t){var e=t.context||nI.active(),r=new o$(this._sharedState,this.instrumentationScope,o0({context:e},t));this._sharedState.activeProcessor.onEmit(r,e),r._makeReadonly()},t}(),o2=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),o3=function(t){function e(r){var n=t.call(this,r)||this;return Object.setPrototypeOf(n,e.prototype),n}return o2(e,t),e}(Error);function o4(t,e){var r;return Promise.race([t,new Promise(function(t,n){r=setTimeout(function(){n(new o3("Operation timed out."))},e)})]).then(function(t){return clearTimeout(r),t},function(t){throw clearTimeout(r),t})}var o5=function(t,e,r,n){return new(r||(r=Promise))(function(o,i){function a(t){try{s(n.next(t))}catch(t){i(t)}}function u(t){try{s(n.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,u)}s((n=n.apply(t,e||[])).next())})},o6=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(t){i=[6,t],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},o8=function(){function t(t,e){this.processors=t,this.forceFlushTimeoutMillis=e}return t.prototype.forceFlush=function(){return o5(this,void 0,void 0,function(){var t;return o6(this,function(e){switch(e.label){case 0:return t=this.forceFlushTimeoutMillis,[4,Promise.all(this.processors.map(function(e){return o4(e.forceFlush(),t)}))];case 1:return e.sent(),[2]}})})},t.prototype.onEmit=function(t,e){this.processors.forEach(function(r){return r.onEmit(t,e)})},t.prototype.shutdown=function(){return o5(this,void 0,void 0,function(){return o6(this,function(t){switch(t.label){case 0:return[4,Promise.all(this.processors.map(function(t){return t.shutdown()}))];case 1:return t.sent(),[2]}})})},t}(),o9=function(){function t(){}return t.prototype.forceFlush=function(){return Promise.resolve()},t.prototype.onEmit=function(t,e){},t.prototype.shutdown=function(){return Promise.resolve()},t}(),o7=function(t,e,r){this.resource=t,this.forceFlushTimeoutMillis=e,this.logRecordLimits=r,this.loggers=new Map,this.registeredLogRecordProcessors=[],this.activeProcessor=new o9},it=function(){function t(t){void 0===t&&(t={});var e,r,n,o,i,a,u,s,c,l,f,p=oU({},{forceFlushTimeoutMillis:3e4,logRecordLimits:{attributeValueLengthLimit:rv().OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:rv().OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT},includeTraceContext:!0,mergeResourceWithDefaults:!0},t);var h=(e=p.mergeResourceWithDefaults,n=null!=(r=t.resource)?r:oA.empty(),e?oA.default().merge(n):n);this._sharedState=new o7(h,p.forceFlushTimeoutMillis,(o=p.logRecordLimits,f=ry(r_),{attributeCountLimit:null!==(u=null!==(a=null!==(i=o.attributeCountLimit)&&void 0!==i?i:f.OTEL_LOGRECORD_ATTRIBUTE_COUNT_LIMIT)&&void 0!==a?a:f.OTEL_ATTRIBUTE_COUNT_LIMIT)&&void 0!==u?u:128,attributeValueLengthLimit:null!==(l=null!==(c=null!==(s=o.attributeValueLengthLimit)&&void 0!==s?s:f.OTEL_LOGRECORD_ATTRIBUTE_VALUE_LENGTH_LIMIT)&&void 0!==c?c:f.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT)&&void 0!==l?l:rp})),this._shutdownOnce=new oX(this._shutdown,this)}return t.prototype.getLogger=function(t,e,r){if(this._shutdownOnce.isCalled)return t$.warn("A shutdown LoggerProvider cannot provide a Logger"),nx;!t&&t$.warn("Logger requested without instrumentation scope name.");var n=t||"unknown",o=n+"@"+(e||"")+":"+((null==r?void 0:r.schemaUrl)||"");return!this._sharedState.loggers.has(o)&&this._sharedState.loggers.set(o,new o1({name:n,version:e,schemaUrl:null==r?void 0:r.schemaUrl},this._sharedState)),this._sharedState.loggers.get(o)},t.prototype.addLogRecordProcessor=function(t){0===this._sharedState.registeredLogRecordProcessors.length&&this._sharedState.activeProcessor.shutdown().catch(function(t){return t$.error("Error while trying to shutdown current log record processor",t)}),this._sharedState.registeredLogRecordProcessors.push(t),this._sharedState.activeProcessor=new o8(this._sharedState.registeredLogRecordProcessors,this._sharedState.forceFlushTimeoutMillis)},t.prototype.forceFlush=function(){return this._shutdownOnce.isCalled?(t$.warn("invalid attempt to force flush after LoggerProvider shutdown"),this._shutdownOnce.promise):this._sharedState.activeProcessor.forceFlush()},t.prototype.shutdown=function(){return this._shutdownOnce.isCalled?(t$.warn("shutdown may only be called once per LoggerProvider"),this._shutdownOnce.promise):this._shutdownOnce.call()},t.prototype._shutdown=function(){return this._sharedState.activeProcessor.shutdown()},t}(),ie=function(t){t$.error(function(t){return"string"==typeof t?t:JSON.stringify(function(t){for(var e={},r=t;null!==r;)Object.getOwnPropertyNames(r).forEach(function(t){if(!e[t]){var n=r[t];n&&(e[t]=String(n))}}),r=Object.getPrototypeOf(r);return e}(t))}(t))};function ir(t){try{ie(t)}catch(t){}}function io(t){}var ii={_export:function(t,e){return new Promise(function(r){nI.with(nk(nI.active()),function(){t.export(e,function(t){r(t)})})})}},ia=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(t){i=[6,t],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},iu=function(){function t(t,e){this._exporter=t,this._finishedLogRecords=[];var r,n,o,i,a=rv();this._maxExportBatchSize=null!==(r=null==e?void 0:e.maxExportBatchSize)&&void 0!==r?r:a.OTEL_BLRP_MAX_EXPORT_BATCH_SIZE,this._maxQueueSize=null!==(n=null==e?void 0:e.maxQueueSize)&&void 0!==n?n:a.OTEL_BLRP_MAX_QUEUE_SIZE,this._scheduledDelayMillis=null!==(o=null==e?void 0:e.scheduledDelayMillis)&&void 0!==o?o:a.OTEL_BLRP_SCHEDULE_DELAY,this._exportTimeoutMillis=null!==(i=null==e?void 0:e.exportTimeoutMillis)&&void 0!==i?i:a.OTEL_BLRP_EXPORT_TIMEOUT,this._shutdownOnce=new oX(this._shutdown,this),this._maxExportBatchSize>this._maxQueueSize&&(t$.warn("BatchLogRecordProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize"),this._maxExportBatchSize=this._maxQueueSize)}return t.prototype.onEmit=function(t){if(!this._shutdownOnce.isCalled)this._addToBuffer(t)},t.prototype.forceFlush=function(){return this._shutdownOnce.isCalled?this._shutdownOnce.promise:this._flushAll()},t.prototype.shutdown=function(){return this._shutdownOnce.call()},t.prototype._shutdown=function(){var t,e,r,n;return t=this,e=void 0,r=void 0,n=function(){return ia(this,function(t){switch(t.label){case 0:return this.onShutdown(),[4,this._flushAll()];case 1:return t.sent(),[4,this._exporter.shutdown()];case 2:return t.sent(),[2]}})},new(r||(r=Promise))(function(o,i){function a(t){try{s(n.next(t))}catch(t){i(t)}}function u(t){try{s(n.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,u)}s((n=n.apply(t,e||[])).next())})},t.prototype._addToBuffer=function(t){if(!(this._finishedLogRecords.length>=this._maxQueueSize))this._finishedLogRecords.push(t),this._maybeStartTimer()},t.prototype._flushAll=function(){var t=this;return new Promise(function(e,r){for(var n=[],o=Math.ceil(t._finishedLogRecords.length/t._maxExportBatchSize),i=0;i<o;i++)n.push(t._flushOneBatch());Promise.all(n).then(function(){e()}).catch(r)})},t.prototype._flushOneBatch=function(){var t=this;return(this._clearTimer(),0===this._finishedLogRecords.length)?Promise.resolve():new Promise(function(e,r){o4(t._export(t._finishedLogRecords.splice(0,t._maxExportBatchSize)),t._exportTimeoutMillis).then(function(){return e()}).catch(r)})},t.prototype._maybeStartTimer=function(){var t=this;if(void 0===this._timer)this._timer=setTimeout(function(){t._flushOneBatch().then(function(){t._finishedLogRecords.length>0&&(t._clearTimer(),t._maybeStartTimer())}).catch(function(t){ir(t)})},this._scheduledDelayMillis),this._timer},t.prototype._clearTimer=function(){void 0!==this._timer&&(clearTimeout(this._timer),this._timer=void 0)},t.prototype._export=function(t){var e=this,n=function(){return ii._export(e._exporter,t).then(function(t){var e;t.code!==r.SUCCESS&&ir(null!==(e=t.error)&&void 0!==e?e:Error("BatchLogRecordProcessor: log record export failed (status "+t+")"))}).catch(ir)},o=t.map(function(t){return t.resource}).filter(function(t){return t.asyncAttributesPending});return 0===o.length?n():Promise.all(o.map(function(t){var e;return null===(e=t.waitForAsyncAttributes)||void 0===e?void 0:e.call(t)})).then(n,ir)},t}(),is=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),ic=function(t){function e(e,r){var n=t.call(this,e,r)||this;return n._onInit(r),n}return is(e,t),e.prototype.onShutdown=function(){if("undefined"!=typeof document)this._visibilityChangeListener&&document.removeEventListener("visibilitychange",this._visibilityChangeListener),this._pageHideListener&&document.removeEventListener("pagehide",this._pageHideListener)},e.prototype._onInit=function(t){var e=this;if((null==t?void 0:t.disableAutoFlushOnDocumentHide)!==!0&&"undefined"!=typeof document)this._visibilityChangeListener=function(){"hidden"===document.visibilityState&&e.forceFlush()},this._pageHideListener=function(){e.forceFlush()},document.addEventListener("visibilitychange",this._visibilityChangeListener),document.addEventListener("pagehide",this._pageHideListener)},e}(iu),il=function(){function t(){this._registeredViews=[]}return t.prototype.addView=function(t){this._registeredViews.push(t)},t.prototype.findViews=function(t,e){var r=this;return this._registeredViews.filter(function(n){return r._matchInstrument(n.instrumentSelector,t)&&r._matchMeter(n.meterSelector,e)})},t.prototype._matchInstrument=function(t,e){return(void 0===t.getType()||e.type===t.getType())&&t.getNameFilter().match(e.name)&&t.getUnitFilter().match(e.unit)},t.prototype._matchMeter=function(t,e){return t.getNameFilter().match(e.name)&&(void 0===e.version||t.getVersionFilter().match(e.version))&&(void 0===e.schemaUrl||t.getSchemaUrlFilter().match(e.schemaUrl))},t}(),ip=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),ih=function(){function t(t,e){this._writableMetricStorage=t,this._descriptor=e}return t.prototype._record=function(t,e,r){if(void 0===e&&(e={}),void 0===r&&(r=nI.active()),"number"!=typeof t){t$.warn("non-number value provided to metric "+this._descriptor.name+": "+t);return}if(this._descriptor.valueType!==i.INT||!!Number.isInteger(t)||(t$.warn("INT value type cannot accept a floating-point value for "+this._descriptor.name+", ignoring the fractional digits."),!!Number.isInteger(t=Math.trunc(t))))this._writableMetricStorage.record(t,e,r,eR(Date.now()))},t}(),id=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ip(e,t),e.prototype.add=function(t,e,r){this._record(t,e,r)},e}(ih),iy=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ip(e,t),e.prototype.add=function(t,e,r){if(t<0){t$.warn("negative value provided to counter "+this._descriptor.name+": "+t);return}this._record(t,e,r)},e}(ih),i_=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ip(e,t),e.prototype.record=function(t,e,r){this._record(t,e,r)},e}(ih),iv=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ip(e,t),e.prototype.record=function(t,e,r){if(t<0){t$.warn("negative value provided to histogram "+this._descriptor.name+": "+t);return}this._record(t,e,r)},e}(ih),ig=function(){function t(t,e,r){this._observableRegistry=r,this._descriptor=t,this._metricStorages=e}return t.prototype.addCallback=function(t){this._observableRegistry.addCallback(t,this)},t.prototype.removeCallback=function(t){this._observableRegistry.removeCallback(t,this)},t}(),im=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ip(e,t),e}(ig),ib=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ip(e,t),e}(ig),iE=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return ip(e,t),e}(ig);function iT(t){return t instanceof ig}var iS=function(){function t(t){this._meterSharedState=t}return t.prototype.createGauge=function(t,e){var r=rC(t,a.GAUGE,e);return new i_(this._meterSharedState.registerMetricStorage(r),r)},t.prototype.createHistogram=function(t,e){var r=rC(t,a.HISTOGRAM,e);return new iv(this._meterSharedState.registerMetricStorage(r),r)},t.prototype.createCounter=function(t,e){var r=rC(t,a.COUNTER,e);return new iy(this._meterSharedState.registerMetricStorage(r),r)},t.prototype.createUpDownCounter=function(t,e){var r=rC(t,a.UP_DOWN_COUNTER,e);return new id(this._meterSharedState.registerMetricStorage(r),r)},t.prototype.createObservableGauge=function(t,e){var r=rC(t,a.OBSERVABLE_GAUGE,e),n=this._meterSharedState.registerAsyncMetricStorage(r);return new ib(r,n,this._meterSharedState.observableRegistry)},t.prototype.createObservableCounter=function(t,e){var r=rC(t,a.OBSERVABLE_COUNTER,e),n=this._meterSharedState.registerAsyncMetricStorage(r);return new im(r,n,this._meterSharedState.observableRegistry)},t.prototype.createObservableUpDownCounter=function(t,e){var r=rC(t,a.OBSERVABLE_UP_DOWN_COUNTER,e),n=this._meterSharedState.registerAsyncMetricStorage(r);return new iE(r,n,this._meterSharedState.observableRegistry)},t.prototype.addBatchObservableCallback=function(t,e){this._meterSharedState.observableRegistry.addBatchCallback(t,e)},t.prototype.removeBatchObservableCallback=function(t,e){this._meterSharedState.observableRegistry.removeBatchCallback(t,e)},t}(),iw=function(){function t(t){this._instrumentDescriptor=t}return t.prototype.getInstrumentDescriptor=function(){return this._instrumentDescriptor},t.prototype.updateDescription=function(t){this._instrumentDescriptor=rC(this._instrumentDescriptor.name,this._instrumentDescriptor.type,{description:t,valueType:this._instrumentDescriptor.valueType,unit:this._instrumentDescriptor.unit,advice:this._instrumentDescriptor.advice})},t}(),iO=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),iA=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(t){i=[6,t],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},iL=function(t){function e(){return t.call(this,rA)||this}return iO(e,t),e}(function(){function t(t){this._hash=t,this._valueMap=new Map,this._keyMap=new Map}return t.prototype.get=function(t,e){return null!=e||(e=this._hash(t)),this._valueMap.get(e)},t.prototype.getOrDefault=function(t,e){var r=this._hash(t);if(this._valueMap.has(r))return this._valueMap.get(r);var n=e();return!this._keyMap.has(r)&&this._keyMap.set(r,t),this._valueMap.set(r,n),n},t.prototype.set=function(t,e,r){null!=r||(r=this._hash(t)),!this._keyMap.has(r)&&this._keyMap.set(r,t),this._valueMap.set(r,e)},t.prototype.has=function(t,e){return null!=e||(e=this._hash(t)),this._valueMap.has(e)},t.prototype.keys=function(){var t,e;return iA(this,function(r){switch(r.label){case 0:e=(t=this._keyMap.entries()).next(),r.label=1;case 1:if(!(!0!==e.done))return[3,3];return[4,[e.value[1],e.value[0]]];case 2:return r.sent(),e=t.next(),[3,1];case 3:return[2]}})},t.prototype.entries=function(){var t,e;return iA(this,function(r){switch(r.label){case 0:e=(t=this._valueMap.entries()).next(),r.label=1;case 1:if(!(!0!==e.done))return[3,3];return[4,[this._keyMap.get(e.value[0]),e.value[1],e.value[0]]];case 2:return r.sent(),e=t.next(),[3,1];case 3:return[2]}})},Object.defineProperty(t.prototype,"size",{get:function(){return this._valueMap.size},enumerable:!1,configurable:!0}),t}()),iR=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},ix=function(){function t(t,e){this._aggregator=t,this._activeCollectionStorage=new iL,this._cumulativeMemoStorage=new iL,this._overflowAttributes={"otel.metric.overflow":!0},this._cardinalityLimit=(null!=e?e:2e3)-1,this._overflowHashCode=rA(this._overflowAttributes)}return t.prototype.record=function(t,e,r,n){var o=this,i=this._activeCollectionStorage.get(e);if(!i){if(this._activeCollectionStorage.size>=this._cardinalityLimit){var a=this._activeCollectionStorage.getOrDefault(this._overflowAttributes,function(){return o._aggregator.createAccumulation(n)});null==a||a.record(t);return}i=this._aggregator.createAccumulation(n),this._activeCollectionStorage.set(e,i)}null==i||i.record(t)},t.prototype.batchCumulate=function(t,e){var r=this;Array.from(t.entries()).forEach(function(t){var n=iR(t,3),o=n[0],i=n[1],a=n[2],u=r._aggregator.createAccumulation(e);null==u||u.record(i);var s=u;if(r._cumulativeMemoStorage.has(o,a)){var c=r._cumulativeMemoStorage.get(o,a);s=r._aggregator.diff(c,u)}else if(r._cumulativeMemoStorage.size>=r._cardinalityLimit&&(o=r._overflowAttributes,a=r._overflowHashCode,r._cumulativeMemoStorage.has(o,a))){var c=r._cumulativeMemoStorage.get(o,a);s=r._aggregator.diff(c,u)}if(r._activeCollectionStorage.has(o,a)){var l=r._activeCollectionStorage.get(o,a);s=r._aggregator.merge(l,s)}r._cumulativeMemoStorage.set(o,u,a),r._activeCollectionStorage.set(o,s,a)})},t.prototype.collect=function(){var t=this._activeCollectionStorage;return this._activeCollectionStorage=new iL,t},t}(),iP=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},iC=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},iN=function(){function t(t,e){var r=this;this._aggregator=t,this._unreportedAccumulations=new Map,this._reportHistory=new Map,e.forEach(function(t){r._unreportedAccumulations.set(t,[])})}return t.prototype.buildMetrics=function(e,r,n,i){this._stashAccumulations(n);var a,u=this._getMergedUnreportedAccumulations(e),s=u;if(this._reportHistory.has(e)){var c=this._reportHistory.get(e),l=c.collectionTime;s=(a=c.aggregationTemporality)===o.CUMULATIVE?t.merge(c.accumulations,u,this._aggregator):t.calibrateStartTime(c.accumulations,u,l)}else a=e.selectAggregationTemporality(r.type);this._reportHistory.set(e,{accumulations:s,collectionTime:i,aggregationTemporality:a});var f=function(t){return Array.from(t.entries())}(s);if(0!==f.length)return this._aggregator.toMetricData(r,a,f,i)},t.prototype._stashAccumulations=function(t){var e,r,n=this._unreportedAccumulations.keys();try{for(var o=iP(n),i=o.next();!i.done;i=o.next()){var a=i.value,u=this._unreportedAccumulations.get(a);void 0===u&&(u=[],this._unreportedAccumulations.set(a,u)),u.push(t)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}},t.prototype._getMergedUnreportedAccumulations=function(e){var r,n,o=new iL,i=this._unreportedAccumulations.get(e);if(this._unreportedAccumulations.set(e,[]),void 0===i)return o;try{for(var a=iP(i),u=a.next();!u.done;u=a.next()){var s=u.value;o=t.merge(o,s,this._aggregator)}}catch(t){r={error:t}}finally{try{u&&!u.done&&(n=a.return)&&n.call(a)}finally{if(r)throw r.error}}return o},t.merge=function(t,e,r){for(var n=e.entries(),o=n.next();!0!==o.done;){var i=iC(o.value,3),a=i[0],u=i[1],s=i[2];if(t.has(a,s)){var c=t.get(a,s),l=r.merge(c,u);t.set(a,l,s)}else t.set(a,u,s);o=n.next()}return t},t.calibrateStartTime=function(t,e,r){var n,o;try{for(var i=iP(t.keys()),a=i.next();!a.done;a=i.next()){var u=iC(a.value,2),s=u[0],c=u[1],l=e.get(s,c);null==l||l.setStartTime(r)}}catch(t){n={error:t}}finally{try{a&&!a.done&&(o=i.return)&&o.call(i)}finally{if(n)throw n.error}}return e},t}(),iM=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),iI=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},iU=function(t){function e(e,r,n,o,i){var a=t.call(this,e)||this;return a._attributesProcessor=n,a._aggregationCardinalityLimit=i,a._deltaMetricStorage=new ix(r,a._aggregationCardinalityLimit),a._temporalMetricStorage=new iN(r,o),a}return iM(e,t),e.prototype.record=function(t,e){var r=this,n=new iL;Array.from(t.entries()).forEach(function(t){var e=iI(t,2),o=e[0],i=e[1];n.set(r._attributesProcessor.process(o),i)}),this._deltaMetricStorage.batchCumulate(n,e)},e.prototype.collect=function(t,e){var r=this._deltaMetricStorage.collect();return this._temporalMetricStorage.buildMetrics(t,this._instrumentDescriptor,r,e)},e}(iw);function ik(t,e){var r="";return t.unit!==e.unit&&(r+="	- Unit '"+t.unit+"' does not match '"+e.unit+"'\n"),t.type!==e.type&&(r+="	- Type '"+t.type+"' does not match '"+e.type+"'\n"),t.valueType!==e.valueType&&(r+="	- Value Type '"+t.valueType+"' does not match '"+e.valueType+"'\n"),t.description!==e.description&&(r+="	- Description '"+t.description+"' does not match '"+e.description+"'\n"),r}function iD(t,e){var r,n,o,i,a,u,s,c,l,f;if(t.valueType!==e.valueType){;return r=t,n=e,"	- use valueType '"+r.valueType+"' on instrument creation or use an instrument name other than '"+n.name+"'"}if(t.unit!==e.unit){;return o=t,i=e,"	- use unit '"+o.unit+"' on instrument creation or use an instrument name other than '"+i.name+"'"}if(t.type!==e.type){;return a=t,s=JSON.stringify({name:(u=e).name,type:u.type,unit:u.unit}),"	- create a new view with a name other than '"+a.name+"' and InstrumentSelector '"+s+"'"}if(t.description!==e.description){;return c=t,f=JSON.stringify({name:(l=e).name,type:l.type,unit:l.unit}),"	- create a new view with a name other than '"+c.name+"' and InstrumentSelector '"+f+"'\n    	- OR - create a new view with the name "+c.name+" and description '"+c.description+"' and InstrumentSelector "+f+"\n    	- OR - create a new view with the name "+l.name+" and description '"+c.description+"' and InstrumentSelector "+f}return""}var iB=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},ij=function(){function t(){this._sharedRegistry=new Map,this._perCollectorRegistry=new Map}return t.create=function(){return new t},t.prototype.getStorages=function(t){var e,r,n,o,i=[];try{for(var a=iB(this._sharedRegistry.values()),u=a.next();!u.done;u=a.next()){var s=u.value;i=i.concat(s)}}catch(t){e={error:t}}finally{try{u&&!u.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}var c=this._perCollectorRegistry.get(t);if(null!=c)try{for(var l=iB(c.values()),f=l.next();!f.done;f=l.next()){var s=f.value;i=i.concat(s)}}catch(t){n={error:t}}finally{try{f&&!f.done&&(o=l.return)&&o.call(l)}finally{if(n)throw n.error}}return i},t.prototype.register=function(t){this._registerStorage(t,this._sharedRegistry)},t.prototype.registerForCollector=function(t,e){var r=this._perCollectorRegistry.get(t);null==r&&(r=new Map,this._perCollectorRegistry.set(t,r)),this._registerStorage(e,r)},t.prototype.findOrUpdateCompatibleStorage=function(t){var e=this._sharedRegistry.get(t.name);return void 0===e?null:this._findOrUpdateCompatibleStorage(t,e)},t.prototype.findOrUpdateCompatibleCollectorStorage=function(t,e){var r=this._perCollectorRegistry.get(t);if(void 0===r)return null;var n=r.get(e.name);return void 0===n?null:this._findOrUpdateCompatibleStorage(e,n)},t.prototype._registerStorage=function(t,e){var r=t.getInstrumentDescriptor(),n=e.get(r.name);if(void 0===n){e.set(r.name,[t]);return}n.push(t)},t.prototype._findOrUpdateCompatibleStorage=function(t,e){var r,n,o=null;try{for(var i=iB(e),a=i.next();!a.done;a=i.next()){var u=a.value,s=u.getInstrumentDescriptor();(function(t,e){var r,n;return r=t.name,n=e.name,r.toLowerCase()===n.toLowerCase()&&t.unit===e.unit&&t.type===e.type&&t.valueType===e.valueType})(s,t)?(s.description!==t.description&&(t.description.length>s.description.length&&u.updateDescription(t.description),t$.warn("A view or instrument with the name ",t.name," has already been registered, but has a different description and is incompatible with another registered view.\n","Details:\n",ik(s,t),"The longer description will be used.\nTo resolve the conflict:",iD(s,t))),o=u):t$.warn("A view or instrument with the name ",t.name," has already been registered and is incompatible with another registered view.\n","Details:\n",ik(s,t),"To resolve the conflict:\n",iD(s,t))}}catch(t){r={error:t}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}return o},t}(),iF=function(){function t(t){this._backingStorages=t}return t.prototype.record=function(t,e,r,n){this._backingStorages.forEach(function(o){o.record(t,e,r,n)})},t}(),iG=function(){function t(t,e){this._instrumentName=t,this._valueType=e,this._buffer=new iL}return t.prototype.observe=function(t,e){if(void 0===e&&(e={}),"number"!=typeof t){t$.warn("non-number value provided to metric "+this._instrumentName+": "+t);return}if(this._valueType!==i.INT||!!Number.isInteger(t)||(t$.warn("INT value type cannot accept a floating-point value for "+this._instrumentName+", ignoring the fractional digits."),!!Number.isInteger(t=Math.trunc(t))))this._buffer.set(e,t)},t}(),iV=function(){function t(){this._buffer=new Map}return t.prototype.observe=function(t,e,r){if(void 0===r&&(r={}),!iT(t))return;var n=this._buffer.get(t);if(null==n&&(n=new iL,this._buffer.set(t,n)),"number"!=typeof e){t$.warn("non-number value provided to metric "+t._descriptor.name+": "+e);return}if(t._descriptor.valueType!==i.INT||!!Number.isInteger(e)||(t$.warn("INT value type cannot accept a floating-point value for "+t._descriptor.name+", ignoring the fractional digits."),!!Number.isInteger(e=Math.trunc(e))))n.set(r,e)},t}(),iH=function(t,e,r,n){return new(r||(r=Promise))(function(o,i){function a(t){try{s(n.next(t))}catch(t){i(t)}}function u(t){try{s(n.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,u)}s((n=n.apply(t,e||[])).next())})},iz=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(t){i=[6,t],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},iX=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},iW=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)(n||!(o in e))&&(!n&&(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},iq=function(){function t(){this._callbacks=[],this._batchCallbacks=[]}return t.prototype.addCallback=function(t,e){if(!(this._findCallback(t,e)>=0))this._callbacks.push({callback:t,instrument:e})},t.prototype.removeCallback=function(t,e){var r=this._findCallback(t,e);if(!(r<0))this._callbacks.splice(r,1)},t.prototype.addBatchCallback=function(t,e){var r=new Set(e.filter(iT));if(0===r.size){t$.error("BatchObservableCallback is not associated with valid instruments",e);return}if(!(this._findBatchCallback(t,r)>=0))this._batchCallbacks.push({callback:t,instruments:r})},t.prototype.removeBatchCallback=function(t,e){var r=new Set(e.filter(iT)),n=this._findBatchCallback(t,r);if(!(n<0))this._batchCallbacks.splice(n,1)},t.prototype.observe=function(t,e){return iH(this,void 0,void 0,function(){var r,n;return iz(this,function(o){switch(o.label){case 0:return r=this._observeCallbacks(t,e),n=this._observeBatchCallbacks(t,e),[4,function(t){return rb(this,void 0,void 0,function(){var e=this;return rE(this,function(r){return[2,Promise.all(t.map(function(t){return rb(e,void 0,void 0,function(){return rE(this,function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),[4,t];case 1:return[2,{status:"fulfilled",value:e.sent()}];case 2:return[2,{status:"rejected",reason:e.sent()}];case 3:return[2]}})})}))]})})}(iW(iW([],iX(r),!1),iX(n),!1))];case 1:return[2,o.sent().filter(rx).map(function(t){return t.reason})]}})})},t.prototype._observeCallbacks=function(t,e){var r=this;return this._callbacks.map(function(n){var o=n.callback,i=n.instrument;return iH(r,void 0,void 0,function(){var r,n;return iz(this,function(a){switch(a.label){case 0:return n=Promise.resolve(o(r=new iG(i._descriptor.name,i._descriptor.valueType))),null!=e&&(n=rR(n,e)),[4,n];case 1:return a.sent(),i._metricStorages.forEach(function(e){e.record(r._buffer,t)}),[2]}})})})},t.prototype._observeBatchCallbacks=function(t,e){var r=this;return this._batchCallbacks.map(function(n){var o=n.callback,i=n.instruments;return iH(r,void 0,void 0,function(){var r,n;return iz(this,function(a){switch(a.label){case 0:return n=Promise.resolve(o(r=new iV)),null!=e&&(n=rR(n,e)),[4,n];case 1:return a.sent(),i.forEach(function(e){var n=r._buffer.get(e);if(null!=n)e._metricStorages.forEach(function(e){e.record(n,t)})}),[2]}})})})},t.prototype._findCallback=function(t,e){return this._callbacks.findIndex(function(r){return r.callback===t&&r.instrument===e})},t.prototype._findBatchCallback=function(t,e){return this._batchCallbacks.findIndex(function(r){return r.callback===t&&function(t,e){var r,n;if(t.size!==e.size)return!1;try{for(var o=rw(t),i=o.next();!i.done;i=o.next()){var a=i.value;if(!e.has(a))return!1}}catch(t){r={error:t}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return!0}(r.instruments,e)})},t}(),iY=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),iQ=function(t){function e(e,r,n,o,i){var a=t.call(this,e)||this;return a._attributesProcessor=n,a._aggregationCardinalityLimit=i,a._deltaMetricStorage=new ix(r,a._aggregationCardinalityLimit),a._temporalMetricStorage=new iN(r,o),a}return iY(e,t),e.prototype.record=function(t,e,r,n){e=this._attributesProcessor.process(e,r),this._deltaMetricStorage.record(t,e,r,n)},e.prototype.collect=function(t,e){var r=this._deltaMetricStorage.collect();return this._temporalMetricStorage.buildMetrics(t,this._instrumentDescriptor,r,e)},e}(iw),iK=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),iJ=function(){function t(){}return t.Noop=function(){return i$},t}(),iZ=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return iK(e,t),e.prototype.process=function(t,e){return t},e}(iJ);(function(t){function e(e){var r=t.call(this)||this;return r._allowedAttributeNames=e,r}iK(e,t),e.prototype.process=function(t,e){var r=this,n={};return Object.keys(t).filter(function(t){return r._allowedAttributeNames.includes(t)}).forEach(function(e){return n[e]=t[e]}),n}})(iJ);var i$=new iZ,i0=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(t){i=[6,t],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},i1=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},i2=function(){function t(t,e){this._meterProviderSharedState=t,this._instrumentationScope=e,this.metricStorageRegistry=new ij,this.observableRegistry=new iq,this.meter=new iS(this)}return t.prototype.registerMetricStorage=function(t){var e=this._registerMetricStorage(t,iQ);return 1===e.length?e[0]:new iF(e)},t.prototype.registerAsyncMetricStorage=function(t){return this._registerMetricStorage(t,iU)},t.prototype.collect=function(t,e,r){var n,o,i,a;return n=this,o=void 0,i=void 0,a=function(){var n,o,i;return i0(this,function(a){switch(a.label){case 0:return[4,this.observableRegistry.observe(e,null==r?void 0:r.timeoutMillis)];case 1:if(n=a.sent(),0===(o=this.metricStorageRegistry.getStorages(t)).length)return[2,null];if(0===(i=o.map(function(r){return r.collect(t,e)}).filter(rO)).length)return[2,{errors:n}];return[2,{scopeMetrics:{scope:this._instrumentationScope,metrics:i},errors:n}]}})},new(i||(i=Promise))(function(t,e){function r(t){try{s(a.next(t))}catch(t){e(t)}}function u(t){try{s(a.throw(t))}catch(t){e(t)}}function s(e){var n;e.done?t(e.value):((n=e.value)instanceof i?n:new i(function(t){t(n)})).then(r,u)}s((a=a.apply(n,o||[])).next())})},t.prototype._registerMetricStorage=function(t,e){var r=this,n=this._meterProviderSharedState.viewRegistry.findViews(t,this._instrumentationScope).map(function(n){var o,i,a,u,s=(o=n,i=t,{name:null!==(a=o.name)&&void 0!==a?a:i.name,description:null!==(u=o.description)&&void 0!==u?u:i.description,type:i.type,unit:i.unit,valueType:i.valueType,advice:i.advice}),c=r.metricStorageRegistry.findOrUpdateCompatibleStorage(s);if(null!=c)return c;var l=n.aggregation.createAggregator(s),f=new e(s,l,n.attributesProcessor,r._meterProviderSharedState.metricCollectors,n.aggregationCardinalityLimit);return r.metricStorageRegistry.register(f),f});if(0===n.length){var o=this._meterProviderSharedState.selectAggregations(t.type).map(function(n){var o=i1(n,2),i=o[0],a=o[1],u=r.metricStorageRegistry.findOrUpdateCompatibleCollectorStorage(i,t);if(null!=u)return u;var s=a.createAggregator(t),c=i.selectCardinalityLimit(t.type),l=new e(t,s,iJ.Noop(),[i],c);return r.metricStorageRegistry.registerForCollector(i,l),l});n=n.concat(o)}return n},t}(),i3=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},i4=function(){function t(t){this.resource=t,this.viewRegistry=new il,this.metricCollectors=[],this.meterSharedStates=new Map}return t.prototype.getMeterSharedState=function(t){var e,r,n,o=(e=t).name+":"+(null!==(r=e.version)&&void 0!==r?r:"")+":"+(null!==(n=e.schemaUrl)&&void 0!==n?n:""),i=this.meterSharedStates.get(o);return null==i&&(i=new i2(this,t),this.meterSharedStates.set(o,i)),i},t.prototype.selectAggregations=function(t){var e,r,n=[];try{for(var o=i3(this.metricCollectors),i=o.next();!i.done;i=o.next()){var a=i.value;n.push([a,a.selectAggregation(t)])}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return n},t}(),i5=function(t,e,r,n){return new(r||(r=Promise))(function(o,i){function a(t){try{s(n.next(t))}catch(t){i(t)}}function u(t){try{s(n.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,u)}s((n=n.apply(t,e||[])).next())})},i6=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(t){i=[6,t],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},i8=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},i9=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)(n||!(o in e))&&(!n&&(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},i7=function(){function t(t,e){this._sharedState=t,this._metricReader=e}return t.prototype.collect=function(t){return i5(this,void 0,void 0,function(){var e,r,n,o=this;return i6(this,function(i){switch(i.label){case 0:return e=eR(Date.now()),r=[],n=[],[4,Promise.all(Array.from(this._sharedState.meterSharedStates.values()).map(function(i){return i5(o,void 0,void 0,function(){var o;return i6(this,function(a){switch(a.label){case 0:return[4,i.collect(this,e,t)];case 1:return(null==(o=a.sent())?void 0:o.scopeMetrics)!=null&&r.push(o.scopeMetrics),(null==o?void 0:o.errors)!=null&&n.push.apply(n,i9([],i8(o.errors),!1)),[2]}})})}))];case 1:return i.sent(),[2,{resourceMetrics:{resource:this._sharedState.resource,scopeMetrics:r},errors:n}]}})})},t.prototype.forceFlush=function(t){return i5(this,void 0,void 0,function(){return i6(this,function(e){switch(e.label){case 0:return[4,this._metricReader.forceFlush(t)];case 1:return e.sent(),[2]}})})},t.prototype.shutdown=function(t){return i5(this,void 0,void 0,function(){return i6(this,function(e){switch(e.label){case 0:return[4,this._metricReader.shutdown(t)];case 1:return e.sent(),[2]}})})},t.prototype.selectAggregationTemporality=function(t){return this._metricReader.selectAggregationTemporality(t)},t.prototype.selectAggregation=function(t){return this._metricReader.selectAggregation(t)},t.prototype.selectCardinalityLimit=function(t){var e,r,n;return null!==(n=null===(r=(e=this._metricReader).selectCardinalityLimit)||void 0===r?void 0:r.call(e,t))&&void 0!==n?n:2e3},t}(),at=function(t,e,r,n){return new(r||(r=Promise))(function(o,i){function a(t){try{s(n.next(t))}catch(t){i(t)}}function u(t){try{s(n.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,u)}s((n=n.apply(t,e||[])).next())})},ae=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(t){i=[6,t],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},ar=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},an=function(){function t(t){if(this._shutdown=!1,this._sharedState=new i4((e=null===(s=null==t?void 0:t.mergeResourceWithDefaults)||void 0===s||s,n=null!=(r=null==t?void 0:t.resource)?r:oA.empty(),e?oA.default().merge(n):n)),(null==t?void 0:t.views)!=null&&t.views.length>0)try{for(var e,r,n,o,i,a,u,s,c=ar(t.views),l=c.next();!l.done;l=c.next()){var f=l.value;this._sharedState.viewRegistry.addView(f)}}catch(t){o={error:t}}finally{try{l&&!l.done&&(i=c.return)&&i.call(c)}finally{if(o)throw o.error}}if((null==t?void 0:t.readers)!=null&&t.readers.length>0)try{for(var p=ar(t.readers),h=p.next();!h.done;h=p.next()){var d=h.value;this.addMetricReader(d)}}catch(t){a={error:t}}finally{try{h&&!h.done&&(u=p.return)&&u.call(p)}finally{if(a)throw a.error}}}return t.prototype.getMeter=function(t,e,r){return(void 0===e&&(e=""),void 0===r&&(r={}),this._shutdown)?(t$.warn("A shutdown MeterProvider cannot provide a Meter"),tI):this._sharedState.getMeterSharedState({name:t,version:e,schemaUrl:r.schemaUrl}).meter},t.prototype.addMetricReader=function(t){var e=new i7(this._sharedState,t);t.setMetricProducer(e),this._sharedState.metricCollectors.push(e)},t.prototype.shutdown=function(t){return at(this,void 0,void 0,function(){return ae(this,function(e){switch(e.label){case 0:if(this._shutdown)return t$.warn("shutdown may only be called once per MeterProvider"),[2];return this._shutdown=!0,[4,Promise.all(this._sharedState.metricCollectors.map(function(e){return e.shutdown(t)}))];case 1:return e.sent(),[2]}})})},t.prototype.forceFlush=function(t){return at(this,void 0,void 0,function(){return ae(this,function(e){switch(e.label){case 0:if(this._shutdown)return t$.warn("invalid attempt to force flush after MeterProvider shutdown"),[2];return[4,Promise.all(this._sharedState.metricCollectors.map(function(e){return e.forceFlush(t)}))];case 1:return e.sent(),[2]}})})},t}(),ao=function(t){return r9.Default()},ai=function(t){return o.CUMULATIVE},aa=function(t,e,r,n){return new(r||(r=Promise))(function(o,i){function a(t){try{s(n.next(t))}catch(t){i(t)}}function u(t){try{s(n.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,u)}s((n=n.apply(t,e||[])).next())})},au=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(t){i=[6,t],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},as=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},ac=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)(n||!(o in e))&&(!n&&(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},al=function(){function t(t){var e,r,n;this._shutdown=!1,this._aggregationSelector=null!==(e=null==t?void 0:t.aggregationSelector)&&void 0!==e?e:ao,this._aggregationTemporalitySelector=null!==(r=null==t?void 0:t.aggregationTemporalitySelector)&&void 0!==r?r:ai,this._metricProducers=null!==(n=null==t?void 0:t.metricProducers)&&void 0!==n?n:[],this._cardinalitySelector=null==t?void 0:t.cardinalitySelector}return t.prototype.setMetricProducer=function(t){if(this._sdkMetricProducer)throw Error("MetricReader can not be bound to a MeterProvider again.");this._sdkMetricProducer=t,this.onInitialized()},t.prototype.selectAggregation=function(t){return this._aggregationSelector(t)},t.prototype.selectAggregationTemporality=function(t){return this._aggregationTemporalitySelector(t)},t.prototype.selectCardinalityLimit=function(t){return this._cardinalitySelector?this._cardinalitySelector(t):2e3},t.prototype.onInitialized=function(){},t.prototype.collect=function(t){return aa(this,void 0,void 0,function(){var e,r,n,o,i;return au(this,function(a){switch(a.label){case 0:if(void 0===this._sdkMetricProducer)throw Error("MetricReader is not bound to a MetricProducer");if(this._shutdown)throw Error("MetricReader is shutdown");return[4,Promise.all(ac([this._sdkMetricProducer.collect({timeoutMillis:null==t?void 0:t.timeoutMillis})],as(this._metricProducers.map(function(e){return e.collect({timeoutMillis:null==t?void 0:t.timeoutMillis})})),!1))];case 1:return r=(e=as.apply(void 0,[a.sent()]))[0],n=e.slice(1),o=r.errors.concat(rP(n,function(t){return t.errors})),i=r.resourceMetrics.resource,[2,{resourceMetrics:{resource:i,scopeMetrics:r.resourceMetrics.scopeMetrics.concat(rP(n,function(t){return t.resourceMetrics.scopeMetrics}))},errors:o}]}})})},t.prototype.shutdown=function(t){return aa(this,void 0,void 0,function(){return au(this,function(e){switch(e.label){case 0:if(this._shutdown)return t$.error("Cannot call shutdown twice."),[2];if((null==t?void 0:t.timeoutMillis)!=null)return[3,2];return[4,this.onShutdown()];case 1:return e.sent(),[3,4];case 2:return[4,rR(this.onShutdown(),t.timeoutMillis)];case 3:e.sent(),e.label=4;case 4:return this._shutdown=!0,[2]}})})},t.prototype.forceFlush=function(t){return aa(this,void 0,void 0,function(){return au(this,function(e){switch(e.label){case 0:if(this._shutdown)return t$.warn("Cannot forceFlush on already shutdown MetricReader."),[2];if((null==t?void 0:t.timeoutMillis)!=null)return[3,2];return[4,this.onForceFlush()];case 1:case 3:return e.sent(),[2];case 2:return[4,rR(this.onForceFlush(),t.timeoutMillis)]}})})},t}(),af=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),ap=function(t,e,r,n){return new(r||(r=Promise))(function(o,i){function a(t){try{s(n.next(t))}catch(t){i(t)}}function u(t){try{s(n.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,u)}s((n=n.apply(t,e||[])).next())})},ah=function(t,e){var r,n,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function u(i){return function(u){return function(i){if(r)throw TypeError("Generator is already executing.");for(;a;)try{if(r=1,n&&(o=2&i[0]?n.return:i[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,i[1])).done)return o;switch(n=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,n=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!(o=(o=a.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=e.call(t,a)}catch(t){i=[6,t],n=0}finally{r=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,u])}}},ad=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},ay=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)(n||!(o in e))&&(!n&&(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},a_=function(t){function e(e){var r,n,o,i,a=t.call(this,{aggregationSelector:null===(r=e.exporter.selectAggregation)||void 0===r?void 0:r.bind(e.exporter),aggregationTemporalitySelector:null===(n=e.exporter.selectAggregationTemporality)||void 0===n?void 0:n.bind(e.exporter),metricProducers:e.metricProducers})||this;if(void 0!==e.exportIntervalMillis&&e.exportIntervalMillis<=0)throw Error("exportIntervalMillis must be greater than 0");if(void 0!==e.exportTimeoutMillis&&e.exportTimeoutMillis<=0)throw Error("exportTimeoutMillis must be greater than 0");if(void 0!==e.exportTimeoutMillis&&void 0!==e.exportIntervalMillis&&e.exportIntervalMillis<e.exportTimeoutMillis)throw Error("exportIntervalMillis must be greater than or equal to exportTimeoutMillis");return a._exportInterval=null!==(o=e.exportIntervalMillis)&&void 0!==o?o:6e4,a._exportTimeout=null!==(i=e.exportTimeoutMillis)&&void 0!==i?i:3e4,a._exporter=e.exporter,a}return af(e,t),e.prototype._runOnce=function(){return ap(this,void 0,void 0,function(){var t;return ah(this,function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),[4,rR(this._doRun(),this._exportTimeout)];case 1:return e.sent(),[3,3];case 2:if((t=e.sent())instanceof rL)return t$.error("Export took longer than %s milliseconds and timed out.",this._exportTimeout),[2];return ir(t),[3,3];case 3:return[2]}})})},e.prototype._doRun=function(){var t,e;return ap(this,void 0,void 0,function(){var n,o,i,a,u,s;return ah(this,function(c){switch(c.label){case 0:return[4,this.collect({timeoutMillis:this._exportTimeout})];case 1:if(o=(n=c.sent()).resourceMetrics,(i=n.errors).length>0&&(s=t$).error.apply(s,ay(["PeriodicExportingMetricReader: metrics collection errors"],ad(i),!1)),!o.resource.asyncAttributesPending)return[3,5];c.label=2;case 2:return c.trys.push([2,4,,5]),[4,null===(e=(t=o.resource).waitForAsyncAttributes)||void 0===e?void 0:e.call(t)];case 3:return c.sent(),[3,5];case 4:return a=c.sent(),t$.debug("Error while resolving async portion of resource: ",a),ir(a),[3,5];case 5:return[4,ii._export(this._exporter,o)];case 6:if((u=c.sent()).code!==r.SUCCESS)throw Error("PeriodicExportingMetricReader: metrics export failed (error "+u.error+")");return[2]}})})},e.prototype.onInitialized=function(){var t=this;this._interval=setInterval(function(){t._runOnce()},this._exportInterval),this._interval},e.prototype.onForceFlush=function(){return ap(this,void 0,void 0,function(){return ah(this,function(t){switch(t.label){case 0:return[4,this._runOnce()];case 1:return t.sent(),[4,this._exporter.forceFlush()];case 2:return t.sent(),[2]}})})},e.prototype.onShutdown=function(){return ap(this,void 0,void 0,function(){return ah(this,function(t){switch(t.label){case 0:return this._interval&&clearInterval(this._interval),[4,this.onForceFlush()];case 1:return t.sent(),[4,this._exporter.shutdown()];case 2:return t.sent(),[2]}})})},e}(al);!function(t){t[t.NOT_RECORD=0]="NOT_RECORD",t[t.RECORD=1]="RECORD",t[t.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(g||(g={}));var av=function(){function t(){}return t.prototype.shouldSample=function(){return{decision:g.NOT_RECORD}},t.prototype.toString=function(){return"AlwaysOffSampler"},t}(),ag=function(){function t(){}return t.prototype.shouldSample=function(){return{decision:g.RECORD_AND_SAMPLED}},t.prototype.toString=function(){return"AlwaysOnSampler"},t}(),am=function(){function t(t){var e,r,n,o;this._root=t.root,!this._root&&(ir(Error("ParentBasedSampler must have a root sampler configured")),this._root=new ag),this._remoteParentSampled=null!==(e=t.remoteParentSampled)&&void 0!==e?e:new ag,this._remoteParentNotSampled=null!==(r=t.remoteParentNotSampled)&&void 0!==r?r:new av,this._localParentSampled=null!==(n=t.localParentSampled)&&void 0!==n?n:new ag,this._localParentNotSampled=null!==(o=t.localParentNotSampled)&&void 0!==o?o:new av}return t.prototype.shouldSample=function(t,r,n,o,i,a){var u=tT.getSpanContext(t);if(!u||!th(u))return this._root.shouldSample(t,r,n,o,i,a);if(u.isRemote)return u.traceFlags&e.SAMPLED?this._remoteParentSampled.shouldSample(t,r,n,o,i,a):this._remoteParentNotSampled.shouldSample(t,r,n,o,i,a);return u.traceFlags&e.SAMPLED?this._localParentSampled.shouldSample(t,r,n,o,i,a):this._localParentNotSampled.shouldSample(t,r,n,o,i,a)},t.prototype.toString=function(){return"ParentBased{root="+this._root.toString()+", remoteParentSampled="+this._remoteParentSampled.toString()+", remoteParentNotSampled="+this._remoteParentNotSampled.toString()+", localParentSampled="+this._localParentSampled.toString()+", localParentNotSampled="+this._localParentNotSampled.toString()+"}"},t}(),ab=function(){function t(t){void 0===t&&(t=0),this._ratio=t,this._ratio=this._normalize(t),this._upperBound=Math.floor(4294967295*this._ratio)}return t.prototype.shouldSample=function(t,e){return{decision:tp(e)&&this._accumulate(e)<this._upperBound?g.RECORD_AND_SAMPLED:g.NOT_RECORD}},t.prototype.toString=function(){return"TraceIdRatioBased{"+this._ratio+"}"},t.prototype._normalize=function(t){return"number"!=typeof t||isNaN(t)?0:t>=1?1:t<=0?0:t},t.prototype._accumulate=function(t){for(var e=0,r=0;r<t.length/8;r++){var n=8*r;e=(e^parseInt(t.slice(n,n+8),16))>>>0}return e},t}(),aE=function(){function t(t,e){this._exporter=t,this._isExporting=!1,this._finishedSpans=[],this._droppedSpansCount=0;var r=rv();this._maxExportBatchSize="number"==typeof(null==e?void 0:e.maxExportBatchSize)?e.maxExportBatchSize:r.OTEL_BSP_MAX_EXPORT_BATCH_SIZE,this._maxQueueSize="number"==typeof(null==e?void 0:e.maxQueueSize)?e.maxQueueSize:r.OTEL_BSP_MAX_QUEUE_SIZE,this._scheduledDelayMillis="number"==typeof(null==e?void 0:e.scheduledDelayMillis)?e.scheduledDelayMillis:r.OTEL_BSP_SCHEDULE_DELAY,this._exportTimeoutMillis="number"==typeof(null==e?void 0:e.exportTimeoutMillis)?e.exportTimeoutMillis:r.OTEL_BSP_EXPORT_TIMEOUT,this._shutdownOnce=new oX(this._shutdown,this),this._maxExportBatchSize>this._maxQueueSize&&(t$.warn("BatchSpanProcessor: maxExportBatchSize must be smaller or equal to maxQueueSize, setting maxExportBatchSize to match maxQueueSize"),this._maxExportBatchSize=this._maxQueueSize)}return t.prototype.forceFlush=function(){return this._shutdownOnce.isCalled?this._shutdownOnce.promise:this._flushAll()},t.prototype.onStart=function(t,e){},t.prototype.onEnd=function(t){if(!this._shutdownOnce.isCalled&&(t.spanContext().traceFlags&e.SAMPLED)!=0)this._addToBuffer(t)},t.prototype.shutdown=function(){return this._shutdownOnce.call()},t.prototype._shutdown=function(){var t=this;return Promise.resolve().then(function(){return t.onShutdown()}).then(function(){return t._flushAll()}).then(function(){return t._exporter.shutdown()})},t.prototype._addToBuffer=function(t){if(this._finishedSpans.length>=this._maxQueueSize){0===this._droppedSpansCount&&t$.debug("maxQueueSize reached, dropping spans"),this._droppedSpansCount++;return}this._droppedSpansCount>0&&(t$.warn("Dropped "+this._droppedSpansCount+" spans because maxQueueSize reached"),this._droppedSpansCount=0),this._finishedSpans.push(t),this._maybeStartTimer()},t.prototype._flushAll=function(){var t=this;return new Promise(function(e,r){for(var n=[],o=Math.ceil(t._finishedSpans.length/t._maxExportBatchSize),i=0;i<o;i++)n.push(t._flushOneBatch());Promise.all(n).then(function(){e()}).catch(r)})},t.prototype._flushOneBatch=function(){var t=this;return(this._clearTimer(),0===this._finishedSpans.length)?Promise.resolve():new Promise(function(e,n){var o=setTimeout(function(){n(Error("Timeout"))},t._exportTimeoutMillis);nI.with(nk(nI.active()),function(){t._finishedSpans.length<=t._maxExportBatchSize?(i=t._finishedSpans,t._finishedSpans=[]):i=t._finishedSpans.splice(0,t._maxExportBatchSize);for(var i,a=function(){return t._exporter.export(i,function(t){var i;clearTimeout(o),t.code===r.SUCCESS?e():n(null!==(i=t.error)&&void 0!==i?i:Error("BatchSpanProcessor: span export failed"))})},u=null,s=0,c=i.length;s<c;s++){var l=i[s];l.resource.asyncAttributesPending&&l.resource.waitForAsyncAttributes&&(null!=u||(u=[]),u.push(l.resource.waitForAsyncAttributes()))}null===u?a():Promise.all(u).then(a,function(t){ir(t),n(t)})})})},t.prototype._maybeStartTimer=function(){var t=this;if(!this._isExporting){var e=function(){t._isExporting=!0,t._flushOneBatch().finally(function(){t._isExporting=!1,t._finishedSpans.length>0&&(t._clearTimer(),t._maybeStartTimer())}).catch(function(e){t._isExporting=!1,ir(e)})};if(this._finishedSpans.length>=this._maxExportBatchSize)return e();void 0===this._timer&&(this._timer=setTimeout(function(){return e()},this._scheduledDelayMillis),this._timer)}},t.prototype._clearTimer=function(){void 0!==this._timer&&(clearTimeout(this._timer),this._timer=void 0)},t}(),aT=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),aS=function(t){function e(e,r){var n=t.call(this,e,r)||this;return n.onInit(r),n}return aT(e,t),e.prototype.onInit=function(t){var e=this;(null==t?void 0:t.disableAutoFlushOnDocumentHide)!==!0&&"undefined"!=typeof document&&(this._visibilityChangeListener=function(){"hidden"===document.visibilityState&&e.forceFlush().catch(function(t){ir(t)})},this._pageHideListener=function(){e.forceFlush().catch(function(t){ir(t)})},document.addEventListener("visibilitychange",this._visibilityChangeListener),document.addEventListener("pagehide",this._pageHideListener))},e.prototype.onShutdown=function(){"undefined"!=typeof document&&(this._visibilityChangeListener&&document.removeEventListener("visibilitychange",this._visibilityChangeListener),this._pageHideListener&&document.removeEventListener("pagehide",this._pageHideListener))},e}(aE),aw=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},aO=function(){function t(t){var e;void 0===t&&(t={}),this._propagators=null!==(e=t.propagators)&&void 0!==e?e:[],this._fields=Array.from(new Set(this._propagators.map(function(t){return"function"==typeof t.fields?t.fields():[]}).reduce(function(t,e){return t.concat(e)},[])))}return t.prototype.inject=function(t,e,r){var n,o;try{for(var i=aw(this._propagators),a=i.next();!a.done;a=i.next()){var u=a.value;try{u.inject(t,e,r)}catch(t){t$.warn("Failed to inject with "+u.constructor.name+". Err: "+t.message)}}}catch(t){n={error:t}}finally{try{a&&!a.done&&(o=i.return)&&o.call(i)}finally{if(n)throw n.error}}},t.prototype.extract=function(t,e,r){return this._propagators.reduce(function(t,n){try{return n.extract(t,e,r)}catch(t){t$.warn("Failed to extract with "+n.constructor.name+". Err: "+t.message)}return t},t)},t.prototype.fields=function(){return this._fields.slice()},t}(),aA="baggage",aL=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a};function aR(t){var e,r=t.split(";");if(r.length<=0)return;var n=r.shift();if(!!n){var o=n.indexOf("=");if(!(o<=0)){var i=decodeURIComponent(n.substring(0,o).trim()),a=decodeURIComponent(n.substring(o+1).trim());return r.length>0&&(e=ea(r.join(";"))),{key:i,value:a,metadata:e}}}}var ax=function(){function t(){}return t.prototype.inject=function(t,e,r){var n=ec.getBaggage(t);if(!(!n||nD(t))){var o=n.getAllEntries().map(function(t){var e=aL(t,2),r=e[0],n=e[1],o=encodeURIComponent(r)+"="+encodeURIComponent(n.value);return void 0!==n.metadata&&(o+=";"+n.metadata.toString()),o}).filter(function(t){return t.length<=4096}).slice(0,180).reduce(function(t,e){var r=""+t+(""!==t?",":"")+e;return r.length>8192?t:r},"");o.length>0&&r.set(e,aA,o)}},t.prototype.extract=function(t,e,r){var n=r.get(e,aA),o=Array.isArray(n)?n.join(","):n;if(!o)return t;var i={};return 0===o.length?t:(o.split(",").forEach(function(t){var e=aR(t);if(e){var r={value:e.value};e.metadata&&(r.metadata=e.metadata),i[e.key]=r}}),0===Object.entries(i).length)?t:ec.setBaggage(t,ec.createBaggage(i))},t.prototype.fields=function(){return[aA]},t}();!function(t){t[t.NOT_RECORD=0]="NOT_RECORD",t[t.RECORD=1]="RECORD",t[t.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(m||(m={})),!function(t){t[t.UNSET=0]="UNSET",t[t.OK=1]="OK",t[t.ERROR=2]="ERROR"}(b||(b={}));var aP=function(){return(aP=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r],e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},aC=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},aN=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},aM=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)(n||!(o in e))&&(!n&&(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},aI=function(){function t(t,e,r,n,o,i,a,u,s,c){void 0===a&&(a=[]),this.attributes={},this.links=[],this.events=[],this._droppedAttributesCount=0,this._droppedEventsCount=0,this._droppedLinksCount=0,this.status={code:b.UNSET},this.endTime=[0,0],this._ended=!1,this._duration=[-1,-1],this.name=r,this._spanContext=n,this.parentSpanId=i,this.kind=o,this.links=a;var l=Date.now();this._performanceStartTime=eL.now(),this._performanceOffset=l-(this._performanceStartTime+ex()),this._startTimeProvided=null!=u,this.startTime=this._getTime(null!=u?u:l),this.resource=t.resource,this.instrumentationLibrary=t.instrumentationLibrary,this._spanLimits=t.getSpanLimits(),this._attributeValueLengthLimit=this._spanLimits.attributeValueLengthLimit||0,null!=c&&this.setAttributes(c),this._spanProcessor=t.getActiveSpanProcessor(),this._spanProcessor.onStart(this,e)}return t.prototype.spanContext=function(){return this._spanContext},t.prototype.setAttribute=function(t,e){return null==e||this._isSpanEnded()?this:0===t.length?(t$.warn("Invalid attribute key: "+t),this):oQ(e)?Object.keys(this.attributes).length>=this._spanLimits.attributeCountLimit&&!Object.prototype.hasOwnProperty.call(this.attributes,t)?(this._droppedAttributesCount++,this):(this.attributes[t]=this._truncateToSize(e),this):(t$.warn("Invalid attribute value set for key: "+t),this)},t.prototype.setAttributes=function(t){var e,r;try{for(var n=aC(Object.entries(t)),o=n.next();!o.done;o=n.next()){var i=aN(o.value,2),a=i[0],u=i[1];this.setAttribute(a,u)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}return this},t.prototype.addEvent=function(t,e,r){if(this._isSpanEnded())return this;if(0===this._spanLimits.eventCountLimit)return t$.warn("No events allowed."),this._droppedEventsCount++,this;this.events.length>=this._spanLimits.eventCountLimit&&(0===this._droppedEventsCount&&t$.debug("Dropping extra events."),this.events.shift(),this._droppedEventsCount++),eU(e)&&(!eU(r)&&(r=e),e=void 0);var n=oY(e);return this.events.push({name:t,attributes:n,time:this._getTime(r),droppedAttributesCount:0}),this},t.prototype.addLink=function(t){return this.links.push(t),this},t.prototype.addLinks=function(t){var e;return(e=this.links).push.apply(e,aM([],aN(t),!1)),this},t.prototype.setStatus=function(t){return this._isSpanEnded()?this:(this.status=aP({},t),null!=this.status.message&&"string"!=typeof t.message&&(t$.warn("Dropping invalid status.message of type '"+typeof t.message+"', expected 'string'"),delete this.status.message),this)},t.prototype.updateName=function(t){return this._isSpanEnded()?this:(this.name=t,this)},t.prototype.end=function(t){var e,r,n,o;if(this._isSpanEnded()){t$.error(this.name+" "+this._spanContext.traceId+"-"+this._spanContext.spanId+" - You can only call end() on a span once.");return}this._ended=!0,this.endTime=this._getTime(t),this._duration=(e=this.startTime,n=(r=this.endTime)[0]-e[0],(o=r[1]-e[1])<0&&(n-=1,o+=1e9),[n,o]),this._duration[0]<0&&(t$.warn("Inconsistent start and end time, startTime > endTime. Setting span duration to 0ms.",this.startTime,this.endTime),this.endTime=this.startTime.slice(),this._duration=[0,0]),this._droppedEventsCount>0&&t$.warn("Dropped "+this._droppedEventsCount+" events because eventCountLimit reached"),this._spanProcessor.onEnd(this)},t.prototype._getTime=function(t){if("number"==typeof t&&t<=eL.now())return eP(t+this._performanceOffset);if("number"==typeof t)return eR(t);if(t instanceof Date)return eR(t.getTime());if(eI(t))return t;if(this._startTimeProvided)return eR(Date.now());var e=eL.now()-this._performanceStartTime;return ek(this.startTime,eR(e))},t.prototype.isRecording=function(){return!1===this._ended},t.prototype.recordException=function(t,e){var r={};"string"==typeof t?r[nK]=t:t&&(t.code?r[nQ]=t.code.toString():t.name&&(r[nQ]=t.name),t.message&&(r[nK]=t.message),t.stack&&(r["exception.stacktrace"]=t.stack)),r[nQ]||r[nK]?this.addEvent("exception",r,e):t$.warn("Failed to record an exception "+t)},Object.defineProperty(t.prototype,"duration",{get:function(){return this._duration},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"ended",{get:function(){return this._ended},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"droppedAttributesCount",{get:function(){return this._droppedAttributesCount},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"droppedEventsCount",{get:function(){return this._droppedEventsCount},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"droppedLinksCount",{get:function(){return this._droppedLinksCount},enumerable:!1,configurable:!0}),t.prototype._isSpanEnded=function(){return this._ended&&t$.warn("Can not execute the operation on ended Span {traceId: "+this._spanContext.traceId+", spanId: "+this._spanContext.spanId+"}"),this._ended},t.prototype._truncateToLimitUtil=function(t,e){return t.length<=e?t:t.substring(0,e)},t.prototype._truncateToSize=function(t){var e=this,r=this._attributeValueLengthLimit;return r<=0?(t$.warn("Attribute value limit must be positive, got "+r),t):"string"==typeof t?this._truncateToLimitUtil(t,r):Array.isArray(t)?t.map(function(t){return"string"==typeof t?e._truncateToLimitUtil(t,r):t}):t},t}(),aU=n.AlwaysOn;function ak(){var t=rv();return{sampler:aD(t),forceFlushTimeoutMillis:3e4,generalLimits:{attributeValueLengthLimit:t.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:t.OTEL_ATTRIBUTE_COUNT_LIMIT},spanLimits:{attributeValueLengthLimit:t.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT,attributeCountLimit:t.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT,linkCountLimit:t.OTEL_SPAN_LINK_COUNT_LIMIT,eventCountLimit:t.OTEL_SPAN_EVENT_COUNT_LIMIT,attributePerEventCountLimit:t.OTEL_SPAN_ATTRIBUTE_PER_EVENT_COUNT_LIMIT,attributePerLinkCountLimit:t.OTEL_SPAN_ATTRIBUTE_PER_LINK_COUNT_LIMIT},mergeResourceWithDefaults:!0}}function aD(t){switch(void 0===t&&(t=rv()),t.OTEL_TRACES_SAMPLER){case n.AlwaysOn:return new ag;case n.AlwaysOff:return new av;case n.ParentBasedAlwaysOn:return new am({root:new ag});case n.ParentBasedAlwaysOff:return new am({root:new av});case n.TraceIdRatio:return new ab(aB(t));case n.ParentBasedTraceIdRatio:return new am({root:new ab(aB(t))});default:return t$.error('OTEL_TRACES_SAMPLER value "'+t.OTEL_TRACES_SAMPLER+" invalid, defaulting to "+aU+'".'),new ag}}function aB(t){if(void 0===t.OTEL_TRACES_SAMPLER_ARG||""===t.OTEL_TRACES_SAMPLER_ARG)return t$.error("OTEL_TRACES_SAMPLER_ARG is blank, defaulting to 1."),1;var e=Number(t.OTEL_TRACES_SAMPLER_ARG);return isNaN(e)?(t$.error("OTEL_TRACES_SAMPLER_ARG="+t.OTEL_TRACES_SAMPLER_ARG+" was given, but it is invalid, defaulting to 1."),1):e<0||e>1?(t$.error("OTEL_TRACES_SAMPLER_ARG="+t.OTEL_TRACES_SAMPLER_ARG+" was given, but it is out of range ([0..1]), defaulting to 1."),1):e}var aj=function(){this.generateTraceId=aG(16),this.generateSpanId=aG(8)},aF=Array(32);function aG(t){return function(){for(var e=0;e<2*t;e++)aF[e]=Math.floor(16*Math.random())+48,aF[e]>=58&&(aF[e]+=39);return String.fromCharCode.apply(null,aF.slice(0,2*t))}}var aV=function(){function t(t,e,r){this._tracerProvider=r;var n,o,i,a,u=(n=e,o={sampler:aD()},(a=Object.assign({},i=ak(),o,n)).generalLimits=Object.assign({},i.generalLimits,n.generalLimits||{}),a.spanLimits=Object.assign({},i.spanLimits,n.spanLimits||{}),a);this._sampler=u.sampler,this._generalLimits=u.generalLimits,this._spanLimits=u.spanLimits,this._idGenerator=e.idGenerator||new aj,this.resource=r.resource,this.instrumentationLibrary=t}return t.prototype.startSpan=function(t,r,n){void 0===r&&(r={}),void 0===n&&(n=nI.active()),r.root&&(n=tT.deleteSpan(n));var o,i,a,u,s,c,l=tT.getSpan(n);if(nD(n)){t$.debug("Instrumentation suppressed, returning Noop Span");var f=tT.wrapSpanContext(te);return f}var p=null==l?void 0:l.spanContext(),h=this._idGenerator.generateSpanId();p&&tT.isSpanContextValid(p)?(u=p.traceId,s=p.traceState,c=p.spanId):u=this._idGenerator.generateTraceId();var y=null!==(o=r.kind)&&void 0!==o?o:d.INTERNAL,_=(null!==(i=r.links)&&void 0!==i?i:[]).map(function(t){return{context:t.context,attributes:oY(t.attributes)}}),v=oY(r.attributes),g=this._sampler.shouldSample(n,u,t,y,v,_);s=null!==(a=g.traceState)&&void 0!==a?a:s;var b={traceId:u,spanId:h,traceFlags:g.decision===m.RECORD_AND_SAMPLED?e.SAMPLED:e.NONE,traceState:s};if(g.decision===m.NOT_RECORD){t$.debug("Recording is off, propagating context in a non-recording span");var f=tT.wrapSpanContext(b);return f}var E=oY(Object.assign(v,g.attributes));return new aI(this,n,t,b,y,c,_,r.startTime,void 0,E)},t.prototype.startActiveSpan=function(t,e,r,n){if(!(arguments.length<2)){2==arguments.length?a=e:3==arguments.length?(o=e,a=r):(o=e,i=r,a=n);var o,i,a,u=null!=i?i:nI.active(),s=this.startSpan(t,o,u),c=tT.setSpan(u,s);return nI.with(c,a,void 0,s)}},t.prototype.getGeneralLimits=function(){return this._generalLimits},t.prototype.getSpanLimits=function(){return this._spanLimits},t.prototype.getActiveSpanProcessor=function(){return this._tracerProvider.getActiveSpanProcessor()},t}(),aH=function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},az=function(){function t(t){this._spanProcessors=t}return t.prototype.forceFlush=function(){var t,e,r=[];try{for(var n=aH(this._spanProcessors),o=n.next();!o.done;o=n.next()){var i=o.value;r.push(i.forceFlush())}}catch(e){t={error:e}}finally{try{o&&!o.done&&(e=n.return)&&e.call(n)}finally{if(t)throw t.error}}return new Promise(function(t){Promise.all(r).then(function(){t()}).catch(function(e){ir(e||Error("MultiSpanProcessor: forceFlush failed")),t()})})},t.prototype.onStart=function(t,e){var r,n;try{for(var o=aH(this._spanProcessors),i=o.next();!i.done;i=o.next())i.value.onStart(t,e)}catch(t){r={error:t}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}},t.prototype.onEnd=function(t){var e,r;try{for(var n=aH(this._spanProcessors),o=n.next();!o.done;o=n.next())o.value.onEnd(t)}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}},t.prototype.shutdown=function(){var t,e,r=[];try{for(var n=aH(this._spanProcessors),o=n.next();!o.done;o=n.next()){var i=o.value;r.push(i.shutdown())}}catch(e){t={error:e}}finally{try{o&&!o.done&&(e=n.return)&&e.call(n)}finally{if(t)throw t.error}}return new Promise(function(t,e){Promise.all(r).then(function(){t()},e)})},t}(),aX=function(){function t(){}return t.prototype.onStart=function(t,e){},t.prototype.onEnd=function(t){},t.prototype.shutdown=function(){return Promise.resolve()},t.prototype.forceFlush=function(){return Promise.resolve()},t}(),aW=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},aq=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)(n||!(o in e))&&(!n&&(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))};!function(t){t[t.resolved=0]="resolved",t[t.timeout=1]="timeout",t[t.error=2]="error",t[t.unresolved=3]="unresolved"}(E||(E={}));var aY=function(){function t(t){void 0===t&&(t={}),this._registeredSpanProcessors=[],this._tracers=new Map;var e,r,n,o,i,a,u,s,c,l,f,p,h,d,y,_,v,g=oU({},ak(),(d=Object.assign({},(e=t).spanLimits),y=ry(r_),d.attributeCountLimit=null!==(u=null!==(a=null!==(i=null!==(n=null===(r=e.spanLimits)||void 0===r?void 0:r.attributeCountLimit)&&void 0!==n?n:null===(o=e.generalLimits)||void 0===o?void 0:o.attributeCountLimit)&&void 0!==i?i:y.OTEL_SPAN_ATTRIBUTE_COUNT_LIMIT)&&void 0!==a?a:y.OTEL_ATTRIBUTE_COUNT_LIMIT)&&void 0!==u?u:128,d.attributeValueLengthLimit=null!==(h=null!==(p=null!==(f=null!==(c=null===(s=e.spanLimits)||void 0===s?void 0:s.attributeValueLengthLimit)&&void 0!==c?c:null===(l=e.generalLimits)||void 0===l?void 0:l.attributeValueLengthLimit)&&void 0!==f?f:y.OTEL_SPAN_ATTRIBUTE_VALUE_LENGTH_LIMIT)&&void 0!==p?p:y.OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT)&&void 0!==h?h:rp,Object.assign({},e,{spanLimits:d})));if(this.resource=null!==(_=g.resource)&&void 0!==_?_:oA.empty(),g.mergeResourceWithDefaults&&(this.resource=oA.default().merge(this.resource)),this._config=Object.assign({},g,{resource:this.resource}),null===(v=t.spanProcessors)||void 0===v?void 0:v.length)this._registeredSpanProcessors=aq([],aW(t.spanProcessors),!1),this.activeSpanProcessor=new az(this._registeredSpanProcessors);else{var m=this._buildExporterFromEnv();if(void 0!==m){var b=new aS(m);this.activeSpanProcessor=b}else this.activeSpanProcessor=new aX}}return t.prototype.getTracer=function(t,e,r){var n=t+"@"+(e||"")+":"+((null==r?void 0:r.schemaUrl)||"");return!this._tracers.has(n)&&this._tracers.set(n,new aV({name:t,version:e,schemaUrl:null==r?void 0:r.schemaUrl},this._config,this)),this._tracers.get(n)},t.prototype.addSpanProcessor=function(t){0===this._registeredSpanProcessors.length&&this.activeSpanProcessor.shutdown().catch(function(t){return t$.error("Error while trying to shutdown current span processor",t)}),this._registeredSpanProcessors.push(t),this.activeSpanProcessor=new az(this._registeredSpanProcessors)},t.prototype.getActiveSpanProcessor=function(){return this.activeSpanProcessor},t.prototype.register=function(t){void 0===t&&(t={}),tT.setGlobalTracerProvider(this),void 0===t.propagator&&(t.propagator=this._buildPropagatorFromEnv()),t.contextManager&&nI.setGlobalContextManager(t.contextManager),t.propagator&&ec.setGlobalPropagator(t.propagator)},t.prototype.forceFlush=function(){var t=this._config.forceFlushTimeoutMillis,e=this._registeredSpanProcessors.map(function(e){return new Promise(function(r){var n,o=setTimeout(function(){r(Error("Span processor did not completed within timeout period of "+t+" ms")),n=E.timeout},t);e.forceFlush().then(function(){clearTimeout(o),n!==E.timeout&&r(n=E.resolved)}).catch(function(t){clearTimeout(o),n=E.error,r(t)})})});return new Promise(function(t,r){Promise.all(e).then(function(e){var n=e.filter(function(t){return t!==E.resolved});n.length>0?r(n):t()}).catch(function(t){return r([t])})})},t.prototype.shutdown=function(){return this.activeSpanProcessor.shutdown()},t.prototype._getPropagator=function(t){var e;return null===(e=this.constructor._registeredPropagators.get(t))||void 0===e?void 0:e()},t.prototype._getSpanExporter=function(t){var e;return null===(e=this.constructor._registeredExporters.get(t))||void 0===e?void 0:e()},t.prototype._buildPropagatorFromEnv=function(){var t=this,e=Array.from(new Set(rv().OTEL_PROPAGATORS)),r=e.map(function(e){var r=t._getPropagator(e);return!r&&t$.warn('Propagator "'+e+'" requested through environment variable is unavailable.'),r}).reduce(function(t,e){return e&&t.push(e),t},[]);return 0===r.length?void 0:1===e.length?r[0]:new aO({propagators:r})},t.prototype._buildExporterFromEnv=function(){var t=rv().OTEL_TRACES_EXPORTER;if("none"!==t&&""!==t){var e=this._getSpanExporter(t);return!e&&t$.error('Exporter "'+t+'" requested through environment variable is unavailable.'),e}},t._registeredPropagators=new Map([["tracecontext",function(){return new nW}],["baggage",function(){return new ax}]]),t._registeredExporters=new Map,t}(),aQ=function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},aK=function(t,e,r){if(r||2==arguments.length)for(var n,o=0,i=e.length;o<i;o++)(n||!(o in e))&&(!n&&(n=Array.prototype.slice.call(e,0,o)),n[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))},aJ=function(){function t(){this._enabled=!1,this._currentContext=D}return t.prototype._bindFunction=function(t,e){void 0===t&&(t=D);var r=this,n=function(){for(var n=this,o=[],i=0;i<arguments.length;i++)o[i]=arguments[i];return r.with(t,function(){return e.apply(n,o)})};return Object.defineProperty(n,"length",{enumerable:!1,configurable:!0,writable:!1,value:e.length}),n},t.prototype.active=function(){return this._currentContext},t.prototype.bind=function(t,e){return(void 0===t&&(t=this.active()),"function"==typeof e)?this._bindFunction(t,e):e},t.prototype.disable=function(){return this._currentContext=D,this._enabled=!1,this},t.prototype.enable=function(){return this._enabled?this:(this._enabled=!0,this._currentContext=D,this)},t.prototype.with=function(t,e,r){for(var n=[],o=3;o<arguments.length;o++)n[o-3]=arguments[o];var i=this._currentContext;this._currentContext=t||D;try{return e.call.apply(e,aK([r],aQ(n),!1))}finally{this._currentContext=i}},t}(),aZ=function(){var t=function(e,r){return(t=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),a$=function(t){function e(e){void 0===e&&(e={});var r=t.call(this,e)||this;if(e.contextManager)throw"contextManager should be defined in register method not in constructor";if(e.propagator)throw"propagator should be defined in register method not in constructor";return r}return aZ(e,t),e.prototype.register=function(e){void 0===e&&(e={}),void 0===e.contextManager&&(e.contextManager=new aJ),e.contextManager&&e.contextManager.enable(),t.prototype.register.call(this,e)},e}(aY);function a0(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function a1(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}function a2(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function a3(t,e,r){return e&&a2(t.prototype,e),r&&a2(t,r),t}function a4(t,e){return(a4=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function a5(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&a4(t,e)}function a6(t){return t&&"undefined"!=typeof Symbol&&t.constructor===Symbol?"symbol":typeof t}function a8(t){return(a8=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function a9(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(a9=function(){return!!t})()}function a7(t){var e=a9();return function(){var r,n,o,i=a8(t);return o=e?Reflect.construct(i,arguments,a8(this).constructor):i.apply(this,arguments),r=this,(n=o)&&("object"===a6(n)||"function"==typeof n)?n:a0(r)}}!function(t){t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.TRACE=1]="TRACE",t[t.TRACE2=2]="TRACE2",t[t.TRACE3=3]="TRACE3",t[t.TRACE4=4]="TRACE4",t[t.DEBUG=5]="DEBUG",t[t.DEBUG2=6]="DEBUG2",t[t.DEBUG3=7]="DEBUG3",t[t.DEBUG4=8]="DEBUG4",t[t.INFO=9]="INFO",t[t.INFO2=10]="INFO2",t[t.INFO3=11]="INFO3",t[t.INFO4=12]="INFO4",t[t.WARN=13]="WARN",t[t.WARN2=14]="WARN2",t[t.WARN3=15]="WARN3",t[t.WARN4=16]="WARN4",t[t.ERROR=17]="ERROR",t[t.ERROR2=18]="ERROR2",t[t.ERROR3=19]="ERROR3",t[t.ERROR4=20]="ERROR4",t[t.FATAL=21]="FATAL",t[t.FATAL2=22]="FATAL2",t[t.FATAL3=23]="FATAL3",t[t.FATAL4=24]="FATAL4"}(T||(T={}));var ut=["ad:init","cl:load-modules","cl:init-creative","ac:init","ac:preload:fonts","ac:preload:feeds","ac:init:load:features","ac:init:load:elements","ac:init:load:widgets","ac:init:render","ac:init:rerender"];function ue(t,e){return t.find(function(t){return t.name===e})}function ur(){var t=Array.from(document.getElementsByTagName("meta")).find(function(t){return t.getAttribute("name")===eE});return t&&t.content||""}var un=function(t){a5(r,t);var e=a7(r);function r(){var t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{logLevels:["log","warn","error"]};return a1(this,r),L(a0(t=e.call(this,"@bannerflow/instrumentation-console","0.1.0",n)),"_originalConsole",function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable}))),n.forEach(function(e){L(t,e,r[e])})}return t}({},console)),L(a0(t),"_errorMeter",void 0),t._errorMeter=t.meter.createCounter("PreviewErrors/all",{description:"Count of preview errors"}),t}return a3(r,[{key:"enable",value:function(){var t=this,e=ew(ur());this._config.logLevels.forEach(function(r){t._wrap(console,r,t._patchConsoleMethod(r,e))})}},{key:"disable",value:function(){var t=this;this._config.logLevels.forEach(function(e){t._unwrap(console,e)})}},{key:"init",value:function(){this.enable()}},{key:"_patchConsoleMethod",value:function(t,e){var r=this;return function(n){return function(){for(var o,i,a=arguments.length,u=Array(a),s=0;s<a;s++)u[s]=arguments[s];try{var c,l=u[0];r.logger.emit({body:(void 0===l?"undefined":a6(l))==="object"?null!==(c=l.message)&&void 0!==c?c:JSON.stringify(l):String(l),severityText:t,severityNumber:function(t){switch(t){case"error":return T.ERROR;case"warn":return T.WARN;case"info":return T.INFO;case"debug":return T.DEBUG;default:return 9}}(t),attributes:{"trace.id":null==e?void 0:e.traceId,"span.id":null==e?void 0:e.spanId}})}catch(t){r._originalConsole.error("Logging interceptor error",t)}return"error"===t&&r._errorMeter.add(1,{errorType:(null===(i=arguments[0])||void 0===i?void 0:null===(o=i.constructor)||void 0===o?void 0:o.name)||"unknown",message:String(arguments[0]).substring(0,100)}),n.apply(this,arguments)}}}}]),r}(ot),uo=function(t){a5(r,t);var e=a7(r);function r(t){var n;return a1(this,r),L(a0(n=e.call(this,"@bannerflow/instrumentation-creative","0.1.0",t)),"_adTag",void 0),L(a0(n),"_adCounter",void 0),n._adCounter=n.meter.createCounter("Ad/renders",{description:"Ad render count"}),n}return a3(r,[{key:"init",value:function(){}},{key:"enable",value:function(){this._init()}},{key:"disable",value:function(){var t,e;null===(t=this._adTag)||void 0===t||t.removeEventListener("render",this._onAdRendered),null===(e=this._adTag)||void 0===e||e.removeEventListener("error",this._onAdError)}},{key:"_onAdRendered",value:function(){this._diag.verbose("ad rendered"),this._adCounter.add(1),this._collectPerformance()}},{key:"_onAdError",value:function(t){var e=this;this._diag.verbose("ad error",t);var r=ur();nI.with(ec.extract(D,{traceparent:r}),function(){var r,n,o=e._startSpan("AdError");if(!!o){var i=null!==(n=null===(r=t.data)||void 0===r?void 0:r.event)&&void 0!==n?n:"Failed to load ad.js",a=Error(i);a.name="AdError",o.setStatus({code:b.ERROR,message:i}),o.recordException(a),o.end()}})}},{key:"_collectPerformance",value:function(){var t=this,e=performance.getEntriesByType("mark"),r=ur();nI.with(ec.extract(D,{traceparent:r}),function(){var r=t._startTimedSpan("AdRender","ad:tag",e);if(!!r){var n=window._bannerflow.ads[0].selectedCreative;r.setAttribute("bf.brand.id",n.brand.id),r.setAttribute("bf.creative_set.id",n.creativeset.id),r.setAttribute("bf.creative_set.name",n.creativeset.name),r.setAttribute("bf.creative.id",n.id),r.setAttribute("bf.design.id",n.design.id),r.setAttribute("bf.size.id",n.size.id),r.setAttribute("bf.version.id",n.version.id),t._addCreativePerformanceSpans(r,e),t._endSpan(r,"ad:render",e)}})}},{key:"_addCreativePerformanceSpans",value:function(t,e){var r=this;ut.forEach(function(n){var o=r._startTimedSpan(n,"".concat(n,":start"),e,t);o&&r._endSpan(o,"".concat(n,":end"),e)})}},{key:"_startTimedSpan",value:function(t,e,r,n){var o=ue(r,e);if(o)return this.tracer.startSpan(t,{kind:d.CLIENT,startTime:o.startTime},n?tT.setSpan(nI.active(),n):void 0)}},{key:"_startSpan",value:function(t,e){return this.tracer.startSpan(t,{kind:d.CLIENT},e?tT.setSpan(nI.active(),e):void 0)}},{key:"_endSpan",value:function(t,e,r){if(t){var n=ue(r,e);n?t.end(n.startTime):t.end()}}},{key:"_initAdEventListeners",value:function(){var t,e,r=!!document.querySelector('script[data-rendered="true"]');this._onAdRendered=this._onAdRendered.bind(this),this._onAdError=this._onAdError.bind(this),r?this._onAdRendered():null===(e=this._adTag)||void 0===e||e.addEventListener("render",this._onAdRendered),null===(t=this._adTag)||void 0===t||t.addEventListener("error",this._onAdError)}},{key:"_init",value:function(){if(this._diag.info("_init"),this._adTag=document.querySelector(this._config.adSelector),!this._adTag){this._diag.error("Ad tag not found");return}this._adTag.removeEventListener("render",this._onAdRendered),this._adTag.removeEventListener("error",this._onAdError),this._initAdEventListeners()}}]),r}(ot),ui=OT_API_KEY,ua=new oA((L(S={},"service.name","Creative Preview"),L(S,"service.version","1.0.0"),S)),uu=new rs({url:"https://otlp.eu01.nr-data.net/v1/logs",headers:{"api-key":ui},concurrencyLimit:1}),us=new it({resource:ua});us.addLogRecordProcessor(new ic(uu)),tZ.setGlobalLoggerProvider(us);var uc=new nO({url:"https://otlp.eu01.nr-data.net/v1/traces",headers:{"api-key":ui}}),ul=new a$({resource:ua,sampler:new am({root:new ab(.1)})});ul.addSpanProcessor(new aS(uc,{maxQueueSize:100,maxExportBatchSize:50,scheduledDelayMillis:500,exportTimeoutMillis:15e3})),ul.register({propagator:new t1({propagators:[new ey,new eO]})}),tT.setGlobalTracerProvider(ul);var uf=new an({resource:ua,readers:[new a_({exporter:new nb({url:"https://otlp.eu01.nr-data.net/v1/metrics",headers:{"api-key":ui}})})]});tz.setGlobalMeterProvider(uf),document.onvisibilitychange=function(){"hidden"===document.visibilityState&&uf.shutdown()},!function(t){var e,r,n=t.tracerProvider||tT.getTracerProvider(),o=t.meterProvider||tz.getMeterProvider(),i=t.loggerProvider||nM.getLoggerProvider(),a=null!==(r=null===(e=t.instrumentations)||void 0===e?void 0:e.flat())&&void 0!==r?r:[];!function(t,e,r,n){for(var o=0,i=t.length;o<i;o++){var a=t[o];e&&a.setTracerProvider(e),r&&a.setMeterProvider(r),n&&a.setLoggerProvider&&a.setLoggerProvider(n),!a.getConfig().enabled&&a.enable()}}(a,n,o,i)}({loggerProvider:us,tracerProvider:ul,meterProvider:uf,instrumentations:[new un,new oi({applyCustomAttributesOnSpan:{documentLoad:function(t){new URLSearchParams(window.location.search).forEach(function(e,r){"snapshot"===r?t.setAttribute("bf.creative_set.snapshot.id",e):t.setAttribute("query.".concat(r),e)})}}}),new og({clearTimingResources:!0,propagateTraceHeaderCorsUrls:[/.+/g]}),new uo({adSelector:".studio-ad"})]})})();