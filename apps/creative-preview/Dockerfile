# This file is generated by Nx.
#
# Build the docker image with `pnpm exec nx docker-build creative-preview`.
# Tip: Modify "docker-build" options in project.json to change docker build args.
#
# Run the container with `docker run -p 3000:3000 -t cps`.
FROM docker.io/node:lts-alpine

WORKDIR /app

RUN addgroup --system cps && \
          adduser --system -G cps cps

COPY dist/apps/creative-preview cps
# copy ad scripts which CPS depends on at runtime
COPY dist/libs/ad libs/ad
COPY dist/libs/creative libs/creative

# Set npm authentication for private registry
COPY .npmrc cps

RUN chown -R cps:cps .

# You can remove this install step if you build with `--bundle` option.
# The bundled output will include external dependencies.
RUN npm --prefix cps --omit=dev -f install --ignore-scripts


# Switch to the cps user
USER cps

CMD ["node", "--max-old-space-size=1536", "--enable-source-maps", "cps"]