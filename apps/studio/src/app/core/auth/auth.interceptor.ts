import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    HttpInterceptor,
    HttpRequest
} from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AuthHttpInterceptor } from '@auth0/auth0-angular';
import { CreativesetDataService } from '@studio/common/creativeSet/creativeset.data.service';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { StudioHubService } from '@studio/common/services/studio-hub.service';
import { Observable, switchMap, take } from 'rxjs';
import { CreativeSetShowcaseService } from '../../shared/creativeset-showcase/state/creativeset-showcase.service';
import { AuthGuard } from './auth.guard';

/**
 Intercepts HTTPClient requests and adds the authorization header,
 as well as the signalR connection header.

 This works for all libraries that use the Angular HttpClient. The apollo client for collections is not included.
 **/
@Injectable({ providedIn: 'root' })
export class AuthInterceptor implements HttpInterceptor {
    private readonly auth0HttpInterceptor = inject(AuthHttpInterceptor);
    // Needed for first request after redirected from Auth0. Contains brandId and creativeSetId
    private readonly authGuard = inject(AuthGuard);
    private readonly creativeSetDataService = inject(CreativesetDataService);
    private readonly creativesetShowcaseService = inject(CreativeSetShowcaseService);
    private readonly environmentService = inject(EnvironmentService);
    private readonly studioHubService = inject(StudioHubService);

    private brandId = '';
    private creativeSetId = '';

    constructor() {
        this.creativeSetDataService.creativeset$.pipe(takeUntilDestroyed()).subscribe(creativeSet => {
            if (!creativeSet) {
                return;
            }
            this.creativeSetId = creativeSet.id;
            this.brandId = creativeSet.brandId;
        });
    }

    intercept(request: HttpRequest<HttpHeaders>, next: HttpHandler): Observable<HttpEvent<unknown>> {
        // E2E TESTS
        if (this.environmentService.appEnvironment.stage === 'test') {
            return next.handle(request);
        }
        // SHOWCASE
        if (this.environmentService.inShowcaseMode) {
            return this.creativesetShowcaseService.showcaseToken$.pipe(
                take(1),
                switchMap(showcaseToken => {
                    const newHeaders: Record<string, string> = {
                        Authorization: `Bearer ${showcaseToken}`
                    };
                    if (this.brandId && this.creativeSetId) {
                        newHeaders['BF-Brand-Id'] = this.brandId;
                        newHeaders['BF-CreativeSet-Id'] = this.creativeSetId;
                    }
                    request = request.clone({
                        setHeaders: {
                            ...newHeaders
                        }
                    });

                    return next.handle(request);
                })
            );
        }

        // REGULAR
        const connectionId = this.studioHubService.connectionId;
        if (connectionId) {
            request = request.clone({
                setHeaders: {
                    'X-BF-ConnectionId': connectionId,
                    'BF-Studio-SignalR-ConnectionId': connectionId
                }
            });
        }

        if (!this.brandId || !this.creativeSetId) {
            const urlChunks = this.authGuard.stateUrl.split('/');

            if (urlChunks.length >= 4) {
                this.brandId = urlChunks[2];
                this.creativeSetId = urlChunks[4].split('?')[0];
            }
        }

        request = this.addBFHeaders(request, this.brandId, this.creativeSetId);
        return this.auth0HttpInterceptor.intercept(request, next);
    }

    private addBFHeaders(
        request: HttpRequest<HttpHeaders>,
        brandId: string,
        creativeSetId: string
    ): HttpRequest<HttpHeaders> {
        return request.clone({
            setHeaders: {
                'BF-Brand-Id': brandId,
                'BF-CreativeSet-Id': creativeSetId
            }
        });
    }
}
