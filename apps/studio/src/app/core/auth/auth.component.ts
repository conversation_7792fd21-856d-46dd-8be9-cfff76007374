import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UILoaderComponent } from '@bannerflow/ui';

// This component is used by Auth0 to handle the authorization PKCE code
@Component({
    imports: [UILoaderComponent],
    template: '<ui-loader></ui-loader>'
})
export class AuthComponent implements OnInit {
    constructor(
        private route: ActivatedRoute,
        private router: Router
    ) {}

    ngOnInit(): void {
        const codeParam = this.route.snapshot.queryParams.code;
        if (!codeParam) {
            this.router.navigate(['/404']);
        }
    }
}
