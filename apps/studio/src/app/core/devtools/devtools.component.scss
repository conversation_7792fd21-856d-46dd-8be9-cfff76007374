:host {
    display: block;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 200;

    .hover-area {
        width: 200px;
        height: 35px;
    }

    .ui-button {
        cursor: pointer;
    }
}

.wrapper {
    display: flex;
    gap: 10px;
    padding: 1.4rem 2rem;
    padding-top: 1.2rem;

    [icon='close'] {
        position: absolute;
        top: 10px;
        right: 10px;
        cursor: pointer;
        color: var(--ui-color-text);
    }

    &.with-content {
        flex-direction: column;
        align-items: center;
    }

    .header {
        font-weight: bold;
        color: var(--ui-color-text);
    }

    .content {
        display: flex;
        gap: 10px;
    }
}

::ng-deep ui-popover-master.ui-popover {
    &:not(.ui-tooltip) {
        &::before,
        &::after {
            display: none !important;
        }
    }

    .devtools {
        padding: 0;
        background: var(--ui-color-background-second);
        box-shadow: 0px -3px 69px 4px rgba(0, 0, 0, 0.15);
        border-radius: 7px;

        .active {
            outline: 1px solid;
        }
    }
}
