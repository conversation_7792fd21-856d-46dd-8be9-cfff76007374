import { inject, Injectable } from '@angular/core';
import { Logger } from '@bannerflow/sentinel-logger';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { IntercomService, IntercomTokenService } from '@bannerflow/intercom-messenger';

@Injectable({ providedIn: 'root' })
export class IntercomHelperService {
    private logger = new Logger('IntercomService');
    private readonly intercom = inject(IntercomService);
    private readonly intercomTokenService = inject(IntercomTokenService);

    constructor() {
        const environmentService = inject(EnvironmentService);

        const { production, intercomId } = environmentService.appEnvironment;
        if (!production) {
            return;
        }

        environmentService.isMobile$.subscribe(data => {
            if (data) {
                this.intercom.update({ hide_default_launcher: true });
            } else {
                this.intercom.update({ hide_default_launcher: false });
            }
        });

        this.logger.verbose('Booting Intercom');

        this.intercom
            .init({
                appId: intercomId,
                jwtProvider: () => this.intercomTokenService.getToken() as Promise<string>
            })
            .then(() => {
                this.intercom.boot();
            });
    }

    public showNewMessage(message?: string): void {
        this.logger.verbose('showing message');
        this.intercom.showNewMessage(message);
    }

    public showIntercomNews(): void {
        this.logger.verbose('showing news');
        this.intercom.showIntercomNews();
    }
}
