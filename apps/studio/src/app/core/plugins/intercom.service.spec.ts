import { TestBed } from '@angular/core/testing';
import { Logger } from '@bannerflow/sentinel-logger';
import { IntercomService, IntercomTokenService } from '@bannerflow/intercom-messenger';
import { provideEnvironment } from '@studio/common/environment/environment.provider';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { of } from 'rxjs';
import { environment } from '../../../environments/environment.test';
import { defineMatchMedia } from '../../shared/mocks/matchMedia.mock';
import { IntercomHelperService } from './intercom-helper.service';
import { provideHttpClient } from '@angular/common/http';

describe('IntercomHelperService', () => {
    let service: IntercomHelperService;
    let environmentServiceSpy: jest.Mocked<EnvironmentService>;
    let intercomServiceMock: jest.Mocked<IntercomService>;
    let intercomTokenServiceMock: jest.Mocked<IntercomTokenService>;

    beforeAll(() => {
        defineMatchMedia();
    });

    beforeEach(() => {
        const environmentServiceMock: Partial<EnvironmentService> = {
            inShowcaseMode: false,
            isMobile$: of(false),
            appEnvironment: {
                ...environment,
                production: true,
                intercomId: 'test-intercom-id'
            }
        };

        intercomServiceMock = {
            init: jest.fn().mockResolvedValue(undefined),
            boot: jest.fn(),
            update: jest.fn(),
            showNewMessage: jest.fn(),
            showIntercomNews: jest.fn()
        } as unknown as jest.Mocked<IntercomService>;

        intercomTokenServiceMock = {
            getToken: jest.fn().mockResolvedValue('test-token')
        } as unknown as jest.Mocked<IntercomTokenService>;

        TestBed.configureTestingModule({
            providers: [
                provideHttpClient(),
                provideEnvironment(environment),
                { provide: EnvironmentService, useValue: environmentServiceMock },
                { provide: IntercomService, useValue: intercomServiceMock },
                { provide: IntercomTokenService, useValue: intercomTokenServiceMock },
                IntercomHelperService
            ]
        });

        environmentServiceSpy = TestBed.inject(EnvironmentService) as jest.Mocked<EnvironmentService>;
    });

    it('should be created', () => {
        service = TestBed.inject(IntercomHelperService);
        expect(service).toBeTruthy();
    });

    describe('Intercom not available in production environment', () => {
        beforeEach(() => {
            jest.clearAllMocks();
            intercomServiceMock.init.mockRejectedValue(new Error('Test error'));
        });

        it('should still log "Booting Intercom" when in production environment', () => {
            const loggerSpy = jest.spyOn(Logger.prototype, 'verbose');
            service = TestBed.inject(IntercomHelperService);

            expect(loggerSpy).toHaveBeenCalledWith('Booting Intercom');
        });

        it('showNewMessage() should call Intercom service and log message', () => {
            const loggerSpy = jest.spyOn(Logger.prototype, 'verbose');
            service = TestBed.inject(IntercomHelperService);
            service.showNewMessage('Test message');

            expect(loggerSpy).toHaveBeenCalledWith('showing message');
            expect(intercomServiceMock.showNewMessage).toHaveBeenCalledWith('Test message');
        });

        it('showIntercomNews() should call Intercom service and log news', () => {
            const loggerSpy = jest.spyOn(Logger.prototype, 'verbose');
            service = TestBed.inject(IntercomHelperService);
            service.showIntercomNews();

            expect(loggerSpy).toHaveBeenCalledWith('showing news');
            expect(intercomServiceMock.showIntercomNews).toHaveBeenCalled();
        });
    });

    describe('Intercom in different environments', () => {
        describe('non-production environment', () => {
            beforeEach(() => {
                jest.clearAllMocks();
                environmentServiceSpy.appEnvironment.production = false;
            });

            it('should not boot Intercom if not in production environment', () => {
                const loggerSpy = jest.spyOn(Logger.prototype, 'verbose');
                service = TestBed.inject(IntercomHelperService);

                expect(loggerSpy).not.toHaveBeenCalledWith('Booting Intercom');
                expect(intercomServiceMock.init).not.toHaveBeenCalled();
                expect(intercomServiceMock.boot).not.toHaveBeenCalled();
            });
        });

        describe('production environment with mobile detection', () => {
            beforeEach(() => {
                jest.clearAllMocks();
                environmentServiceSpy.appEnvironment.production = true;
            });

            it("should hide Intercom's default launcher when isMobile$ emits true", () => {
                environmentServiceSpy.isMobile$ = of(true);
                service = TestBed.inject(IntercomHelperService);

                expect(intercomServiceMock.update).toHaveBeenCalledWith({
                    hide_default_launcher: true
                });
            });

            it("should show Intercom's default launcher when isMobile$ emits false", () => {
                environmentServiceSpy.isMobile$ = of(false);
                service = TestBed.inject(IntercomHelperService);

                expect(intercomServiceMock.update).toHaveBeenCalledWith({
                    hide_default_launcher: false
                });
            });
        });

        describe('Intercom service functions', () => {
            beforeEach(() => {
                jest.clearAllMocks();
                environmentServiceSpy.appEnvironment.production = true;
            });

            it('showNewMessage() should call IntercomService showNewMessage method', () => {
                const loggerSpy = jest.spyOn(Logger.prototype, 'verbose');
                service = TestBed.inject(IntercomHelperService);

                service.showNewMessage('Test message');

                expect(loggerSpy).toHaveBeenCalledWith('showing message');
                expect(intercomServiceMock.showNewMessage).toHaveBeenCalledWith('Test message');
            });

            it('showIntercomNews() should call IntercomService showIntercomNews method', () => {
                const loggerSpy = jest.spyOn(Logger.prototype, 'verbose');
                service = TestBed.inject(IntercomHelperService);

                service.showIntercomNews();

                expect(loggerSpy).toHaveBeenCalledWith('showing news');
                expect(intercomServiceMock.showIntercomNews).toHaveBeenCalled();
            });
        });
    });
});
