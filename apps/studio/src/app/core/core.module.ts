import { CommonModule } from '@angular/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NgModule, Optional, SkipSelf } from '@angular/core';
import { RouterModule } from '@angular/router';
import { UIModule } from '@bannerflow/ui';
import { GlobalErrorHandler } from './global-error-handler.service';
import { IntercomHelperService } from './plugins/intercom-helper.service';
import { GoogleTagManagerService } from './plugins/gtm.service';

@NgModule({
    imports: [CommonModule, RouterModule, UIModule],
    exports: [],
    declarations: [],
    providers: [
        {
            provide: ErrorHandler,
            useClass: GlobalErrorHandler
        }
    ]
})
export class CoreModule {
    constructor(
        @SkipSelf() @Optional() core: CoreModule,
        // Injected to avoid tree-shaking
        private _intercom: IntercomHelperService,
        private _gtm: GoogleTagManagerService
    ) {
        if (core) {
            throw new Error('CoreModule should only be instantiated once!');
        }
    }
}
