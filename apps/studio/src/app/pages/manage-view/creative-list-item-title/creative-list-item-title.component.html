<div class="size-label">
    @if (creative) {
        <div class="creative-size">
            <div
                class="creative-size-fake-input"
                [ngClass]="{ editing: isEditing() && !isCreativeGroup }">
                <div
                    *permissions="'Status'"
                    class="approval-status-wrapper"
                    [ngClass]="{ 'margin-right-s': isCreativeGroup }"
                    [uiDropdownTarget]="statusDropdown && statusDropdown.dropdown">
                    @if (isNewUI()) {
                        <ui-button
                            class="status-button"
                            type="plain-secondary"
                            size="xs"
                            [nuiSvgIcon]="statusIcon()" />

                        <ui-button
                            class="approval-status-selector"
                            [id]="'approval-status-selector-' + index"
                            type="solid-secondary"
                            [nuiSvgIcon]="statusIcon()"
                            [nuiTrailingIcon]="'keyboard_arrow_down'"
                            [uiTooltip]="creative.approvalStatus || 'No status'"
                            uiTooltipPosition="bottom"
                            size="xs" />
                    } @else {
                        <ui-svg-icon
                            class="status-icon status-icon_{{ statusClass() }}"
                            icon="status-dot"></ui-svg-icon>
                        <div
                            class="approval-status-selector"
                            [id]="'approval-status-selector-' + index">
                            <ui-svg-icon
                                class="status-icon status-icon_{{ statusClass() }}"
                                icon="status-dot"></ui-svg-icon>
                            <div>
                                {{ creative.approvalStatus || 'No status' }}
                            </div>
                            <div class="arrow">
                                <ui-svg-icon icon="arrow-down"></ui-svg-icon>
                            </div>
                        </div>
                    }

                    <status-dropdown
                        #statusDropdown
                        size="xs"></status-dropdown>
                </div>
                @if (creative.version.id | localizationForVersion | async; as localizationId) {
                    @if (isCreativeGroup) {
                        <version-flag
                            [localizationId]="localizationId"
                            [versionId]="creative.version.id"
                            [showVersionName]="true"></version-flag>
                    }
                }
                @if (!isCreativeGroup) {
                    <span
                        class="creative-size-name"
                        [ngClass]="{
                            empty:
                                (!creative.size.name || !creative.size.name.length) &&
                                !isEditing() &&
                                !isSaving(),
                            editing: isEditing(),
                            saving: isSaving()
                        }">
                        {{ creative.size.width }} × {{ creative.size.height }}
                    </span>
                }
                @if (creative.size.name && !isEditing() && !isCreativeGroup) {
                    <span
                        class="size-name"
                        [uiTooltip]="creative.size.name"
                        uiTooltipPosition="bottom"
                        truncateSpan
                        [spanText]="creative.size.name"
                        (click)="editSizeName()">
                    </span>
                }
                <ng-container *permissions="'Default'">
                    @if (isEditing() && !isSaving() && !isCreativeGroup) {
                        @if (isNewUI()) {
                            <ui-input
                                #nameInput
                                [autofocus]="true"
                                placeholder="Name"
                                size="xs"
                                kind="secondary"
                                [ngModel]="creative.size.name"
                                (blur)="saveSizeName(nameInput.value || '')"
                                (click)="$event.stopPropagation()"
                                (clickOutside)="saveSizeName(nameInput.value || '')"
                                (keyup.enter)="saveSizeName(nameInput.value || '')"
                                (keyup.esc)="cancelEditSizeName()" />
                        } @else {
                            <input
                                #nameInput
                                class="name-input"
                                placeholder="Name"
                                type="text"
                                [maxlength]="250"
                                [ngModel]="creative.size.name"
                                [style.width.px]="
                                    (sizeNameCalc && sizeNameCalc.nativeElement.scrollWidth + 15) || 30
                                "
                                (blur)="saveSizeName(nameInput.value)"
                                (click)="$event.stopPropagation()"
                                (clickOutside)="saveSizeName(nameInput.value)"
                                (keyup.enter)="saveSizeName(nameInput.value)"
                                (keyup.esc)="cancelEditSizeName()" />
                        }
                    }

                    @if (isEditing() && !isCreativeGroup) {
                        <div
                            #sizeNameCalc
                            class="size-name-calc">
                            {{ creative.size.name }}
                        </div>
                    }
                    @if (isSaving() && !isCreativeGroup) {
                        <div class="edit-loader">
                            <ui-loader></ui-loader>
                        </div>
                    }

                    @if (!isSaving() && !isCreativeGroup) {
                        @if (isNewUI()) {
                            <ui-button
                                class="edit-button"
                                type="ghost-secondary"
                                size="xs"
                                [nuiSvgIcon]="'edit'"
                                [uiTooltip]="
                                    creative.size.name && creative.size.name.length
                                        ? 'Edit name'
                                        : 'Add name'
                                "
                                uiTooltipPosition="bottom"
                                (click)="editSizeName()" />
                        } @else {
                            <ui-svg-icon
                                class="hover-icon"
                                icon="edit"
                                [uiTooltip]="
                                    creative.size.name && creative.size.name.length
                                        ? 'Edit name'
                                        : 'Add name'
                                "
                                (click)="editSizeName()" />
                        }
                    }
                </ng-container>
            </div>
        </div>
    }
</div>
