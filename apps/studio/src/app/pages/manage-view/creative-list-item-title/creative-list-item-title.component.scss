@use 'mixins' as *;
@use 'variables' as *;

:where(:root:not([data-uinew])) :host {
    display: block;

    .size-label {
        & ::ng-deep {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 46px;
            z-index: 2;
            user-select: none;

            .creative-size {
                text-align: center;
                width: 100%;

                .x-icon {
                    margin: 0 2px;
                    font-size: 9px;
                }
            }

            .creative-size-name {
                padding-left: 5px;
                display: inline-block;
            }

            ui-flag {
                flex-shrink: 0;
                margin-right: 8px;

                @include media-breakpoint-down(desktop) {
                    --size: #{$mobile-icon-size} !important;
                }
            }

            .version-name {
                min-width: 0;
                overflow: hidden;
            }
        }
    }

    .size-name {
        color: var(--studio-color-text-second);
        font-size: 11px;
        display: inline-block;
        vertical-align: middle;
        margin-left: 5px;
        max-width: calc(100% - 100px);

        @include mobile-text;
    }

    .creative-size-fake-input {
        height: 22px;
        border-radius: 2px;
        border: 1px solid transparent;
        cursor: pointer;
        position: relative;
        text-align: center;
        align-items: center;
        justify-content: center;
        display: flex;

        @include mobile-text;

        &.editing {
            background: var(--studio-color-background-second);
            border: 1px solid var(--studio-color-primary) !important;

            .creative-size-name {
                color: var(--studio-color-text-second);
            }

            .hover-icon,
            .status-icon {
                display: none;
            }
        }

        .hover-icon {
            &:hover {
                color: var(--studio-color-black);
            }
        }

        .edit-loader {
            position: relative;
            width: 14px;
            margin-left: 5px;

            ::ng-deep {
                .ui-loader {
                    background: none;
                    width: auto;
                }
            }
        }
    }

    .name-input {
        display: inline-block;
        margin-left: 5px;
        font-size: 11px;
        background: none;
        border: none;
        outline: none;
        line-height: 14px;
        height: 14px;
        padding: 0;
        min-width: 40px;
        width: 40px;
        max-width: calc(100% - 100px);

        &::placeholder {
            color: var(--studio-color-grey-84);
        }
    }

    .status-icon {
        color: var(--studio-color-grey-84);
        visibility: visible;
        cursor: pointer;
        margin-left: 5px;
        vertical-align: middle;
        font-size: 14px;

        @include mobile-text;

        &_in-progress {
            color: var(--studio-color-status-progress);
        }

        &_for-review {
            color: var(--studio-color-status-review);
        }

        &_approved {
            color: var(--studio-color-status-approved);
        }

        &_not-approved {
            color: var(--studio-color-status-not-approved);
        }
    }

    .size-name-calc {
        display: inline-block;
        height: 0;
        overflow: hidden;
        position: absolute;
        top: 0;
        left: 0;
        font-size: 11px;
    }

    .approval-status-wrapper {
        display: flex;
        position: relative;
        height: 27px;
        align-items: center;

        & > .status-icon {
            &_no-status {
                visibility: hidden;
            }
        }

        .approval-status-selector {
            position: absolute;
            top: 0;
            margin-top: 1px;
            visibility: hidden;

            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid var(--studio-color-third);
            border-radius: 0.2rem;
            background: var(--studio-color-background-second);
            z-index: 100;
            height: 25px;
            white-space: nowrap;
            cursor: pointer;
            transition: visibility 0s 0.5s;

            .status-icon {
                visibility: inherit;
                margin: 0 10px;
                font-size: 14px;
            }
        }

        &:hover {
            .approval-status-selector {
                visibility: visible;
                transition: visibility 0s 0.1s;
            }
        }

        .arrow {
            min-width: 2.6rem;
            text-align: center;
            padding: 0 0.4rem;
            padding-left: 1.4rem;
            color: var(--studio-color-text-discrete);
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    ::ng-deep.approval-status-item {
        &.highlight {
            .content {
                font-weight: 600;
            }
        }
    }

    .margin-right-s {
        margin-right: 0.5rem;
    }

    .hover-icon {
        color: var(--studio-color-grey-84);
        visibility: hidden;
        cursor: pointer;
        margin-left: 5px;
        vertical-align: middle;

        &:hover {
            color: var(--studio-color-black);
        }
    }
}

:where(:root:not([data-uinew])) :host-context(.content:hover) {
    .hover-icon,
    .status-icon_no-status {
        visibility: visible;
    }
}
