:where(:root[data-uinew]) :host {
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
    width: calc(100% - 2 * var(--nui-icon-width) - 2 * var(--nui-space-200));

    .size-label {
        font-family: var(--nui-body-regular-font-family);
        font-size: var(--nui-body-regular-font-size);
        font-style: normal;
        line-height: var(--nui-body-regular-line-height);
        letter-spacing: var(--nui-body-regular-letter-spacing);
        width: 100%;
    }

    .creative-size-name {
        font-weight: var(--nui-body-bold-font-weight);
        white-space: nowrap;
    }

    .size-name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: var(--nui-text-secondary);
        font-weight: var(--nui-body-regular-font-weight);
    }

    .creative-size-fake-input {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: var(--nui-space-100);

        &.editing {
            display: block;

            .creative-size-name {
                display: none;
            }

            .edit-button,
            .status-button {
                display: none;
            }

            ui-input {
                padding: 0 var(--nui-space-100);
            }
        }

        ui-loader {
            position: relative;
            --background-color: none;
            width: inherit;
        }
    }

    .size-name-calc {
        display: none;
    }

    .approval-status-wrapper {
        display: flex;
        align-items: center;

        .approval-status-selector {
            display: none;
        }

        .status-button {
            display: flex;
        }

        &:hover {
            .approval-status-selector {
                display: block;
            }

            .status-button {
                display: none;
            }
        }
    }

    .edit-button {
        visibility: hidden;
    }
}

:where(:root[data-uinew]) :host-context(.content:hover) {
    .edit-button {
        visibility: visible;
    }
}
