import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, Input } from '@angular/core';
import { UIButtonComponent, UISVGIconComponent, UINewThemeService } from '@bannerflow/ui';
import { ICreative } from '@domain/creativeset/creative';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { ManageViewContextMenuService } from '../context-menu/manage-view-context-menu.service';
import { TileSelectService } from '../services/tile-select.service';

@Component({
    selector: 'creative-list-item-menu',
    imports: [CommonModule, UIButtonComponent, UISVGIconComponent],
    templateUrl: './creative-list-item-menu.component.html',
    styleUrls: [
        './creative-list-item-menu.component.scss',
        './creative-list-item.menu.new.component.scss'
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CreativeListItemMenuComponent {
    private uiNewThemeService = inject(UINewThemeService);
    @Input() creative?: ICreative;
    isMobileShowcase: boolean;
    menuOpen$: Observable<boolean>;
    isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    constructor(
        private environmentService: EnvironmentService,
        private manageViewContextMenuService: ManageViewContextMenuService,
        private tileSelectService: TileSelectService
    ) {
        this.isMobileShowcase = this.environmentService.isMobileShowcase;

        this.menuOpen$ = this.manageViewContextMenuService.opened$.pipe(
            filter(
                ({ creatives }) =>
                    creatives.length === 0 ||
                    creatives.some(creative => creative.id === this.creative?.id)
            ),
            map(({ isOpen }) => isOpen)
        );
    }

    openKebabMenu($event: MouseEvent): void {
        if ($event) {
            $event.stopPropagation();
            $event.preventDefault();
        }
        if (!this.creative) {
            return;
        }

        this.manageViewContextMenuService.close();
        this.tileSelectService.select($event, this.creative);
        this.manageViewContextMenuService.open($event, [this.creative], false, 15, 4);
    }
}
