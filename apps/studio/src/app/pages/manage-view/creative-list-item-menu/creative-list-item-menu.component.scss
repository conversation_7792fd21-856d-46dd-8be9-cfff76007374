:where(:root:not([data-uinew])) :host {
    display: block;

    position: absolute;
    right: 1.6rem;
    top: 1.7rem;
    z-index: 4;

    .menu {
        opacity: 0;
        z-index: 4;
        cursor: pointer;

        font-size: 14px;
        color: var(--studio-color-text-second);

        &:hover,
        &.active {
            opacity: 1;
            color: var(--studio-color-black);
        }
    }
}

:where(:root:not([data-uinew])) :host-context(.content:hover) {
    .menu {
        opacity: 1;
    }
}
