import { ChangeDetectionStrategy, Component, output, signal } from '@angular/core';
import { UIButtonComponent } from '@bannerflow/ui';

@Component({
    imports: [UIButtonComponent],
    selector: 'duplicate-creative-panel',
    templateUrl: './duplicate-creative-panel.component.html',
    styleUrls: [
        './duplicate-creative-panel.component.scss',
        './duplicate-creative-panel.new.component.scss'
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        '[class.ui-panel]': 'true'
    }
})
export class DuplicateCreativePanelComponent {
    name = signal<string>('');
    buttonIcon = signal<string>('');
    buttonClick = output<MouseEvent>();
}
