:where(:root[data-uinew]) :host {
    color: var(--nui-label-text-neutral);

    font-family: var(--nui-label-regular-font-family);
    font-size: var(--nui-label-regular-font-size);
    font-style: normal;
    font-weight: var(--nui-label-regular-font-weight);
    line-height: var(--nui-label-regular-line-height);
    letter-spacing: var(--nui-label-regular-letter-spacing);

    .version-section {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        align-self: stretch;
        gap: var(--nui-space-300);
        padding: var(--nui-space-500);

        .section-title {
            display: flex;
            align-items: center;
            gap: var(--nui-checkbox-space-gap);

            font-weight: var(--nui-label-bold-font-weight);
        }
    }

    .version {
        display: flex;
        align-items: center;
        flex-direction: row-reverse;
        cursor: pointer;
        gap: var(--nui-checkbox-space-gap);

        &__wrapper {
            display: flex;
            flex-direction: row-reverse;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
    }
}
