.menu-trigger {
    display: hidden;
    position: fixed;
}

.custom-dropdown {
    grid-template-columns: 0 auto 0 !important;

    .custom-item {
        display: inline-grid;
        width: 100%;
        grid-template-columns: 24px auto auto;
        margin: 0;
        align-items: center;

        .custom-column {
            display: grid;
            color: var(--studio-color-grey-84);
            justify-self: center;
            margin-right: 0;
            font-size: 14px;
        }

        .custom-action {
            justify-self: right;
            padding-right: 16px;
            color: var(--studio-color-text-second);

            &.mac::first-letter {
                font-size: 10px;
                letter-spacing: 1px;
            }
        }
    }
}
