@if (!isMobileShowcase && creative?.design?.elements?.length) {
    @if (isNewUI()) {
        <ui-button
            type="plain-primary"
            size="xs"
            nuiSvgIcon="open_in_new"
            uiTooltip="View in 100%"
            uiTooltipPosition="bottom"
            text="{{ scale * 100 | number: '1.0-0' }}%"
            (click)="openCreativeDialog()" />
    } @else {
        <span
            id="mv-preview"
            class="scale"
            [uiTooltip]="percentageTooltip"
            uiTooltipPosition="bottom"
            [uiTooltipHideArrow]="true"
            (click)="openCreativeDialog()">
            {{ scale * 100 | number: '1.0-0' }}%
        </span>
    }
}

@if (creative) {
    <ui-dialog
        #dialog="ui-dialog"
        [config]="dialogCreativeConfig">
        <ng-template ui-dialog-template>
            <div
                class="full-preview"
                [style.height.px]="creative.size.height"
                [style.width.px]="creative.size.width">
                <studio-creative
                    id="fullPreviewCreative"
                    [ngClass]="{ 'in-mobile-showcase': isMobileShowcase }"
                    [showTargetUrl]="!isMobileShowcase"
                    [creative]="creative"
                    [preview]="true"
                    [renderOnInit]="true"
                    [shouldPlay]="true"></studio-creative>
            </div>
        </ng-template>
    </ui-dialog>
}
