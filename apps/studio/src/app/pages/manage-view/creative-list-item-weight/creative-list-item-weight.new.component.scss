:where(:root[data-uinew]) :host {
    display: block;

    font-family: var(--nui-body-bold-font-family);
    font-size: var(--nui-body-bold-font-size);
    font-style: normal;
    font-weight: var(--nui-body-bold-font-weight);
    line-height: var(--nui-body-bold-line-height);
    letter-spacing: var(--nui-body-bold-letter-spacing);
    opacity: 0;

    .filesize {
        opacity: 0;
        &.loading {
            opacity: 1;
            position: relative;
        }

        .calculate-button {
            width: fit-content;
        }

        .filesize-text {
            display: flex;
            gap: var(--nui-space-100);
        }

        .subLoad {
            color: var(--nui-text-secondary);
        }

        .initialLoad {
            color: var(--nui-text-primary);
        }

        .ui-loader {
            --background-color: none;
            width: inherit;
        }
    }
}

:where(:root[data-uinew]) :host-context(.content:hover) {
    opacity: 1;
}
