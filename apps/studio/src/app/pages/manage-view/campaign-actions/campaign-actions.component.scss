:where(:root:not([data-uinew])) :host {
    margin-left: 2rem;

    ui-button {
        --font-size: 11px;
    }

    .push-button {
        min-width: 150px;
    }

    .ad-stats {
        color: var(--studio-color-grey-71);
        margin: 0 5px;
        cursor: pointer;
        display: grid;
        align-items: center;
        justify-items: center;
        grid-template-columns: repeat(2, auto);
        grid-gap: 2px;

        &:hover,
        &.active {
            color: var(--studio-color-black);
        }
    }

    .ad-count {
        align-self: end;
        padding-left: 0.4rem;
    }

    .ad-icon {
        font-size: 1.4rem;

        &.active {
            color: var(--studio-color-primary);
        }
    }

    .ui-button {
        &.disabled {
            pointer-events: none;
        }

        &:focus {
            outline: none;
        }
    }
}
