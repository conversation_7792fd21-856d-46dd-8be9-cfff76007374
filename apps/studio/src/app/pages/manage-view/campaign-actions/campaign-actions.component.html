@if (state$ | async; as currentState) {
    @switch (currentState) {
        @case ('list') {
            <ui-button
                id="interaction-topbar-open-campaigns"
                [type]="isNewUI() ? 'solid-primary' : 'default'"
                [text]="'View campaigns ' + '(' + ((numberOfCampaigns$ | async) ?? 0) + ')'"
                [uiDropdownTarget]="campaignsDropdown.dropdown"
                [loading]="(isUpdatingChecksums$ | async)!"
                size="sm"
                nuiSvgIcon="rocket_launch"
                svgIcon="campaigns-l">
            </ui-button>
        }
        @case ('push') {
            <ui-button
                id="push-changes-button"
                class="push-button"
                [type]="isNewUI() ? 'solid-primary' : 'default'"
                text="Push changes"
                [loading]="!!(isPublishing$ | async) || (isUpdatingChecksums$ | async)!"
                svgIcon="campaigns-l"
                nuiSvgIcon="rocket_launch"
                size="sm"
                (click)="onPushChanges()">
            </ui-button>
        }
        @case ('create') {
            <ui-button
                id="create-campaign-button"
                (click)="onCreateCampaign()"
                [uiDropdownTarget]="hasSocialCampaignPermissions ? campaignsDropdown.dropdown : empty"
                [type]="isNewUI() ? 'solid-primary' : 'default'"
                text="Create campaign"
                svgIcon="campaigns-l"
                size="sm"
                nuiSvgIcon="rocket_launch"
                uiTooltip="Create new campaign to publish your creatives"
                uiTooltipPosition="bottom"
                [uiTooltipWidth]="isNewUI() ? 'auto' : 265">
            </ui-button>
        }
    }

    <campaigns-dropdown
        #campaignsDropdown
        [isAddToCampaignVisible]="currentState === 'list'">
    </campaigns-dropdown>
    <ui-dropdown #empty></ui-dropdown>

    <add-to-campaign-dropdown #addToCampaignDropdown></add-to-campaign-dropdown>
}
