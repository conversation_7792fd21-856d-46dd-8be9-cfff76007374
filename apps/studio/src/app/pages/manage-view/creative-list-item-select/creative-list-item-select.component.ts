import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { UIModule } from '@bannerflow/ui';
import { ICreative } from '@domain/creativeset/creative';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ManageViewContextMenuService } from '../context-menu/manage-view-context-menu.service';
import { TileSelectService } from '../services/tile-select.service';

@Component({
    selector: 'creative-list-item-select',
    imports: [CommonModule, UIModule],
    templateUrl: './creative-list-item-select.component.html',
    styleUrls: [
        './creative-list-item-select.component.scss',
        './creative-list-item-select.new.component.scss'
    ],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CreativeListItemSelectComponent {
    @Input() index: number;
    @Input() creative: ICreative | undefined;

    isMobileShowcase: boolean;
    selected$: Observable<boolean>;

    constructor(
        private environmentService: EnvironmentService,
        private manageViewContextMenuService: ManageViewContextMenuService,
        private tileSelectService: TileSelectService
    ) {
        this.isMobileShowcase = this.environmentService.isMobileShowcase;

        this.selected$ = this.tileSelectService.selection$.pipe(
            map(creatives => creatives.some(creative => creative.id === this.creative?.id))
        );
    }

    onSelect($event: MouseEvent): void {
        if (!this.isMobileShowcase && $event) {
            $event.stopPropagation();
            $event.preventDefault();
        }

        if (!this.creative) {
            return;
        }

        this.manageViewContextMenuService.close();
        this.tileSelectService.select($event, this.creative, true);
    }
}
