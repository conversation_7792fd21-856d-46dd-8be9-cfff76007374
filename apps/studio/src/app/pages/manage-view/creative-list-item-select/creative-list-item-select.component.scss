:where(:root:not([data-uinew])) :host {
    display: block;
    position: absolute;
    top: 0;
    z-index: 4;

    ui-checkbox.checkbox {
        opacity: 0;
        cursor: pointer;
        width: 51px;
        height: 50px;
        display: grid;
        align-items: center;
        justify-items: center;
    }
}

:where(:root:not([data-uinew])) :host-context(.content:hover),
:where(:root:not([data-uinew])) :host-context(.content.active),
:where(:root:not([data-uinew])) :host-context(.content.selected) {
    .checkbox {
        opacity: 1;
    }
}
