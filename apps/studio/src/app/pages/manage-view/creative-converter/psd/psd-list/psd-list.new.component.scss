:where(:root[data-uinew]) :host {
    height: calc(100% - var(--nui-space-700));
    display: flex;
    flex-direction: column;
    overflow-y: scroll;
    gap: var(--nui-space-400);

    &:has(.psd-content) {
        justify-content: center;
        align-items: center;
    }
    .psd-content {
        text-align: center;
        align-items: start;
    }

    ui-accordion {
        width: 100%;
    }

    .accordion-content {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: var(--nui-space-200);
        align-self: stretch;
    }

    .layers-wrapper {
        display: flex;
        padding: var(--nui-space-400);
        flex-direction: column;
        align-items: flex-start;
        gap: var(--nui-space-200);

        border-radius: var(--nui-border-radius-small);
        border: 1px solid var(--nui-border-neutral-secondary-bold);
    }

    .psd-layer-item {
        display: flex;
        width: 374px;
        height: 40px;
        padding: var(--nui-space-200) 0px;
        align-items: center;
        gap: var(--nui-space-400);
        flex-shrink: 0;
    }
}
