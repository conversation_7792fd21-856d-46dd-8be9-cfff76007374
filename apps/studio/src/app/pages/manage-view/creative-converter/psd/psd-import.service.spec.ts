import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { UIDialogComponent, UIDialogRef, UINotificationService } from '@bannerflow/ui';
import { creativesetDataServiceMock } from '@mocks/creativeset.mock';
import { CreativesetDataService } from '@studio/common';
import { BrandService } from '@studio/common/brand';
import { provideEnvironment } from '@studio/common/environment/environment.provider';
import { FontFamiliesService } from '@studio/common/font-families';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { PSDElementType, PSDRootElement } from '@studio/domain/components/psd-import/psd';
import { ApolloTestingModule } from 'apollo-angular/testing';
import { of, Subject, Subscription } from 'rxjs';
import { environment } from '../../../../../environments/environment.test';
import { defineMatchMedia } from '../../../../shared/mocks/matchMedia.mock';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../../../shared/mocks/store.mock';
import { SizeAddService } from '../../size-add-dialog/size-add.service';
import { CreativeConverterUploadService } from '../creative-converter-upload.service';
import { CreativeConverterService } from '../creative-converter.service';
import { CreativeConverterStateService } from '../state/creative-converter.service';
import { simplePSD } from './mock/psd.mock';
import { PsdImportService } from './psd-import.service';
import { createBlueprintFromPsd } from './psd-reader';

const UINotificationServiceMock = jest.fn().mockImplementation(() => ({
    open: jest.fn()
}));

const EditCreativeServiceMock = jest.fn().mockImplementation(() => ({
    saveNewCreative: jest.fn()
}));

const SizeAddServiceMock = jest.fn().mockImplementation(() => ({
    addSizes: jest.fn()
}));

const creativeMock = {
    size: {
        id: 'size-1'
    },
    design: {
        id: 'design-1',
        document: { clearEmptyChildren: jest.fn() }
    }
} as any;

const blueprintMock: PSDRootElement = {
    type: PSDElementType.Root,
    id: 'id',
    name: 'root',
    position: { x: 0, y: 0 },
    size: { width: 100, height: 100 },
    thumbnail: '',
    hidden: false,
    opacity: 1,
    data: {
        children: [],
        flatChildren: []
    }
};

const creativeConverterServiceMock: Partial<CreativeConverterService> = {
    convertedCreative$: of({ creative: creativeMock, blueprint: blueprintMock })
};

const creativeConverterUploadServiceMock: Partial<CreativeConverterUploadService> = {
    hasAssetsToUpload: jest.fn().mockImplementation(() => false),
    assetUploadComplete$: new Subject(),
    uploadSubscription: Subscription.EMPTY
};

describe('PsdImportService', () => {
    let service: PsdImportService;

    const uiNotificationServiceMock: UINotificationService = new UINotificationServiceMock();
    const editCreativeServiceMock = new EditCreativeServiceMock();
    const sizeAddServiceMock = new SizeAddServiceMock();
    const fontFamiliesServiceMock = {
        fontFamilies$: of([])
    };
    const creativeConverterStateServiceMock = {
        psdLayers$: of([])
    };

    beforeAll(() => {
        defineMatchMedia();
    });

    beforeEach(() => {
        const blueprint = createBlueprintFromPsd(simplePSD);

        TestBed.configureTestingModule({
            providers: [
                provideHttpClient(),
                provideHttpClientTesting(),
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER,
                provideEnvironment(environment),
                EnvironmentService,
                { provide: UINotificationService, useValue: uiNotificationServiceMock },
                { provide: CreativesetDataService, useValue: creativesetDataServiceMock },
                { provide: CreativeConverterStateService, useValue: creativeConverterStateServiceMock },
                {
                    provide: UIDialogRef,
                    useValue: { config: { data: { creative: creativeMock, blueprint: blueprint } } }
                },
                { provide: SizeAddService, useValue: sizeAddServiceMock },
                {
                    provide: CreativeConverterService,
                    useValue: creativeConverterServiceMock
                },
                {
                    provide: CreativeConverterUploadService,
                    useValue: creativeConverterUploadServiceMock
                },
                { provide: FontFamiliesService, useValue: fontFamiliesServiceMock },
                PsdImportService,
                UIDialogComponent,
                BrandService
            ],
            imports: [ApolloTestingModule]
        });

        service = TestBed.inject(PsdImportService);
    });

    it('should call showErrorNotification() method with error message if createSize() fails', async () => {
        const errorMessage =
            'There was an error when creating the creative size. Please try again. If the problem persists, please contact our support team for assistance. We apologize for any inconvenience.';
        sizeAddServiceMock.addSizes = jest.fn().mockImplementation(() => Promise.reject(new Error()));

        await service.saveCreative();

        expect(uiNotificationServiceMock.open).toHaveBeenCalledWith(errorMessage, {
            type: 'error',
            placement: 'top',
            autoCloseDelay: 5000
        });
    });

    it('should call showErrorNotification() method with error message if createDesign() fails', async () => {
        const errorMessage =
            'There was an error when creating the creative design. Please try again. If the problem persists, please contact our support team for assistance. We apologize for any inconvenience.';
        editCreativeServiceMock.saveNewCreative = jest
            .fn()
            .mockImplementation(() => Promise.reject(new Error()));

        await service.saveCreative();

        expect(uiNotificationServiceMock.open).toHaveBeenCalledWith(errorMessage, {
            type: 'error',
            placement: 'top',
            autoCloseDelay: 5000
        });
    });
});
