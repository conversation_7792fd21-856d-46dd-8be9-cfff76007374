@let _layer = layer();

<ui-checkbox
    [selected]="_layer.selected && !_layer.error?.isCritical"
    [disabled]="_layer.error?.isCritical ?? false" />

<div class="thumbnail">
    @if (_layer.thumbnail) {
        <img
            [src]="_layer.thumbnail"
            data-test-id="psd-thumbnail"
            alt="Layer thumbnail" />
    }
</div>
<div
    class="name"
    [class.warning]="!!_layer.error"
    [class.disabled]="_layer.error?.isCritical">
    {{ _layer.name }}
</div>

<div class="right">
    <div class="warning-icon">
        @if (_layer.error && !_layer.error.isFixed) {
            <ui-svg-icon
                ui-popover-target
                #warningTarget="ui-popover-target"
                [id]="'target-' + _layer.id"
                [uiTooltip]="_layer.error.message"
                [uiTooltipDisabled]="!_layer.error"
                data-test-id="psd-warning-icon"
                icon="none"
                nuiIcon="status-in-progress-colored" />
        }
    </div>
    <ui-button
        type="plain-primary"
        [disabled]="_layer.error?.isCritical"
        [nuiSvgIcon]="_layer.hidden || _layer.error?.isCritical ? 'visibility_off' : 'visibility'"
        (click)="toggleVisibility.emit(_layer)"
        size="sm" />
</div>
