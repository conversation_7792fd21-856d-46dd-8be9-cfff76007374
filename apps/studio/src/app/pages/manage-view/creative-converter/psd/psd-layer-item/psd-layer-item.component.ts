import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, input, output, viewChildren } from '@angular/core';
import {
    UIButtonComponent,
    UICheckboxComponent,
    UIPopoverTargetDirective,
    UISVGIconComponent,
    UITooltipDirective
} from '@bannerflow/ui';
import { LayerItem } from '@studio/domain/components/psd-import/psd';
import { FixFontPopoverInput } from '@studio/domain/components/psd-import/import-fonts.types';

@Component({
    standalone: true,
    imports: [
        CommonModule,
        UICheckboxComponent,
        UISVGIconComponent,
        UITooltipDirective,
        UIPopoverTargetDirective,
        UIButtonComponent
    ],
    selector: 'psd-layer-item',
    templateUrl: './psd-layer-item.component.html',
    styleUrls: ['./psd-layer-item.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        class: 'layer',
        '[class.disabled]': 'layer().error?.isCritical',
        '(click)': 'toggleSelection.emit(layer())'
    }
})
export class PsdLayerItemComponent {
    layer = input.required<LayerItem>();
    index = input<number>(0);
    importFontData = input<FixFontPopoverInput | undefined>();

    toggleSelection = output<LayerItem>();
    toggleVisibility = output<LayerItem>();

    targets = viewChildren<UIPopoverTargetDirective>('warningTarget');
}
