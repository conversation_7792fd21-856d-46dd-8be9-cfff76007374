:where(:root:not([data-uinew])) :host {
    .psd-content {
        text-align: center;
        margin-top: 36vh;
    }

    .layer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        cursor: pointer;
        background: var(--studio-color-surface-second);

        &:hover {
            background: var(--studio-color-grey-93);
        }

        &--group {
            border-top: solid 1px var(--studio-color-border);
            border-bottom: solid 1px var(--studio-color-border);
            margin-top: -1px;
        }

        &__left {
            display: flex;
            width: 100%;
            height: 100%;
            align-items: center;
        }

        &__right {
            display: grid;
            grid-auto-flow: column;
            align-items: center;
            grid-template-areas: 'warning visibility selection';
            grid-template-columns: 24px 24px 24px;
            gap: 5px;
        }

        &__thumbnail {
            width: 40px;
            max-width: 40px;
            max-height: 90%;
            display: flex;
            justify-content: center;
            padding: 5px;

            img {
                object-fit: contain;
                width: 100%;
            }
        }

        &__name {
            max-width: 150px;
            font-size: 11px;
            display: flex;

            &--group {
                max-width: 180px;
            }

            .bold {
                font-weight: 700;
            }

            .title {
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                max-width: 155px;
                padding-right: 5px;
            }
        }

        &__toggle {
            width: 14px;
            height: 14px;
            margin: 0 5px;
            display: flex;
            justify-content: center;
            align-items: center;

            &--warning {
                grid-area: warning;
            }

            &--visibility {
                grid-area: visibility;
            }

            &--selection {
                grid-area: selection;
            }
        }

        &__icon {
            font-size: 20px;
            display: grid;
            align-items: center;
            justify-content: end;
            margin-right: 5px;

            &--hidden {
                color: var(--ui-color-text-disabled);
            }
            &--warning {
                color: var(--ui-color-warning);
            }

            ui-svg-icon {
                --color1: var(--studio-color-text);

                &.collapsed {
                    transform: rotateZ(180deg) !important;
                }
            }
        }
    }

    .group-layer {
        position: relative;
        display: flex;
        justify-content: space-between;
        padding: 0 1rem;
        background-color: var(--studio-color-surface-table);
        cursor: pointer;
        width: 100%;
        height: 26px;
    }

    .any-layer {
        height: 30px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 10px;
    }
}
