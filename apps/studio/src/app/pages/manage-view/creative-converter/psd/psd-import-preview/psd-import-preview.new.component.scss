@keyframes creative-bounce {
    0% {
        opacity: 0;
        transform: scale(0.5);
    }

    40% {
        opacity: 1;
        transform: scale(1);
    }

    100% {
        opacity: 1;
        transform: scale(1);
    }
}

:where(:root[data-uinew]) :host {
    .import-area {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: var(--nui-space-500);
        height: 100%;
    }

    .article-link {
        color: var(--nui-text-link);
        text-decoration: underline;
    }

    .inline-content {
        display: inline;
    }

    ui-file-dropzone {
        height: 100%;
    }

    .preview-wrapper {
        height: 100%;
    }

    .preview {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 100%;
    }

    .creative {
        position: relative;
        display: flex;
        padding: var(--nui-space-300);
        flex-direction: column;
        align-items: center;
        gap: var(--nui-space-300);

        &__size {
            color: var(--nui-text-secondary);

            font-size: var(--nui-label-regular-font-size);
            font-style: normal;
            font-weight: var(--nui-label-regular-font-weight);
            line-height: var(--nui-label-regular-line-height);
            letter-spacing: var(--nui-label-regular-letter-spacing);
        }

        &__outlet {
            height: inherit;
            width: inherit;
            animation: creative-bounce 0.75s ease;
            animation-fill-mode: forwards;
            box-sizing: content-box;
            border: 1px solid var(--nui-border-neutral-secondary-bold);
            border-radius: var(--nui-border-radius-tiny);
        }
    }
}
