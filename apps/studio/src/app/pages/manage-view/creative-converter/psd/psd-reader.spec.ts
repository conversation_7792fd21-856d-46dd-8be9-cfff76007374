import {
    PSDRootElement,
    PSDElementType,
    PSDLayerElement,
    PSDTextElement,
    IPSDElement,
    PSDGroupElement,
    PSDErrorType
} from '@studio/domain/components/psd-import/psd';
import { PSDConversionError } from '../conversion-errors';
import {
    mockEmptyPsd,
    mockEmptyTextLayer,
    mockImageLayer,
    mockPSDStyleRuns,
    mockTextLayer,
    mockVectorLayer,
    simplePSD
} from './mock/psd.mock';
import { createBlueprintFromPsd, parsePSDLayer } from './psd-reader';

describe('PSDReader', () => {
    describe('Root Layer', () => {
        let rootElement: PSDRootElement;

        beforeEach(() => {
            rootElement = createBlueprintFromPsd(simplePSD);
        });

        it('should parse layer type', function () {
            expect(rootElement.type).toBe(PSDElementType.Root);
        });

        it('should parse layer name', function () {
            expect(rootElement.name).toBe('Root');
        });

        it('should have 3 children', function () {
            expect(rootElement.data.children!.length).toBe(3); // 1 layer + 1 vector + 1 group
        });

        it('should have 5 layers', function () {
            expect(rootElement.data.flatChildren!.length).toBe(5); // 1 layer + 1 vector + 1 group + 2 texts (in group)
        });
    });

    describe('Image Layer', () => {
        let imageElement: PSDLayerElement;

        beforeEach(() => {
            imageElement = parsePSDLayer(mockImageLayer) as PSDLayerElement;
        });

        it('should parse layer type', function () {
            expect(imageElement.type).toBe(PSDElementType.Layer);
        });

        it('should parse layer name', function () {
            expect(imageElement.name).toBe('Background');
        });

        it('should parse layer opacity', function () {
            expect(imageElement.opacity).toBe(1);
        });
    });

    describe('Text Layer', () => {
        let textElement: PSDTextElement;

        beforeEach(() => {
            textElement = parsePSDLayer(mockTextLayer) as PSDTextElement;
        });

        it('should parse layer type', function () {
            expect(textElement.type).toBe(PSDElementType.Text);
        });

        it('should parse layer name', function () {
            expect(textElement.name).toBe('What Is Love?');
        });

        it('should parse layer opacity', function () {
            expect(textElement.opacity).toBe(0.7);
        });

        it('should parse text', function () {
            expect(textElement.data.text).toEqual('What Is Love?');
        });

        it('should parse font size', function () {
            expect(textElement.data.fontSize).toEqual(53);
        });

        it('should parse font family', function () {
            expect(textElement.data.font).toEqual('LuissSans-BoldItalic');
        });

        it('should parse justification', function () {
            expect(textElement.data.justification).toEqual('left');
        });

        it('should add an error on an empty text layer', function () {
            textElement = parsePSDLayer(mockEmptyTextLayer) as PSDTextElement;

            expect(textElement.data).toEqual({});
            expect(textElement.error!.message).toEqual('Text layer does not contain any text');
        });

        it('should scale a transformed text layers font size', function () {
            const transformedTextLayer = {
                ...mockTextLayer,
                text: {
                    ...mockTextLayer.text!,
                    transform: [10, 0, 0, 5, 64, 362.12],
                    style: {
                        ...mockTextLayer.text!.style!,
                        fontSize: 7
                    }
                }
            };
            textElement = parsePSDLayer(transformedTextLayer) as PSDTextElement;

            expect(textElement.data.fontSize).toEqual(70);
        });

        it('should scale a transformed text layer font size with character styles', function () {
            const transformedTextLayer = {
                ...mockTextLayer,
                text: {
                    ...mockTextLayer.text!,
                    transform: [0.5, 0, 0, 0.5, 64, 362.12],
                    style: {
                        ...mockTextLayer.text!.style!,
                        fontSize: undefined
                    },
                    styleRuns: mockPSDStyleRuns
                }
            };
            textElement = parsePSDLayer(transformedTextLayer) as PSDTextElement;

            expect(textElement.data.fontSize).toEqual(20); // 40px from style runs * 0.5 transform = 20px
        });
    });

    describe('Vector Layer', () => {
        let vectorElement: IPSDElement;

        beforeEach(() => {
            vectorElement = parsePSDLayer(mockVectorLayer);
        });

        // Will be implemented fully with - COBE-1010
        // it('should parse layer type', function () {
        //     expect(vectorElement.type).toBe(PSDElementType.Vector);
        // });

        it('should parse layer name', function () {
            expect(vectorElement.name).toBe('Strip');
        });

        it('should parse layer opacity', function () {
            expect(vectorElement.opacity).toBe(0.25);
        });

        it('should throw an error', function () {
            const errorLayer = {
                ...mockVectorLayer,
                vectorOrigination: {
                    keyDescriptorList: []
                }
            };
            const t = (): void => {
                vectorElement = parsePSDLayer(errorLayer);
            };
            expect(t).toThrow(TypeError);
        });
    });

    describe('Group Layer', () => {
        let rootElement: PSDRootElement;
        let groupElement: PSDGroupElement;

        beforeEach(() => {
            rootElement = createBlueprintFromPsd(simplePSD);
            groupElement = rootElement.data.children!.find(
                child => child.type === PSDElementType.Group
            ) as PSDGroupElement;
        });

        it('should parse layer type', function () {
            expect(groupElement.type).toBe(PSDElementType.Group);
        });

        it('should parse layer name', function () {
            expect(groupElement.name).toBe('TextGroup');
        });

        it('should parse layer opacity', function () {
            expect(groupElement.opacity).toBe(1);
        });

        it('should have 2 children', function () {
            expect(groupElement.data.children!.length).toBe(2);
        });

        it('should parse children', function () {
            const child = groupElement.data.children![0];
            expect(child.name).toBe("Baby, don't hurt me.");
        });
    });

    describe('Exceptions', () => {
        describe('createBlueprintFromPsd', () => {
            it('should throw PSDConversionError', () => {
                expect.assertions(2);
                try {
                    createBlueprintFromPsd(mockEmptyPsd);
                } catch (e) {
                    expect(e).toBeInstanceOf(PSDConversionError);
                    expect((e as PSDConversionError).type).toBe(PSDErrorType.CannotReadElements);
                }
            });
        });
    });
});
