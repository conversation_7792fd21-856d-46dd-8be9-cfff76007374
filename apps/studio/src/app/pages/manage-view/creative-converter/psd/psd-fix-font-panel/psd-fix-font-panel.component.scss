:where(:root:not([data-uinew])) :host {
    .wrapper {
        width: 100%;
        background: var(--studio-color-white-off-light);
        height: 100%;
        margin: 0;
        padding: 1em;
        display: flex;
        flex-direction: column;
        border: 1px solid var(--studio-color-grey);
        animation-fill-mode: forwards;
        animation-iteration-count: 1;

        &.open {
            animation: slide-panel 200ms ease-out;
            visibility: visible;
            animation-play-state: initial;
        }

        &.close {
            animation: slide-panel-out 200ms ease-out;
            animation-play-state: initial;
            visibility: hidden;
        }

        .header {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            font-weight: bold;
            color: var(--studio-color-hover);
            margin-bottom: 8px;

            ui-svg-icon {
                margin-right: 8px;
            }
        }

        .info-section {
            margin-bottom: 8px;
        }

        .inputs {
            margin-bottom: 8px;

            ui-checkbox {
                margin-bottom: 8px;
            }
        }

        .no-results-wrapper,
        .loader-wrapper {
            padding-top: 24px;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: top;
            justify-content: center;
        }
    }
}
@keyframes slide-panel {
    0% {
        visibility: hidden;
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(0%);
    }
}

@keyframes slide-panel-out {
    0% {
        transform: translateX(0%);
    }

    100% {
        visibility: hidden;
        transform: translateX(-100%);
    }
}
