:where(:root[data-uinew]) :host {
    display: flex;
    height: 40px;
    padding: var(--nui-space-200) 0px;
    align-items: center;
    gap: var(--nui-space-400);
    align-self: stretch;

    &.disabled {
        pointer-events: none;
        cursor: not-allowed;
    }

    .thumbnail {
        width: var(--nui-icon-width);
        height: var(--nui-icon-height);

        img {
            width: 100%;
            object-fit: contain;
        }
    }

    .name {
        flex-grow: 1;
        color: var(--nui-icon-primary);

        font-family: var(--nui-label-regular-sm-font-family);
        font-size: var(--nui-label-regular-sm-font-size);
        font-style: normal;
        font-weight: var(--nui-label-regular-sm-font-weight);
        line-height: var(--nui-label-regular-sm-line-height);
        letter-spacing: var(--nui-label-regular-sm-letter-spacing);

        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;

        &.disabled {
            color: var(--nui-text-disabled);
        }
    }

    .right {
        display: flex;
        align-items: center;
        gap: var(--nui-space-200);
    }

    .warning-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: var(--nui-icon-width);
        height: var(--nui-icon-height);
    }
}
