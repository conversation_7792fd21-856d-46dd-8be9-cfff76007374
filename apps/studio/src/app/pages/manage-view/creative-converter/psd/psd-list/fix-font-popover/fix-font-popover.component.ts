import { NgTemplateOutlet } from '@angular/common';
import { Component, computed, effect, inject, input, viewChild } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { CreativeConverterStateService } from '@app/manage-view/creative-converter/state/creative-converter.service';
import {
    UIModule,
    UINewThemeService,
    UIPopoverDirective,
    UIPopoverTargetDirective
} from '@bannerflow/ui';
import { FontFamiliesService } from '@studio/common/font-families';
import { FixFontPopoverInput } from '@studio/domain/components/psd-import/import-fonts.types';
import { PSDErrorType } from '@studio/domain/components/psd-import/psd';

@Component({
    imports: [UIModule, NgTemplateOutlet],
    selector: 'fix-font-popover',
    templateUrl: './fix-font-popover.component.html',
    styleUrls: ['./fix-font-popover.component.scss', './fix-font-popover.new.component.scss']
})
export class FixFontPopoverComponent {
    private creativeConverterStateService = inject(CreativeConverterStateService);
    private fontFamiliesService = inject(FontFamiliesService);
    private uiNewThemeService = inject(UINewThemeService);

    layerInput = input.required<FixFontPopoverInput | undefined>();
    importFontPopover = viewChild.required<UIPopoverDirective>('popover');

    shouldShowCheckbox = computed(() => this.computeShouldShowCheckbox());
    applyToAll = false;
    fontToFix = computed(() => this.layerInput()?.layer.error?.data?.missingFontName);
    alternativeFontName = computed(() => this.layerInput()?.layer.error?.data?.alternativeFontName);
    isNewUI = this.uiNewThemeService.isNewThemeEnabled;
    private layers = toSignal(this.creativeConverterStateService.psdLayers$, { initialValue: [] });

    constructor() {
        effect(() => {
            if (this.fontFamiliesService.isFontManagerDown()) {
                return;
            }
            const layerInput = this.layerInput();
            if (!layerInput) {
                this.closePopover();
                return;
            }
            this.openPopover(layerInput.target);
        });
    }

    useAlternativeFont(): void {
        const fontToFix = this.layerInput()?.layer.error?.data?.missingFontName;
        if (fontToFix) {
            this.creativeConverterStateService.useAlternativeFont(fontToFix, this.applyToAll);
        }
    }

    searchForFont(): void {
        const fontToFix = this.layerInput()?.layer.error?.data?.missingFontName;
        if (fontToFix) {
            this.creativeConverterStateService.setFontToFix(fontToFix, this.applyToAll);
        }
    }

    closePopoverClicked(): void {
        this.closePopover();
    }

    private openPopover(target: UIPopoverTargetDirective): void {
        const popover = this.importFontPopover();
        popover.open(target);
    }

    private closePopover(): void {
        const popover = this.importFontPopover();
        if (!popover?.isPopoverOpen) {
            return;
        }
        popover.close();
    }

    private computeShouldShowCheckbox(): boolean {
        const differentFontNames = new Set<string>();
        for (const layer of this.layers()) {
            if (layer.error?.type !== PSDErrorType.OriginalFontNotFound || layer.error.isFixed) {
                continue;
            }
            const fontToFix = layer.error?.data?.missingFontName;
            if (fontToFix) {
                differentFontNames.add(fontToFix);
            }
        }
        return differentFontNames.size > 1;
    }
}
