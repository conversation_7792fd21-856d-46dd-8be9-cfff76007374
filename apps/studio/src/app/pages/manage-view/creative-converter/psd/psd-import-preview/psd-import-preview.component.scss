$sidebar-width: 310px;

:where(:root:not([data-uinew])) :host {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    padding: var(--default-padding);
    .import-area {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        gap: 40px;
        flex-shrink: 0;
        height: calc(100% - 80px);
    }

    .note {
        padding: 20px;
        border-radius: 2px;
        border: 1px solid var(--ui-color-border);
        background: var(--ui-color-background);
        width: 100%;
    }

    .preview-wrapper {
        height: 100%;
    }

    .preview {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 100%;
    }

    .actions {
        width: 100%;
    }

    .creative {
        position: relative;
        margin: 30px;

        &__size {
            font-size: 0.9rem;
            color: var(--studio-color-text-discrete);
            text-align: center;
            padding-bottom: 5px;
            position: absolute;
            left: 50%;
            top: -15px;
            margin-left: -30px;
            width: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        &__outlet {
            height: inherit;
            width: inherit;
            animation: creative-bounce 0.75s ease;
            animation-fill-mode: forwards;
            border: 1px solid var(--studio-color-second);
            box-sizing: content-box;
        }
    }

    descriptive-loader {
        --bg-color: white;
    }
}
