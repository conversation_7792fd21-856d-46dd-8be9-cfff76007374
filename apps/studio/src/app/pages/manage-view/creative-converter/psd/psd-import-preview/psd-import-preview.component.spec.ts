import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { creativesetDataServiceMock } from '@mocks/creativeset.mock';
import { CreativesetDataService } from '@studio/common';
import { BrandService } from '@studio/common/brand';
import { provideEnvironment } from '@studio/common/environment/environment.provider';
import { FontFamiliesService } from '@studio/common/font-families';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { ApolloTestingModule } from 'apollo-angular/testing';
import { of } from 'rxjs';
import { environment } from '../../../../../../environments/environment.test';
import { defineMatchMedia } from '../../../../../shared/mocks/matchMedia.mock';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../../../../shared/mocks/store.mock';
import { NodeCreatorService } from '../../../../../shared/services/data-node-creator.service';
import { CreativeConverterUploadService } from '../../creative-converter-upload.service';
import { CreativeConverterService } from '../../creative-converter.service';
import { PsdImportService } from '../psd-import.service';
import { PsdImportPreviewComponent } from './psd-import-preview.component';

const fontFamiliesServiceMock: Partial<FontFamiliesService> = {
    fontFamilies$: of([]),
    nonDeletedBrandFontFamilies$: of([])
};

describe('PsdImportPreviewComponent', () => {
    let component: PsdImportPreviewComponent;
    let fixture: ComponentFixture<PsdImportPreviewComponent>;

    beforeAll(() => {
        defineMatchMedia();
    });

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [PsdImportPreviewComponent, ApolloTestingModule],
            providers: [
                provideHttpClient(),
                provideHttpClientTesting(),
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER,
                provideEnvironment(environment),
                EnvironmentService,
                {
                    provide: FontFamiliesService,
                    useValue: fontFamiliesServiceMock
                },
                {
                    provide: CreativesetDataService,
                    useValue: creativesetDataServiceMock
                },
                BrandService,
                CreativeConverterUploadService,
                NodeCreatorService,
                CreativeConverterService,
                PsdImportService
            ]
        }).compileComponents();

        fixture = TestBed.createComponent(PsdImportPreviewComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create the component', () => {
        expect(component).toBeTruthy();
    });

    it('should emit onCancel event when cancel() is called', () => {
        jest.spyOn(component.onCancel, 'emit');
        component.cancel();
        expect(component.onCancel.emit).toHaveBeenCalled();
    });
});
