:where(:root[data-uinew]) :host {
    .wrapper {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        align-self: stretch;
        gap: var(--nui-space-400);
        overflow-y: hidden;
        width: 329px;
        height: 100%;
        padding: var(--nui-card-space-padding-vertical) var(--nui-card-space-padding-horizontal);
        border-radius: var(--nui-card-radius);
        border: var(--nui-border-width-small) solid var(--nui-card-border-default);
        background: var(--nui-card-fill-default);
        box-shadow: 0px 4px 8px 0px rgba(0, 0, 51, 0.06);

        animation-fill-mode: forwards;
        animation-iteration-count: 1;

        &.open {
            animation: slide-panel 200ms ease-out;
            animation-play-state: initial;
        }

        &.close {
            animation: slide-panel-out 200ms ease-out;
            animation-play-state: initial;
            display: none;
        }

        .header {
            display: flex;
            align-items: center;
            gap: var(--nui-space-200);
            font-size: var(--nui-title-font-size);
            font-weight: var(--nui-title-font-weight);
            line-height: var(--nui-title-line-height);
            letter-spacing: var(--nui-title-letter-spacing);
        }

        .info-section {
            color: var(--nui-text-secondary);
            font-size: var(--nui-body-regular-font-size);
            font-weight: var(--nui-body-regular-font-weight);
            line-height: var(--nui-body-regular-line-height);
            letter-spacing: var(--nui-body-regular-letter-spacing);

            .strong-text {
                font-size: var(--nui-body-bold-font-size);
                font-weight: var(--nui-body-bold-font-weight);
                line-height: var(--nui-body-bold-line-height);
                letter-spacing: var(--nui-body-bold-letter-spacing);
            }
        }

        .inputs {
            display: flex;
            flex-direction: column;
            gap: var(--nui-space-200);
            width: 100%;
        }

        .search-results {
            overflow-y: auto;
            width: 100%;
        }

        .no-results-wrapper,
        .loader-wrapper {
            padding-top: var(--nui-card-space-padding-vertical);
            width: 100%;
            height: 100%;
            display: flex;
            align-items: top;
            justify-content: center;
        }
    }
}
@keyframes slide-panel {
    0% {
        visibility: hidden;
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(0%);
    }
}

@keyframes slide-panel-out {
    0% {
        transform: translateX(0%);
    }

    100% {
        visibility: hidden;
        transform: translateX(-100%);
    }
}
