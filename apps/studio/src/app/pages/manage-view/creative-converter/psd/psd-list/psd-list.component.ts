import { CommonModule } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    computed,
    inject,
    OnD<PERSON>roy,
    viewChildren
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import {
    UINewThemeService,
    UIPillStatus,
    UIPillType,
    UIPopoverTargetDirective,
    UICheckboxComponent,
    UITooltipDirective,
    UISVGIconComponent,
    UIAccordionComponent,
    UIAccordionItemComponent
} from '@bannerflow/ui';
import { FixFontPopoverInput } from '@studio/domain/components/psd-import/import-fonts.types';
import { LayerItem, PSDErrorType } from '@studio/domain/components/psd-import/psd';
import { SizeAddOverviewComponent } from '../../../size-add-dialog/size-add-overview/size-add-overview.component';
import { CreativeConverterStateService } from '../../state/creative-converter.service';
import { getValidChildren } from '../../utils/psd-layer.utils';
import { PsdImportService } from '../psd-import.service';
import { FixFontPopoverComponent } from './fix-font-popover/fix-font-popover.component';
import { PsdLayerItemComponent } from '../psd-layer-item/psd-layer-item.component';

enum LayerSelectedState {
    Unselected,
    SomeSelected,
    AllSelected
}

type LayerGroup = LayerItem & { children: LayerItem[] };
type TreeLayerItem = LayerItem & { children: TreeLayerItem[] };

@Component({
    imports: [
        CommonModule,
        SizeAddOverviewComponent,
        FixFontPopoverComponent,
        UIAccordionComponent,
        UIAccordionItemComponent,
        UISVGIconComponent,
        UICheckboxComponent,
        UITooltipDirective,
        UIPopoverTargetDirective,
        PsdLayerItemComponent
    ],
    selector: 'psd-list',
    templateUrl: './psd-list.component.html',
    styleUrls: ['./psd-list.component.scss', './psd-list.new.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class PsdListComponent implements OnDestroy {
    private uiThemeService = inject(UINewThemeService);
    private creativeConverterStateService = inject(CreativeConverterStateService);
    private psdImportService = inject(PsdImportService);

    LayerSelectedState = LayerSelectedState;

    private targets = viewChildren<UIPopoverTargetDirective>('warningTarget');
    private psdNewUILayerItems = viewChildren<PsdLayerItemComponent>('psdLayerItem');

    private selectedPsdLayers = toSignal(this.psdImportService.selectedPsdLayers$, {
        initialValue: []
    });
    numberOfSelectedPsdLayers = computed(() => this.selectedPsdLayers().length);
    layers = toSignal(this.creativeConverterStateService.psdLayers$, { initialValue: [] });
    layersGrouped = computed(() => this.groupLayers(this.layers()));
    importFontData = computed(() => this.computeImportFontData());
    private fontToFix = toSignal(this.creativeConverterStateService.fontToFix$);
    private proccessedLayers = new Set<string>();
    isNewUI = this.uiThemeService.isNewThemeEnabled;

    ngOnDestroy(): void {
        this.psdImportService.resetPsd();
    }

    private computeImportFontData(): FixFontPopoverInput | undefined {
        const fontToFix = this.fontToFix();
        if (fontToFix) {
            return;
        }

        const layers = this.layers();
        if (!layers) {
            return;
        }

        for (const layer of layers) {
            if (layer.error?.type !== PSDErrorType.OriginalFontNotFound) {
                continue;
            }
            if (layer.error.isFixed) {
                continue;
            }
            const missingFontName = layer.error.data?.missingFontName ?? '';
            if (this.proccessedLayers.has(missingFontName)) {
                continue;
            }

            let targets = [...this.targets()];
            if (this.isNewUI()) {
                targets = [
                    ...this.psdNewUILayerItems()
                        .map(item => item.targets()[0])
                        .filter(target => target !== undefined)
                ];
            }

            const clickedTarget = targets.find(
                target => target.host.nativeElement.id === `target-${layer.id}`
            );
            if (!clickedTarget) {
                return;
            }
            this.proccessedLayers.add(missingFontName);

            return {
                layer,
                target: clickedTarget
            };
        }
        return undefined;
    }

    // this is for new UI only
    // needed to satisfy single layers, accordions and layers within accordions
    private groupLayers(layers: LayerItem[]): LayerGroup[] {
        const layerMap = new Map<string, TreeLayerItem>();
        const roots: TreeLayerItem[] = [];
        const originalLayerMap = new Map<string, LayerItem>(layers.map(layer => [layer.id, layer]));

        for (const layer of layers) {
            layerMap.set(layer.id, { ...layer, children: [] });
        }

        for (const layer of layers) {
            const node = layerMap.get(layer.id);
            if (!node) {
                continue;
            }
            let parentId = layer.parentId;

            // If a group is nested within another group, treat it as a root-level group
            // by re-parenting it to its grandparent.
            if (layer.type === 'group' && layer.parentId) {
                const parent = originalLayerMap.get(layer.parentId);
                if (parent && parent.type === 'group') {
                    parentId = parent.parentId;
                }
            }

            if (parentId && layerMap.has(parentId)) {
                const parentNode = layerMap.get(parentId)!;
                parentNode.children.push(node);
            } else {
                roots.push(node);
            }
        }

        return this.flattenTree(roots);
    }

    private flattenTree(items: TreeLayerItem[]): LayerGroup[] {
        const layerGroups: LayerGroup[] = [];
        for (const layer of items) {
            if (layer.type === 'group') {
                layerGroups.push({
                    ...layer,
                    children: this.flattenChildren(layer.children)
                });
                continue;
            }

            if (layerGroups.length === 0 || layerGroups[layerGroups.length - 1].type === 'group') {
                layerGroups.push({
                    ...layer,
                    children: []
                });
            }
            layerGroups[layerGroups.length - 1].children.push(layer);
        }
        return layerGroups;
    }

    private flattenChildren(children: TreeLayerItem[]): LayerItem[] {
        const result: LayerItem[] = [];
        for (const child of children) {
            result.push(child);
            if (child.children.length > 0) {
                result.push(...this.flattenChildren(child.children));
            }
        }
        return result;
    }

    toggleGroupCollapse(groupLayer: LayerItem): void {
        this.creativeConverterStateService.toggleCollapse(groupLayer);
    }

    toggleVisibility(layer: LayerItem): void {
        this.creativeConverterStateService.toggleVisibility(layer);
    }

    toggleSelection(layer: LayerItem): void {
        this.creativeConverterStateService.toggleSelection(layer);
    }

    toggleAllSelection(): void {
        const anySelected = this.numberOfSelectedPsdLayers() > 0;
        this.creativeConverterStateService.toggleAllSelection(!anySelected);
    }

    getSelectedChildrenCount(layer: LayerItem): number {
        const validChildren = getValidChildren(this.layers(), layer);
        return validChildren.length;
    }

    getChildrenCount(layer: LayerItem): number {
        return this.psdImportService.getChildLayers(layer).filter(child => child.type !== 'group')
            .length;
    }

    selectionState(layer: LayerItem): LayerSelectedState {
        const childrenLength = this.getChildrenCount(layer);
        const selectedChildren = this.getSelectedChildrenCount(layer);

        if (selectedChildren === 0) {
            return LayerSelectedState.Unselected;
        }

        if (selectedChildren === childrenLength) {
            return LayerSelectedState.AllSelected;
        }

        return LayerSelectedState.SomeSelected;
    }

    getGroupPill(layer: LayerGroup): { text: string; status: UIPillStatus; type: UIPillType }[] {
        const hasUnfixedErrors = layer.children?.some(child => child.error?.isFixed === false);
        return hasUnfixedErrors ? [{ text: 'Warning', status: 'warning', type: 'secondary' }] : [];
    }
}
