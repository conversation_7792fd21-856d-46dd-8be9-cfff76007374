import { Layer, Psd } from 'ag-psd';
import { LayerTextData } from 'ag-psd/dist/psd';

export const mockCanvas = {
    toDataURL: (): string => 'base64;xxx',
    toBlob: function (callback: (blob: Blob) => void): void {
        callback(new Blob([''], { type: 'image/png' }));
    }
} as HTMLCanvasElement;

const mockTextData: LayerTextData = {
    transform: [1, 0, 0, 1, 64, 362.12],
    left: 0,
    top: 0,
    right: 100,
    bottom: 100,
    text: 'What Is Love?',
    index: 1,
    gridding: 'none',
    antiAlias: 'sharp',
    orientation: 'horizontal',
    warp: {
        style: 'none',
        value: 0,
        perspective: 0,
        perspectiveOther: 0,
        rotate: 'horizontal'
    },
    useFractionalGlyphWidths: true,
    superscriptSize: 0.583,
    superscriptPosition: 0.333,
    subscriptSize: 0.583,
    subscriptPosition: 0.333,
    smallCapSize: 0.7,
    shapeType: 'point',
    pointBase: [0, 0],
    paragraphStyle: {
        justification: 'left',
        firstLineIndent: 0,
        startIndent: 0,
        endIndent: 0,
        spaceBefore: 0,
        spaceAfter: 0,
        autoHyphenate: false,
        hyphenatedWordSize: 6,
        preHyphen: 2,
        postHyphen: 2,
        consecutiveHyphens: 8,
        zone: 36,
        wordSpacing: [0.8, 1, 1.33],
        letterSpacing: [0, 0, 0],
        glyphSpacing: [1, 1, 1],
        autoLeading: 1.2,
        leadingType: 0,
        hanging: false,
        burasagari: false,
        kinsokuOrder: 0,
        everyLineComposer: false
    },
    style: {
        font: {
            name: 'LuissSans-BoldItalic',
            script: 0,
            type: 1,
            synthetic: 0
        },
        fontSize: 53,
        fauxBold: false,
        fauxItalic: false,
        autoLeading: true,
        leading: 0,
        horizontalScale: 1,
        verticalScale: 1,
        tracking: 0,
        autoKerning: true,
        kerning: 0,
        baselineShift: 0,
        fontCaps: 0,
        fontBaseline: 0,
        underline: false,
        strikethrough: false,
        ligatures: true,
        dLigatures: false,
        baselineDirection: 2,
        tsume: 0,
        styleRunAlignment: 2,
        language: 0,
        noBreak: false,
        fillColor: {
            r: 255,
            g: 255,
            b: 255,
            a: 1
        },
        strokeColor: {
            r: 0,
            g: 0,
            b: 0,
            a: 1
        },
        fillFlag: true,
        strokeFlag: false,
        fillFirst: true,
        yUnderline: 1,
        outlineWidth: 1,
        characterDirection: 0,
        hindiNumbers: false,
        kashida: 1,
        diacriticPos: 2
    }
};
export const mockImageLayer: Layer = {
    top: -135,
    left: 0,
    bottom: 855,
    right: 1280,
    blendMode: 'normal',
    opacity: 1,
    clipping: false,
    transparencyProtected: false,
    hidden: false,
    name: 'Background',
    id: 5,
    sectionDivider: {
        type: 0
    },
    layerColor: 'none',
    referencePoint: {
        x: 0,
        y: 0
    },
    placedLayer: {
        id: '9bc92410-d71c-11e5-b1ae-a548a96e5f9f',
        placed: 'd3615ed4-d71c-11e5-b1ae-a548a96e5f9f',
        type: 'raster',
        pageNumber: 1,
        totalPages: 1,
        frameStep: {
            numerator: 0,
            denominator: 600
        },
        duration: {
            numerator: 0,
            denominator: 600
        },
        frameCount: 1,
        transform: [0, -135, 1280, -135, 1280, 855, 0, 855],
        width: 1280,
        height: 990,
        resolution: {
            value: 72,
            units: 'Density'
        },
        warp: {
            style: 'none',
            value: 0,
            perspective: 0,
            perspectiveOther: 0,
            rotate: 'horizontal',
            bounds: {
                top: {
                    value: 0,
                    units: 'Pixels'
                },
                left: {
                    value: 0,
                    units: 'Pixels'
                },
                bottom: {
                    value: 990,
                    units: 'Pixels'
                },
                right: {
                    value: 1280,
                    units: 'Pixels'
                }
            },
            uOrder: 4,
            vOrder: 4
        }
    },
    canvas: mockCanvas
};

export const mockTextLayer: Layer = {
    top: 325,
    left: 68,
    bottom: 363,
    right: 355,
    blendMode: 'normal',
    opacity: 0.7,
    clipping: false,
    transparencyProtected: false,
    hidden: false,
    name: 'What Is Love?',
    id: 10,
    sectionDivider: {
        type: 0
    },
    layerColor: 'none',
    referencePoint: {
        x: 0,
        y: 0
    },
    nameSource: 'rend',
    text: mockTextData,
    canvas: mockCanvas
};

export const mockVectorLayer: Layer = {
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    blendMode: 'normal',
    opacity: 0.25098039215686274,
    clipping: false,
    transparencyProtected: false,
    hidden: false,
    mask: {
        top: 0,
        left: 0,
        bottom: 720,
        right: 1280,
        defaultColor: 0,
        positionRelativeToLayer: false,
        disabled: false,
        fromVectorData: true,
        userMaskDensity: 1,
        userMaskFeather: 0,
        vectorMaskDensity: 1,
        vectorMaskFeather: 0,
        canvas: mockCanvas
    },
    name: 'Strip',
    id: 6,
    sectionDivider: {
        type: 0
    },
    layerColor: 'none',
    referencePoint: {
        x: 0,
        y: 0
    },
    vectorMask: {
        paths: [
            {
                open: false,
                fillRule: 'non-zero',
                operation: 'combine',
                knots: [
                    {
                        linked: true,
                        points: [0, 0, 0, 0, 0, 0]
                    },
                    {
                        linked: true,
                        points: [1280, 0, 1280, 0, 1280, 0]
                    },
                    {
                        linked: true,
                        points: [1280, 720, 1280, 720, 1280, 720]
                    },
                    {
                        linked: true,
                        points: [0, 720, 0, 720, 0, 720]
                    }
                ]
            }
        ],
        invert: false,
        notLink: false,
        disable: false,
        fillStartsWithAllPixels: false
    },
    vectorStroke: {
        strokeEnabled: false,
        fillEnabled: true,
        lineWidth: {
            value: 1,
            units: 'Pixels'
        },
        lineDashOffset: {
            value: 0,
            units: 'Points'
        },
        miterLimit: 100,
        lineCapType: 'butt',
        lineJoinType: 'miter',
        lineAlignment: 'center',
        scaleLock: false,
        strokeAdjust: false,
        lineDashSet: [],
        blendMode: 'normal',
        opacity: 1,
        content: {
            type: 'color',
            color: {
                r: 0,
                g: 0,
                b: 0
            }
        },
        resolution: 72
    },
    vectorFill: {
        type: 'color',
        color: {
            r: 0,
            g: 0,
            b: 0
        }
    },
    vectorOrigination: {
        keyDescriptorList: [
            {
                keyOriginType: 2,
                keyOriginResolution: 72,
                keyOriginShapeBoundingBox: {
                    top: {
                        value: 0,
                        units: 'Pixels'
                    },
                    left: {
                        value: 0,
                        units: 'Pixels'
                    },
                    bottom: {
                        value: 720,
                        units: 'Pixels'
                    },
                    right: {
                        value: 1280,
                        units: 'Pixels'
                    }
                },
                keyOriginRRectRadii: {
                    topRight: {
                        value: 0,
                        units: 'Pixels'
                    },
                    topLeft: {
                        value: 0,
                        units: 'Pixels'
                    },
                    bottomLeft: {
                        value: 0,
                        units: 'Pixels'
                    },
                    bottomRight: {
                        value: 0,
                        units: 'Pixels'
                    }
                },
                keyOriginBoxCorners: [
                    {
                        x: 0,
                        y: 0
                    },
                    {
                        x: 1280,
                        y: 0
                    },
                    {
                        x: 1280,
                        y: 720
                    },
                    {
                        x: 0,
                        y: 720
                    }
                ]
            }
        ]
    },
    canvas: mockCanvas
};

export const mockEmptyTextLayer: Layer = {
    id: 11,
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
    blendMode: 'normal',
    opacity: 1,
    clipping: false,
    transparencyProtected: false,
    hidden: false,
    name: 'Empty text',
    text: {
        transform: [1, 0, 0, 1, 262.2478790283203, 154.89322916666652],
        left: 0,
        top: 0,
        right: 0,
        bottom: 0,
        text: '',
        index: 1,
        gridding: 'none',
        antiAlias: 'strong',
        orientation: 'horizontal',
        warp: {
            style: 'none',
            value: 0,
            perspective: 0,
            perspectiveOther: 0,
            rotate: 'horizontal'
        },
        useFractionalGlyphWidths: true,
        superscriptSize: 0.583,
        superscriptPosition: 0.333,
        subscriptSize: 0.583,
        subscriptPosition: 0.333,
        smallCapSize: 0.7,
        shapeType: 'box',
        boxBounds: [0, 0, 3.2025, 6.19531],
        paragraphStyle: {},
        style: {}
    },
    nameSource: 'rend',
    blendClippendElements: true,
    blendInteriorElements: false,
    knockout: false,
    protected: {
        transparency: false,
        composite: false,
        position: false
    },
    layerColor: 'none',
    timestamp: 1678366960.9303,
    referencePoint: {
        x: 0,
        y: 0
    }
};

export const mockPSDStyleRuns = [
    {
        length: 1,
        style: {
            fontSize: 40,
            fillColor: {
                r: 26.009999999999998,
                g: 26.009999999999998,
                b: 26.009999999999998,
                a: 1
            }
        }
    },
    {
        length: 1,
        style: {
            fontSize: 30,
            fillColor: {
                r: 26.009999999999998,
                g: 26.009999999999998,
                b: 26.009999999999998,
                a: 1
            }
        }
    },
    {
        length: 1,
        style: {
            fontSize: 20,
            fillColor: {
                r: 26.009999999999998,
                g: 26.009999999999998,
                b: 26.009999999999998,
                a: 1
            }
        }
    },
    {
        length: 1,
        style: {
            fontSize: 40,
            fillColor: {
                r: 26.009999999999998,
                g: 26.009999999999998,
                b: 26.009999999999998,
                a: 1
            }
        }
    },
    {
        length: 1,
        style: {
            fontSize: 40,
            fillColor: {
                r: 26.009999999999998,
                g: 26.009999999999998,
                b: 26.009999999999998,
                a: 1
            }
        }
    },
    {
        length: 1,
        style: {
            fontSize: 20,
            fillColor: {
                r: 195.07500000000002,
                g: 13.004999999999999,
                b: 13.004999999999999,
                a: 1
            }
        }
    },
    {
        length: 1,
        style: {
            fontSize: 20,
            fillColor: {
                r: 195.07500000000002,
                g: 13.004999999999999,
                b: 13.004999999999999,
                a: 1
            }
        }
    },
    {
        length: 1,
        style: {
            fontSize: 40,
            fillColor: {
                r: 26.009999999999998,
                g: 26.009999999999998,
                b: 26.009999999999998,
                a: 1
            }
        }
    },
    {
        length: 1,
        style: {
            fontSize: 40,
            fillColor: {
                r: 26.009999999999998,
                g: 26.009999999999998,
                b: 26.009999999999998,
                a: 1
            }
        }
    },
    {
        length: 1,
        style: {
            fontSize: 40,
            fillColor: {
                r: 65.025,
                g: 251.94,
                b: 0,
                a: 1
            }
        }
    },
    {
        length: 1,
        style: {
            fontSize: 40,
            fillColor: {
                r: 65.025,
                g: 251.94,
                b: 0,
                a: 1
            }
        }
    },
    {
        length: 1,
        style: {
            fontSize: 40,
            fillColor: {
                r: 26.009999999999998,
                g: 26.009999999999998,
                b: 26.009999999999998,
                a: 1
            }
        }
    },
    {
        length: 1,
        style: {
            fontSize: 40,
            fillColor: {
                r: 26.009999999999998,
                g: 26.009999999999998,
                b: 26.009999999999998,
                a: 1
            }
        }
    },
    {
        length: 1,
        style: {
            fontSize: 40,
            fillColor: {
                r: 26.009999999999998,
                g: 26.009999999999998,
                b: 26.009999999999998,
                a: 1
            }
        }
    }
];

export const simplePSD: Psd = {
    children: [
        {
            top: -135,
            left: 0,
            bottom: 855,
            right: 1280,
            blendMode: 'normal',
            opacity: 1,
            clipping: false,
            transparencyProtected: false,
            hidden: false,
            name: 'Background',
            id: 5,
            sectionDivider: {
                type: 0
            },
            layerColor: 'none',
            referencePoint: {
                x: 0,
                y: 0
            },
            placedLayer: {
                id: '9bc92410-d71c-11e5-b1ae-a548a96e5f9f',
                placed: 'd3615ed4-d71c-11e5-b1ae-a548a96e5f9f',
                type: 'raster',
                pageNumber: 1,
                totalPages: 1,
                frameStep: {
                    numerator: 0,
                    denominator: 600
                },
                duration: {
                    numerator: 0,
                    denominator: 600
                },
                frameCount: 1,
                transform: [0, -135, 1280, -135, 1280, 855, 0, 855],
                width: 1280,
                height: 990,
                resolution: {
                    value: 72,
                    units: 'Density'
                },
                warp: {
                    style: 'none',
                    value: 0,
                    perspective: 0,
                    perspectiveOther: 0,
                    rotate: 'horizontal',
                    bounds: {
                        top: {
                            value: 0,
                            units: 'Pixels'
                        },
                        left: {
                            value: 0,
                            units: 'Pixels'
                        },
                        bottom: {
                            value: 990,
                            units: 'Pixels'
                        },
                        right: {
                            value: 1280,
                            units: 'Pixels'
                        }
                    },
                    uOrder: 4,
                    vOrder: 4
                }
            },
            canvas: mockCanvas
        },
        {
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
            blendMode: 'normal',
            opacity: 0.25098039215686274,
            clipping: false,
            transparencyProtected: false,
            hidden: false,
            mask: {
                top: 0,
                left: 0,
                bottom: 720,
                right: 1280,
                defaultColor: 0,
                positionRelativeToLayer: false,
                disabled: false,
                fromVectorData: true,
                userMaskDensity: 1,
                userMaskFeather: 0,
                vectorMaskDensity: 1,
                vectorMaskFeather: 0,
                canvas: mockCanvas
            },
            name: 'Strip',
            id: 6,
            sectionDivider: {
                type: 0
            },
            layerColor: 'none',
            referencePoint: {
                x: 0,
                y: 0
            },
            vectorMask: {
                paths: [
                    {
                        open: false,
                        fillRule: 'non-zero',
                        operation: 'combine',
                        knots: [
                            {
                                linked: true,
                                points: [0, 0, 0, 0, 0, 0]
                            },
                            {
                                linked: true,
                                points: [1280, 0, 1280, 0, 1280, 0]
                            },
                            {
                                linked: true,
                                points: [1280, 720, 1280, 720, 1280, 720]
                            },
                            {
                                linked: true,
                                points: [0, 720, 0, 720, 0, 720]
                            }
                        ]
                    }
                ],
                invert: false,
                notLink: false,
                disable: false,
                fillStartsWithAllPixels: false
            },
            vectorStroke: {
                strokeEnabled: false,
                fillEnabled: true,
                lineWidth: {
                    value: 1,
                    units: 'Pixels'
                },
                lineDashOffset: {
                    value: 0,
                    units: 'Points'
                },
                miterLimit: 100,
                lineCapType: 'butt',
                lineJoinType: 'miter',
                lineAlignment: 'center',
                scaleLock: false,
                strokeAdjust: false,
                lineDashSet: [],
                blendMode: 'normal',
                opacity: 1,
                content: {
                    type: 'color',
                    color: {
                        r: 0,
                        g: 0,
                        b: 0
                    }
                },
                resolution: 72
            },
            vectorFill: {
                type: 'color',
                color: {
                    r: 0,
                    g: 0,
                    b: 0
                }
            },
            vectorOrigination: {
                keyDescriptorList: [
                    {
                        keyOriginType: 2,
                        keyOriginResolution: 72,
                        keyOriginShapeBoundingBox: {
                            top: {
                                value: 0,
                                units: 'Pixels'
                            },
                            left: {
                                value: 0,
                                units: 'Pixels'
                            },
                            bottom: {
                                value: 720,
                                units: 'Pixels'
                            },
                            right: {
                                value: 1280,
                                units: 'Pixels'
                            }
                        },
                        keyOriginRRectRadii: {
                            topRight: {
                                value: 0,
                                units: 'Pixels'
                            },
                            topLeft: {
                                value: 0,
                                units: 'Pixels'
                            },
                            bottomLeft: {
                                value: 0,
                                units: 'Pixels'
                            },
                            bottomRight: {
                                value: 0,
                                units: 'Pixels'
                            }
                        },
                        keyOriginBoxCorners: [
                            {
                                x: 0,
                                y: 0
                            },
                            {
                                x: 1280,
                                y: 0
                            },
                            {
                                x: 1280,
                                y: 720
                            },
                            {
                                x: 0,
                                y: 720
                            }
                        ]
                    }
                ]
            }
        },
        {
            top: 0,
            left: 0,
            bottom: 0,
            right: 0,
            blendMode: 'pass through',
            opacity: 1,
            clipping: false,
            transparencyProtected: false,
            hidden: false,
            name: 'TextGroup',
            id: 11,
            sectionDivider: {
                type: 2,
                key: 'pass'
            },
            layerColor: 'none',
            referencePoint: {
                x: 0,
                y: 0
            },
            opened: false,
            children: [
                {
                    top: 378,
                    left: 63,
                    bottom: 399,
                    right: 195,
                    blendMode: 'normal',
                    opacity: 1,
                    clipping: false,
                    transparencyProtected: false,
                    hidden: false,
                    name: "Baby, don't hurt me.",
                    id: 8,
                    sectionDivider: {
                        type: 0
                    },
                    layerColor: 'none',
                    referencePoint: {
                        x: 0,
                        y: 0
                    },
                    nameSource: 'rend',
                    text: {
                        transform: [1, 0, 0, 1, 64, 393.92],
                        left: 0,
                        top: 0,
                        right: 100,
                        bottom: 100,
                        text: "Baby, don't hurt me.",
                        index: 0,
                        gridding: 'none',
                        antiAlias: 'sharp',
                        orientation: 'horizontal',
                        warp: {
                            style: 'none',
                            value: 0,
                            perspective: 0,
                            perspectiveOther: 0,
                            rotate: 'horizontal'
                        },
                        useFractionalGlyphWidths: true,
                        superscriptSize: 0.583,
                        superscriptPosition: 0.333,
                        subscriptSize: 0.583,
                        subscriptPosition: 0.333,
                        smallCapSize: 0.7,
                        shapeType: 'point',
                        pointBase: [0, 0],
                        paragraphStyle: {
                            justification: 'left',
                            firstLineIndent: 0,
                            startIndent: 0,
                            endIndent: 0,
                            spaceBefore: 0,
                            spaceAfter: 0,
                            autoHyphenate: false,
                            hyphenatedWordSize: 6,
                            preHyphen: 2,
                            postHyphen: 2,
                            consecutiveHyphens: 8,
                            zone: 36,
                            wordSpacing: [0.8, 1, 1.33],
                            letterSpacing: [0, 0, 0],
                            glyphSpacing: [1, 1, 1],
                            autoLeading: 1.2,
                            leadingType: 0,
                            hanging: false,
                            burasagari: false,
                            kinsokuOrder: 0,
                            everyLineComposer: false
                        },
                        style: {
                            font: {
                                name: 'SairaExtraCondensed-SemiBoldItalic',
                                script: 0,
                                type: 1,
                                synthetic: 0
                            },
                            fontSize: 21,
                            fauxBold: false,
                            fauxItalic: false,
                            autoLeading: true,
                            leading: 0,
                            horizontalScale: 1,
                            verticalScale: 1,
                            tracking: 0,
                            autoKerning: true,
                            kerning: 0,
                            baselineShift: 0,
                            fontCaps: 0,
                            fontBaseline: 0,
                            underline: false,
                            strikethrough: false,
                            ligatures: true,
                            dLigatures: false,
                            baselineDirection: 2,
                            tsume: 0,
                            styleRunAlignment: 2,
                            language: 0,
                            noBreak: false,
                            fillColor: {
                                r: 255,
                                g: 255,
                                b: 255,
                                a: 1
                            },
                            strokeColor: {
                                r: 0,
                                g: 0,
                                b: 0,
                                a: 1
                            },
                            fillFlag: true,
                            strokeFlag: false,
                            fillFirst: true,
                            yUnderline: 1,
                            outlineWidth: 1,
                            characterDirection: 0,
                            hindiNumbers: false,
                            kashida: 1,
                            diacriticPos: 2
                        }
                    },
                    canvas: mockCanvas
                },
                {
                    top: 325,
                    left: 68,
                    bottom: 363,
                    right: 355,
                    blendMode: 'normal',
                    opacity: 1,
                    clipping: false,
                    transparencyProtected: false,
                    hidden: false,
                    name: 'What Is Love?',
                    id: 10,
                    sectionDivider: {
                        type: 0
                    },
                    layerColor: 'none',
                    referencePoint: {
                        x: 0,
                        y: 0
                    },
                    nameSource: 'rend',
                    text: mockTextData,
                    canvas: mockCanvas
                }
            ]
        }
    ],
    bitsPerChannel: 8,
    channels: 3,
    colorMode: 3,
    height: 720,
    width: 1280,
    linkedFiles: [
        {
            id: '9bc92410-d71c-11e5-b1ae-a548a96e5f9f',
            name: 'Background.jpg',
            data: new Uint8Array()
        }
    ],
    engineData: 'IC85OCA8PCAvMCA3I..........<cut-off>',
    imageResources: {
        resolutionInfo: {
            horizontalResolution: 72,
            horizontalResolutionUnit: 'PPI',
            widthUnit: 'Centimeters',
            verticalResolution: 72,
            verticalResolutionUnit: 'PPI',
            heightUnit: 'Centimeters'
        },
        gridAndGuidesInformation: {
            grid: {
                horizontal: 576,
                vertical: 576
            },
            guides: []
        },
        thumbnail: mockCanvas,
        idsSeedNumber: 10,
        slices: [
            {
                groupName: 'User',
                bounds: {
                    top: 0,
                    left: 0,
                    bottom: 0,
                    right: 0
                },
                slices: []
            }
        ],
        layerSelectionIds: [10]
    },
    canvas: mockCanvas
};

export const mockEmptyPsd: Psd = {
    width: 1234,
    height: 1234,
    children: undefined
};
