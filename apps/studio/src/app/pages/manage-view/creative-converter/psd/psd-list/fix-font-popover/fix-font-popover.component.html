<ui-popover
    #popover="ui-popover"
    uiSize="xs"
    [config]="{
        width: isNewUI() ? '307px' : '285px',
        arrowPosition: 'left',
        position: 'right',
        panelClass: 'no-padding transparent',
        offset: { x: isNewUI() ? 40 : 70, y: 0 }
    }">
    <ng-template ui-popover-template>
        <ng-container *ngTemplateOutlet="popoverContent"></ng-container>
    </ng-template>
</ui-popover>

<ng-template #popoverContent>
    <div
        class="fix-font-wrapper"
        [class.new-ui]="isNewUI()"
        [class.old-ui]="!isNewUI()"
        uiSize="xs"
        data-test-id="font-fix-popover">
        <div
            class="headline"
            uiSize="lg">
            <strong class="headline-text"> Missing font </strong>
            <ui-svg-icon
                data-test-id="close-font-fix-popover"
                [icon]="'close'"
                (click)="closePopoverClicked()" />
        </div>
        <div
            class="section"
            uiSize="sm">
            The <strong class="strong">{{ fontToFix() ?? '' }}</strong> font in the imported PSD file
            that doesn't exist in your brand. Would you like to search for the similar font in the
            external library, or use the suggested alternative –
            <strong class="strong">{{ alternativeFontName() }}</strong>
        </div>
        @if (shouldShowCheckbox()) {
            <div class="checkbox">
                <ui-checkbox
                    size="sm"
                    [(selected)]="applyToAll"
                    label="Apply to all layers with missing fonts" />
            </div>
        }
        <div class="buttons">
            <ui-button
                [type]="isNewUI() ? 'solid-secondary' : 'default'"
                text="Use alternative"
                data-test-id="use-alternative-font"
                size="sm"
                (click)="useAlternativeFont()" />
            <ui-button
                [type]="isNewUI() ? 'solid-primary' : 'primary'"
                text="Search"
                data-test-id="search-font"
                size="sm"
                (click)="searchForFont()" />
        </div>
    </div>
</ng-template>
