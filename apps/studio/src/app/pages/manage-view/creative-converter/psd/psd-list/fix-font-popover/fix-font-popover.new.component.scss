.fix-font-wrapper.new-ui {
    display: flex;
    width: 307px;
    padding: var(--nui-dialog-space-padding-vertical) var(--nui-dialog-space-padding-horizontal);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--nui-dialog-space-gap);
    border-radius: var(--nui-dialog-radius);
    border: var(--nui-dialog-border) solid var(--nui-dialog-border-primary-default);
    background: var(--nui-dialog-fill-primary-default);
    box-shadow: 0px 5px 20px 0px rgba(0, 8, 48, 0.27);
    margin-left: var(--nui-space-1200);

    .headline {
        display: flex;
        justify-content: space-between;
        align-items: center;
        align-self: stretch;

        .headline-text {
            font-size: var(--nui-title-font-size);
            font-weight: var(--nui-title-font-weight);
            line-height: var(--nui-title-line-height);
        }
    }

    .section {
        color: var(--nui-text-primary);

        font-size: var(--nui-body-regular-font-size);
        font-weight: var(--nui-body-regular-font-weight);
        line-height: var(--nui-body-regular-line-height);

        .strong {
            font-size: var(--nui-body-bold-font-size);
            font-weight: var(--nui-body-bold-font-weight);
            line-height: var(--nui-body-bold-line-height);
        }
    }

    .buttons {
        display: flex;
        justify-content: space-between;
        width: 100%;
    }
}
