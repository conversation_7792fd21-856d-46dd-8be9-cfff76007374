import { IFontFamily } from '@domain/font-families';
import { IWordSpan, OneOfEditableSpans } from '@domain/text';
import { FONT_FAMILIES_MOCK } from '@mocks/font-families.mock';
import { getPossibleFontStyle, getStylesFromPSDRuns } from './creative-converter.utils';
import { mockPSDStyleRuns } from './psd/mock/psd.mock';

const currentSpans = [
    {
        type: 'word',
        content: 'WHAT',
        style: {}
    },
    {
        type: 'space',
        content: ' ',
        style: {}
    },
    {
        type: 'word',
        content: 'is',
        style: {}
    },
    {
        type: 'space',
        content: ' ',
        style: {}
    },
    {
        type: 'word',
        content: 'small?',
        style: {},
        styleIds: {}
    },
    {
        type: 'end',
        content: 'END'
    }
] as unknown as OneOfEditableSpans[];

describe('CreativeConverterUtils', () => {
    describe('getPossibleFontStyle', () => {
        it('should find `Tahoma Regular` font style', () => {
            const targetFont = 'Tahoma Regular';

            const fontStyle = getPossibleFontStyle(targetFont, FONT_FAMILIES_MOCK)!;
            expect(fontStyle.id).toEqual('tahomaId-regular');
            expect(fontStyle.fontFamilyId).toEqual('tahomaID');
        });

        it('should find `Tahoma-Light` font style', () => {
            const targetFont = 'Tahoma-Light';

            const fontStyle = getPossibleFontStyle(targetFont, FONT_FAMILIES_MOCK)!;
            expect(fontStyle.id).toEqual('tahomaId-light');
            expect(fontStyle.fontFamilyId).toEqual('tahomaID');
        });

        it('should find font family for `Tahoma-Shine` but default font style', () => {
            const targetFont = 'Tahoma-Shine';

            const fontStyle = getPossibleFontStyle(targetFont, FONT_FAMILIES_MOCK)!;
            expect(fontStyle.id).toEqual('tahomaId-regular');
            expect(fontStyle.fontFamilyId).toEqual('tahomaID');
        });

        it('should find `Tahoma123-Italic` font style', () => {
            const targetFont = 'Tahoma123-Italic';

            const fontStyle = getPossibleFontStyle(targetFont, FONT_FAMILIES_MOCK)!;
            expect(fontStyle.id).toEqual('tahomaId-italic');
            expect(fontStyle.fontFamilyId).toEqual('tahomaID');
        });

        it('should not find `Helvetica Regular` font style and default to first font', () => {
            const targetFont = 'Helvetica Regular';
            const fontStyle = getPossibleFontStyle(targetFont, FONT_FAMILIES_MOCK)!;
            expect(fontStyle.id).toEqual('fontStylesId1');
            expect(fontStyle.fontFamilyId).toEqual('fontFamilyId1');
        });

        it('should find `Membra` over `Mogra`', () => {
            const targetFont = 'Membra';
            const fontFamilies: IFontFamily[] = [
                {
                    id: 'mograFontFamilyId',
                    name: 'Mogra',
                    createdAt: new Date(),
                    deletedAt: undefined,
                    visibleBrandIds: null,
                    fontStyles: [
                        {
                            id: 'mograFontStyleId',
                            name: 'Regular',
                            deletedAt: undefined,
                            createdAt: new Date(),
                            unicodeGlyphs: [2323],
                            italic: false,
                            weight: 400,
                            fontUrl: 'test.woff',
                            fontFamilyId: 'mograFontFamilyId'
                        }
                    ]
                },
                {
                    id: 'membraFontFamilyId',
                    name: 'Membra',
                    createdAt: new Date(),
                    deletedAt: undefined,
                    visibleBrandIds: null,
                    fontStyles: [
                        {
                            id: 'membraFontStyleId',
                            name: 'Regular',
                            deletedAt: undefined,
                            createdAt: new Date(),
                            unicodeGlyphs: [2424],
                            italic: false,
                            weight: 400,
                            fontUrl: 'test.woff',
                            fontFamilyId: 'membraFontFamilyId'
                        }
                    ]
                }
            ];
            const fontStyle = getPossibleFontStyle(targetFont, fontFamilies)!;
            expect(fontStyle.id).toEqual('membraFontStyleId');
            expect(fontStyle.fontFamilyId).toEqual('membraFontFamilyId');
        });
    });

    describe('character styles', () => {
        const BASE_FONT_SIZE = 40;
        const ROOT_NODE_ID = '#rootNodeId';

        it('should create new spans for styles', () => {
            const { spans } = getStylesFromPSDRuns(
                currentSpans,
                mockPSDStyleRuns,
                BASE_FONT_SIZE,
                ROOT_NODE_ID,
                FONT_FAMILIES_MOCK
            );
            expect(spans.length).toEqual(11);
        });

        it('should deduplicate style runs', () => {
            const { spans } = getStylesFromPSDRuns(
                currentSpans,
                mockPSDStyleRuns,
                BASE_FONT_SIZE,
                ROOT_NODE_ID,
                FONT_FAMILIES_MOCK
            );
            // spans - 1 because of the end span
            expect(spans.length - 1).not.toEqual(mockPSDStyleRuns.length);
        });

        it('should make second letter smaller', () => {
            const { spans } = getStylesFromPSDRuns(
                currentSpans,
                mockPSDStyleRuns,
                BASE_FONT_SIZE,
                ROOT_NODE_ID,
                FONT_FAMILIES_MOCK
            );
            expect((spans[1] as IWordSpan).style.fontSize).toEqual(0.75); // 40 * 0.75 = 30px
        });

        it('should properly map style ids', () => {
            const { spans } = getStylesFromPSDRuns(
                currentSpans,
                mockPSDStyleRuns,
                BASE_FONT_SIZE,
                ROOT_NODE_ID,
                FONT_FAMILIES_MOCK
            );
            expect(
                Object.prototype.hasOwnProperty.call((spans[0] as IWordSpan).styleIds, '#rootNodeId')
            ).toBeTruthy();
        });

        it('should extract characterStyles map from PSD runs', () => {
            const { characterStyles } = getStylesFromPSDRuns(
                currentSpans,
                mockPSDStyleRuns,
                BASE_FONT_SIZE,
                ROOT_NODE_ID,
                FONT_FAMILIES_MOCK
            );
            expect(characterStyles.size).toEqual(5); // no duplicates
        });
    });
});
