:host {
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    height: 100vh;
}

.content {
    display: flex;
    text-align: center;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 92rem;
    font-family: var(--default-font-family);

    p {
        margin: 0;
    }
}

.title {
    h1 {
        margin-top: 0;
        font-size: 2.8rem;
        font-weight: var(--default-font-weight-bold);
    }
}

.link-to-creativeset {
    font-size: 16px;
    color: var(--studio-color-primary);
    cursor: pointer;
}

.text {
    font-size: 16px;

    .second-ph,
    .third-ph {
        margin-top: 40px;
        margin-bottom: 10px;
        font-size: 14px;
        color: var(--studio-color-text-discrete);
        opacity: 0.99;
        line-height: 1.43;

        a {
            color: var(--studio-color-primary);
        }
    }

    .third-ph {
        margin-top: 0;
    }
}

.logo {
    width: 73px;
    height: 73px;
    margin-bottom: 40px;
}

.last-sets {
    list-style: none;
}
