import { Component, Input, OnInit } from '@angular/core';
import { isImageElement, isVideoElement } from '@creative/nodes/helpers';
import {
    IBrandLibraryElement,
    INewBrandLibraryElement,
    OneOfLibraryAssets
} from '@domain/brand/brand-library';
import { BrandLibraryDataService } from '../../../shared/media-library/brand-library.data.service';

@Component({
    selector: 'edit-element-preview',
    templateUrl: 'edit-element-preview.component.html',
    styleUrls: ['edit-element-preview.component.scss'],
    standalone: false
})
export class EditElementPreviewComponent implements OnInit {
    @Input() element: IBrandLibraryElement | INewBrandLibraryElement;
    asset: OneOfLibraryAssets;
    isVideo: boolean;
    isImage: boolean;
    constructor(private brandLibraryDataService: BrandLibraryDataService) {}

    ngOnInit(): void {
        this.asset = this.brandLibraryDataService.getAssetByElement(
            this.element as IBrandLibraryElement
        )!;
        this.isVideo = isVideoElement(this.element);
        this.isImage = isImageElement(this.element);
    }
}
