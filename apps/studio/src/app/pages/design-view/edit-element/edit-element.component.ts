import {
    Component,
    DestroyRef,
    ElementRef,
    EventEmitter,
    HostListener,
    Input,
    OnDestroy,
    OnInit,
    Output,
    QueryList,
    ViewChild,
    ViewChildren
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
    AbstractControl,
    FormBuilder,
    UntypedFormControl,
    UntypedFormGroup,
    ValidatorFn,
    Validators
} from '@angular/forms';
import { Logger } from '@bannerflow/sentinel-logger';
import {
    UIButtonComponent,
    UIConfirmCloseDialogCallback,
    UIConfirmDialogResult,
    UIConfirmDialogService,
    UIInputComponent,
    UINotificationService,
    UISubmitResponse
} from '@bannerflow/ui';
import { Color } from '@creative/color';
import { parseColor } from '@creative/color.utils';
import { createFeed } from '@creative/elements/feed/feeds.utils';
import { sanitizeText } from '@creative/elements/rich-text/utils';
import {
    applyCustomPropertyPrefix,
    isBFLibraryWidgetReference,
    isWidgetCodeProperty,
    isWidgetReference,
    mapElementToCustomWidgetProperties,
    stripCustomPropertyPrefix
} from '@creative/elements/widget/utils';
import { mergeWidgetKind } from '@creative/nodes/helpers';
import { serializeWidgetPropertyValue } from '@creative/serialization';
import {
    CreateWidgetAssetDto,
    UpdateWidgetDto,
    UpsertWidgetResultDto
} from '@domain/api/generated/sapi';
import {
    IBrandLibrary,
    IBrandLibraryElement,
    ImageLibraryAsset,
    INewBrandLibraryElement,
    IWidgetLibraryAsset,
    LibraryWidget,
    LibraryWidgetState,
    PartialLibraryAsset
} from '@domain/brand/brand-library';
import { ColorType, IColor } from '@domain/color';
import { WidgetCode } from '@domain/creative/elements/widget/widget-renderer.header';
import { IElementProperty } from '@domain/creativeset/element';
import { AssetReference } from '@domain/creativeset/element-asset';
import { IWidgetText } from '@domain/creativeset/version';
import { ElementKind } from '@domain/elements';
import { IBfFeed, IFeed } from '@domain/feed';
import { IFontStyle, ISelectedFont } from '@domain/font';
import { IFontFamily, IFontFamilyStyle } from '@domain/font-families';
import { IHotkeyContext } from '@domain/hotkeys/hotkeys.types';
import { OneOfElementPropertyKeys } from '@domain/nodes';
import {
    IWidgetCustomProperty,
    IWidgetElementProperty,
    IWidgetImage,
    IWidgetSelectOption,
    WidgetUnits
} from '@domain/widget';
import { FontFamiliesService } from '@studio/common/font-families';
import { UserService } from '@studio/common/user/user.service';
import { BrowserDefaultHotkeys } from '@studio/hotkeys/hotkeys';
import { cloneDeep } from '@studio/utils/clone';
import { createElementProperty, isNewBrandlibraryElement } from '@studio/utils/element.utils';
import { b64EncodeUnicode } from '@studio/utils/encoding';
import { isImageReference, isVideoReference } from '@studio/utils/media';
import { deepEqual, moveItemInArray, sanitizeString, stringToCamelCase } from '@studio/utils/utils';
import { filter, firstValueFrom, Subject } from 'rxjs';
import { BrandLibraryDataService } from '../../../shared/media-library/brand-library.data.service';
import { MediaLibraryService } from '../../../shared/media-library/state/media-library.service';
import { FileDownloadService } from '../../../shared/services/filedownload.service';
import { HotkeyBetterService } from '../../../shared/services/hotkeys/hotkey.better.service';
import { WidgetDataService } from '../../../shared/services/widget.data.service';
import { WidgetAssetService } from '../../../shared/widget-asset/widget-asset.data.service';
import { IAssetSelectionEvent } from '../asset-picker/asset-picker.component';
import { ColorService } from '../color-section/color.service';
import { DesignViewComponent } from '../design-view.component';
import { SERVICES } from '../design-view.services';
import { BrandLibraryElementDeletionService } from '../media-library/brandlibrary-element-deletion.service';
import { BrandLibraryElementEditService } from '../media-library/brandlibrary-element-edit.service';
import { AssetPropertyContext } from '../properties-panel/asset-property/asset-property';
import { WidgetPropertiesService } from '../properties-panel/widget/widget-properties.service';
import { AssetUploadService } from '../services/asset-upload.service';
import { EditorStateService } from '../services/editor-state.service';
import { MutatorService } from '../services/mutator.service';
import { WidgetEditorComponent, WidgetEditorError } from '../widget-editor/widget-editor.component';
import { File } from './edit-element-topbar.component';

@Component({
    selector: 'edit-element',
    templateUrl: 'edit-element.component.html',
    styleUrls: ['edit-element.component.scss'],
    providers: [...SERVICES, HotkeyBetterService],
    standalone: false
})
export class EditElementComponent implements OnInit, OnDestroy {
    @Input() element: IBrandLibraryElement | INewBrandLibraryElement;
    @Output() closeSettings = new EventEmitter();
    @ViewChild('name', { static: true }) name: UIInputComponent;
    @ViewChild('widgetEditor') widgetEditor: WidgetEditorComponent;
    @ViewChild('propertyList') propertyList: ElementRef;
    @ViewChild('propertiesWrapper') propertiesWrapper: ElementRef;
    @ViewChild('saveButton', { static: true }) saveButton: UIButtonComponent;
    @ViewChild('elementContainer') elementContainer: ElementRef;
    @ViewChildren('properties') properties: QueryList<ElementRef<HTMLElement>>;

    AssetPropertyContext = AssetPropertyContext;
    isBfLibraryElement: boolean;
    isAvailableForExport: boolean;
    propertyTypes = [
        'text',
        'number',
        'boolean',
        'select',
        'color',
        'font',
        'image',
        'feed'
    ] as WidgetUnits[];
    movingProperty: UntypedFormGroup;
    movingPropertyClone?: HTMLElement;
    currentMovingElem: HTMLElement;
    movingPropertyEvent: MouseEvent;
    showDetails = true;
    reservedPropertyKeys = reservedPropertyKeys;
    elementHasError: boolean;
    editorLoaded = false;
    propertyGroups: UntypedFormGroup[] = [];
    widgetCustomProperties: IWidgetCustomProperty[];
    fontFamilies: IFontFamily[] = [];
    brandLibrary: Readonly<IBrandLibrary>;
    pendingImageUpload = false;
    uploadedWidgetImageAsset?: ImageLibraryAsset;
    widgetAsset?: IWidgetLibraryAsset;

    nameValidation = new UntypedFormControl('', [Validators.required]);
    saveWithoutClose: boolean;
    codeViewChange = new Subject<File>();
    fileTabs: File[] = [
        new File('HTML', 'html', 'html', true),
        new File('Style', 'css', 'css'),
        new File('JS/TS', 'ts', 'typescript')
    ];
    bannerFlowLibraryWidget?: LibraryWidget;
    bfWidgetLibraryPublishComment: string;
    widgetAssetReference?: IElementProperty;
    selectedFeedName?: string;
    hotkeysExclusions = ['Escape'];

    isImage: boolean;
    isVideo: boolean;
    isWidget: boolean;

    private isEditingName = false;
    private logger = new Logger('EditElementComponent');

    constructor(
        private uiConfirmDialogService: UIConfirmDialogService,
        public designView: DesignViewComponent,
        private uiNotificationService: UINotificationService,
        private formBuilder: FormBuilder,
        private widgetDataService: WidgetDataService,
        private userService: UserService,
        private fileDownloadService: FileDownloadService,
        private mediaLibraryService: MediaLibraryService,
        public editorStateService: EditorStateService,
        private hotkeyBetterService: HotkeyBetterService,
        public colorService: ColorService,
        private mutatorService: MutatorService,
        private widgetAssetService: WidgetAssetService,
        private fontFamiliesService: FontFamiliesService,
        private brandLibraryDataService: BrandLibraryDataService,
        private assetUploadService: AssetUploadService,
        private brandLibraryElementDeletionService: BrandLibraryElementDeletionService,
        private brandLibraryElementEditService: BrandLibraryElementEditService,
        private widgetPropertiesService: WidgetPropertiesService,
        private destroyRef: DestroyRef
    ) {
        this.mutatorService.preview = true;

        this.brandLibraryElementEditService.isEditingName$$
            .pipe(takeUntilDestroyed())
            .subscribe(isEditingElement => {
                this.isEditingName = isEditingElement;
            });

        this.assetUploadService.uploadProgress$.pipe(takeUntilDestroyed()).subscribe(uploadState => {
            if (uploadState.status === 'IN_PROGRESS') {
                this.pendingImageUpload = true;
            }
            if (uploadState.status === 'AFTER_PROGRESS') {
                this.pendingImageUpload = false;
                this.onWidgetThumbnailUploaded(uploadState.newAsset as ImageLibraryAsset);
            }
        });

        this.brandLibraryDataService.brandLibrary$
            .pipe(takeUntilDestroyed())
            .subscribe(brandLibrary => (this.brandLibrary = brandLibrary));
    }

    @HostListener('contextmenu', ['$event']) contextMenu = (event: MouseEvent): void => {
        event.stopPropagation();
    };

    async ngOnInit(): Promise<void> {
        this.fontFamiliesService.fontFamilies$
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(fontFamilies => (this.fontFamilies = fontFamilies));
        await this.brandLibraryDataService.brandLibraryLoaded;

        if (!this.brandLibraryDataService.brandLibrary) {
            throw new Error('Brand library is not initalized yet. How did you get here?');
        }

        this.isWidget = mergeWidgetKind(this.element.type) === ElementKind.Widget;
        this.isImage = this.element.type === ElementKind.Image;
        this.isVideo = this.element.type === ElementKind.Video;
        this.checkIfIsElement();

        this.setupHotkeys();

        this.name.valueChange.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(value => {
            this.nameValidation.setValue(value);
            this.nameValidation.markAsTouched();
            this.brandLibraryElementEditService.validateName({
                generateName: false,
                inputComponent: this.name,
                nameValidation: this.nameValidation,
                brandLibraryElement: this.element
            });
        });

        if (this.isWidget) {
            this.nameValidation.updateValueAndValidity();
            this.widgetAssetReference = this.element.properties.find(
                property => property.name === AssetReference.Widget
            );
            this.widgetAsset = this.brandLibraryDataService.getWidgetAssetByElement(this.element);

            this.widgetCustomProperties = mapElementToCustomWidgetProperties(this.element);
            this.propertyGroups = this.widgetCustomProperties.map(property =>
                this.createFormGroup(property)
            );

            this.widgetPropertiesService.libraryElementUpdate$
                .pipe(takeUntilDestroyed(this.destroyRef))
                .subscribe(customProperties => this.elementChanged(customProperties));

            const bannerflowLibraryWidget = this.element.properties.find(
                prop => prop.name === AssetReference.BannerflowLibraryWidget
            );
            const hasBannerflowLibraryAccess = await this.userService.hasPermission(
                'BannerflowLibrary',
                true
            );

            if (bannerflowLibraryWidget && hasBannerflowLibraryAccess) {
                this.bannerFlowLibraryWidget = await this.widgetDataService.get(
                    bannerflowLibraryWidget.value as string
                );

                setTimeout(() => {
                    if (this.bannerFlowLibraryWidget) {
                        this.isBfLibraryElement =
                            this.bannerFlowLibraryWidget.state === LibraryWidgetState.Active;
                        this.isAvailableForExport = this.bannerFlowLibraryWidget.exportable ?? true;
                    }
                });
            }
        }

        (window as any).exportWidget = this.exportWidget;
        (window as any).importWidget = this.importWidget;
    }

    setupHotkeys = (): void => {
        const context: IHotkeyContext = {
            name: 'EditElement',
            input: window,
            keyDefaultBehaviourExclusions: Object.values(BrowserDefaultHotkeys),
            keyPropogationExclusions: this.hotkeysExclusions
        };
        this.hotkeyBetterService.pushContext(context);
        this.hotkeyBetterService.on('Escape', this.deselectShortcut);
    };

    deselectShortcut = (): void => {
        this.checkPristineState();
    };

    checkPristineState = async (): Promise<void> => {
        if (this.isDirty()) {
            const result: UIConfirmDialogResult = await this.uiConfirmDialogService.confirm({
                headerText: 'Save before closing',
                confirmText: 'Save',
                discardText: "Don't save",
                text: 'Do you want to save changes before closing?',
                showCancelButton: true,
                onConfirm: () => this.save(),
                onConfirmed: (closeDialog: UIConfirmCloseDialogCallback) => {
                    setTimeout(() => {
                        closeDialog('confirm');
                    });
                    if (this.isImage) {
                        this.saveWithoutClose = false;
                    }
                    this.onSaveDone();
                }
            });
            if (result === 'discard') {
                this.close();
            }
        } else {
            this.close();
        }
    };

    isDirty(): boolean {
        const widgetTouched = this.isWidget ? this.widgetEditor.touched : false;
        return this.nameValidation.touched || widgetTouched;
    }

    elementChanged = (props: IWidgetCustomProperty[]): void => {
        this.propertyGroups = props.map(prop => this.createFormGroup(prop));
    };

    private createFormGroup(property: IWidgetCustomProperty): UntypedFormGroup {
        const valueValidators = property.unit === 'select' ? [this.optionsNamesRequired] : [];
        let value = property.value;

        if (property.unit === 'text') {
            if (typeof value === 'object') {
                value = (value as IWidgetText).text;
            } else {
                value = value?.toString() ?? '';
            }
        }
        return this.formBuilder.group({
            name: [
                stripCustomPropertyPrefix(property.name),
                [Validators.required, Validators.minLength(1), this.validateKey]
            ],
            unit: [property.unit, [Validators.required, Validators.minLength(1)]],
            value: [value, valueValidators],
            label: [property.label, [Validators.required, Validators.minLength(1)]],
            expanded: [false]
        });
    }

    private optionsNamesRequired: ValidatorFn = (control: AbstractControl) => {
        return control.value.every(option => option.value.trim().length > 0)
            ? null
            : { emptyOption: true };
    };

    close(updateLibrary?: boolean, elementType?: string): void {
        this.closeSettings.emit({ updateLibrary, elementType });
    }

    onSaveDone = (): void => {
        if (this.elementHasError || this.hasValidationErrors()) {
            return;
        }
        if (!this.saveWithoutClose) {
            this.close(true, this.element.type);
        }
        this.saveWithoutClose = false;
        this.brandLibraryElementDeletionService.brandLibraryUpdated$.next();
        this.mediaLibraryService.closeMediaLibrary(this.isEditingName);
    };

    saveElement = (saveWithoutClose: boolean): void => {
        this.saveWithoutClose = saveWithoutClose;
        this.saveButton.onClick();
    };

    save = async (saveBannerflowLibraryWidget = false): Promise<UISubmitResponse<void>> => {
        this.elementHasError = false;

        const element = cloneDeep(this.element);

        element.properties = element.properties.filter(
            property =>
                (this.isImage && isImageReference(property)) ||
                (this.isVideo && isVideoReference(property)) ||
                (this.isWidget && isBFLibraryWidgetReference(property))
        );

        const nameValidationError = this.brandLibraryElementEditService.validateName({
            generateName: true,
            inputComponent: this.name,
            nameValidation: this.nameValidation,
            brandLibraryElement: element
        });

        if (nameValidationError) {
            const errorMessage =
                nameValidationError === 'duplicate'
                    ? `There is already a ${element.type} with this name.`
                    : `Please give your ${element.type} a name.`;
            this.uiNotificationService.open(errorMessage, { type: 'error', autoCloseDelay: 5000 });
            this.elementHasError = true;
            return Promise.reject();
        }

        element.name = sanitizeString(this.name.value!);

        try {
            if (this.isWidget) {
                await this.saveWidgetAsset(saveBannerflowLibraryWidget, element);
            }

            let brandLibrary: IBrandLibrary;

            if (isNewBrandlibraryElement(element)) {
                brandLibrary = await firstValueFrom(
                    this.brandLibraryDataService.createElement(element)
                );
            } else {
                brandLibrary = await firstValueFrom(
                    this.brandLibraryDataService.updateElement(element)
                );
            }

            const newWidget = brandLibrary.elements.find(el => el.name === element.name);

            if (newWidget) {
                this.element.id = newWidget.id;
            }

            this.brandLibraryDataService.mutateData('widgets', brandLibrary.widgets);
            this.brandLibraryDataService.mutateData('elements', brandLibrary.elements);

            this.brandLibraryElementDeletionService.brandLibraryUpdated$.next();
        } catch (error: unknown) {
            this.logger.error(error);

            const defaultErrorMessage = `There are one or more errors in your code, please fix them before saving the widget. Debug logs with related errors will be shown in your browser's developer console.`;
            const errorMessage =
                error instanceof WidgetEditorError ? error.message : defaultErrorMessage;

            this.uiNotificationService.open(errorMessage, { type: 'error', autoCloseDelay: 5000 });

            return { error };
        }

        return { error: null };
    };

    async delete(): Promise<void> {
        const element = this.element as IBrandLibraryElement;
        const result = await this.brandLibraryElementDeletionService.delete(element);
        if (result === 'confirm') {
            this.close(true, element.type);
        }
    }

    async saveWidgetAsset(
        updateBannerflowLibraryWidget = false,
        element: IBrandLibraryElement | INewBrandLibraryElement
    ): Promise<void> {
        if (this.hasValidationErrors()) {
            this.elementHasError = true;
            return Promise.reject(
                new WidgetEditorError(
                    `There are one or more errors in your widget, please fix them before saving the widget.`
                )
            );
        }

        try {
            const widgetCode = await this.widgetEditor.getWidgetCodeState();

            for (const key in widgetCode) {
                widgetCode[key] = b64EncodeUnicode(widgetCode[key]);
            }

            // Add all custom properties
            this.propertyGroups.forEach(property => {
                let value: string | undefined;
                const unit = property.controls.unit.value as WidgetUnits;

                if ((unit as any) === 'string' || unit === 'text') {
                    value = sanitizeString(property.controls.value.value as string);
                } else {
                    if (unit === 'select') {
                        property.controls.value.value[0].selected = true;
                    }
                    value = serializeWidgetPropertyValue(
                        property.controls.unit.value,
                        property.controls.value.value
                    );
                }

                element.properties.push(
                    createElementProperty({
                        name: applyCustomPropertyPrefix(property.controls.name.value),
                        unit: property.controls.unit.value,
                        value,
                        label: sanitizeString(property.controls.label.value)
                    })
                );
            });

            if (this.pendingImageUpload) {
                await firstValueFrom(
                    this.assetUploadService.uploadProgress$.pipe(
                        filter(uploadState => uploadState.status === 'COMPLETE')
                    )
                );
            }

            // Create or update bannerflow library widget
            if (updateBannerflowLibraryWidget) {
                const mutationType = this.bannerFlowLibraryWidget?.id ? 'update' : 'create';
                await this.createOrUpdateBannerflowLibraryWidget(mutationType, element, widgetCode);
            }

            const upsertedWidgetAsset = await this.upsertWidgetAsset(element, widgetCode);

            if (upsertedWidgetAsset.asset) {
                const property = createElementProperty({
                    name: AssetReference.Widget,
                    unit: 'id',
                    value: upsertedWidgetAsset.asset.id
                });
                this.widgetAssetReference = property;
                element.properties.push(property);
            }

            if (upsertedWidgetAsset.blobUrl) {
                element.properties.push(
                    createElementProperty({
                        name: AssetReference.WidgetContentUrl,
                        unit: 'string',
                        value: upsertedWidgetAsset.blobUrl
                    })
                );
            }

            if (this.bannerFlowLibraryWidget) {
                this.uiNotificationService.open(
                    this.bannerFlowLibraryWidget.state === LibraryWidgetState.Active
                        ? 'Widget is now published to the bannerflow library.'
                        : 'Widget is now unpublished from the bannerflow library.',
                    {
                        type: 'success',
                        autoCloseDelay: 5000
                    }
                );
            }
        } catch (error: unknown) {
            if (!this.elementHasError) {
                this.elementHasError = true;
                return Promise.reject(error);
            }
        }
    }

    private async upsertWidgetAsset(
        element: IBrandLibraryElement | INewBrandLibraryElement,
        widgetCode: WidgetCode
    ): Promise<UpsertWidgetResultDto | (UpdateWidgetDto & { blobUrl: string })> {
        const widgetAsset = this.brandLibrary.widgets.find(
            ({ id }) => id === this.widgetAssetReference?.value
        );
        const imageAsset: CreateWidgetAssetDto = {
            thumbnail: this.uploadedWidgetImageAsset?.thumbnail.url ?? widgetAsset?.thumbnail,
            animatedThumbnail:
                this.uploadedWidgetImageAsset?.animatedThumbnail?.url ?? widgetAsset?.animatedThumbnail
        };

        if (isNewBrandlibraryElement(element)) {
            return await firstValueFrom(
                this.widgetAssetService.createWidgetAsset({
                    asset: imageAsset,
                    data: widgetCode,
                    bannerflowLibraryId: this.bannerFlowLibraryWidget?.id
                })
            );
        } else {
            if (!this.widgetAssetReference) {
                throw new Error('Could not update widget. widgetAssetReference was not set.');
            }

            return await firstValueFrom(
                this.widgetAssetService.updateWidgetAsset({
                    asset: {
                        id: this.widgetAssetReference?.value,
                        ...imageAsset
                    },
                    data: widgetCode,
                    bannerflowLibraryId: this.bannerFlowLibraryWidget?.id
                })
            );
        }
    }

    checkIfIsElement(): boolean {
        const kind = this.element.type;
        return (
            kind === ElementKind.Button ||
            kind === ElementKind.Text ||
            kind === ElementKind.Rectangle ||
            kind === ElementKind.Ellipse
        );
    }

    private onWidgetThumbnailUploaded(imageAsset: ImageLibraryAsset): void {
        this.uploadedWidgetImageAsset = imageAsset;
    }

    fileChanged(file: File): void {
        this.widgetEditor.changeCodeView(file);
    }

    addWidgetProperty(): void {
        const newProperty: IWidgetCustomProperty = {
            name: '',
            value: { text: '' },
            unit: 'text',
            label: ''
        };

        if (this.hasValidationErrors()) {
            return;
        }

        const formGroup = this.createFormGroup(newProperty);
        this.propertyGroups.push(formGroup);
        this.toggleExpand(formGroup, true);
        this.propertyChanged();
    }

    toggleExpand(property: UntypedFormGroup, isNew?: boolean): void {
        const expanded = property.controls.expanded.value;
        if (this.hasValidationErrors() && !isNew) {
            return;
        }
        this.propertyGroups.forEach(prop => prop.controls.expanded.setValue(false));
        property.controls.expanded.setValue(!expanded);
    }

    updateColor(property: UntypedFormGroup, color: IColor): void {
        property.controls.value.setValue(color);
        this.emitPropertyChange();
    }

    onLabelBlur(property: UntypedFormGroup): void {
        property.controls.label.markAsTouched();

        const propertiesLength = this.propertyGroups.length;
        const { value, label, name } = property.controls;

        if (!value.value && label.value && name.pristine && !name.value) {
            name.setValue(
                stringToCamelCase(label.value.replace(/-/gi, ' ').replace(/[^a-zA-Z0-9\s]/gi, ''))
            );
        } else if (!label.value && name.pristine) {
            label.setValue(`Label ${propertiesLength}`);
            name.setValue(`key${propertiesLength}`);
        }

        this.propertyChanged();
    }

    propertyUnitChanged(property: UntypedFormGroup, value: string): void {
        property.controls.unit.setValue(value);

        const unit = property.controls.unit.value as WidgetUnits;

        property.controls.value.clearValidators();
        if (((unit as any) === 'string' || unit === 'text') && typeof property.value !== 'string') {
            property.controls.value.setValue('');
        } else if (unit === 'number' && property.value) {
            property.controls.value.setValue(
                parseFloat(property.value.toString().replace(/\D/gi, '')) || 0
            );
        } else if (unit === 'color' && !(property.value instanceof Color)) {
            property.controls.value.setValue(new Color());
        } else if (unit === 'select' && !Array.isArray(property.value)) {
            property.controls.value.setValue([{ value: '', selected: true }]);
            property.controls.value.addValidators(this.optionsNamesRequired);
        } else if (unit === 'boolean') {
            property.controls.value.setValue(false);
        } else if (unit === 'font') {
            const fontFamily = this.fontFamilies[0] ?? { fontStyles: [] };
            const fontStyle = (fontFamily.fontStyles[0] ?? {}) as Partial<IFontFamilyStyle>;
            const font: IFontStyle = {
                id: fontStyle.id ?? '',
                src: fontStyle.fontUrl ?? '',
                weight: fontStyle.weight ?? 300,
                style: fontStyle.italic ? 'italic' : 'normal',
                fontFamilyId: fontFamily.id ?? ''
            };
            property.controls.value.setValue(font);
        } else if (unit === 'image') {
            property.controls.value.setValue({
                id: '',
                src: ''
            } satisfies IWidgetImage);
        } else if (unit === 'feed') {
            const feed = createFeed('');
            property.controls.value.setValue(feed);
        }

        this.propertyChanged();
    }

    propertyChanged(property?: UntypedFormGroup, newValue?: any): void {
        const { value, unit } = property?.controls || {};

        if (value && newValue !== undefined) {
            if (deepEqual(value.value, newValue)) {
                return;
            }

            if (unit.value === 'text') {
                newValue = sanitizeText(newValue);
            }

            value.setValue(newValue);
        }

        this.hasValidationErrors();
        this.emitPropertyChange();
    }

    emitPropertyChange(): void {
        if (!this.editorLoaded) {
            return;
        }

        const properties: IWidgetCustomProperty[] = this.propertyGroups.map(property => {
            const value =
                (property.controls.unit.value as WidgetUnits) === 'text'
                    ? { text: property.controls.value.value.toString() }
                    : property.controls.value.value;

            return {
                value,
                name: property.controls.name.value,
                unit: property.controls.unit.value as WidgetUnits,
                label: property.controls.label.value
            };
        });

        this.widgetCustomProperties = properties;

        this.widgetEditor.updatePropertyDeclaration(properties);
        this.widgetPropertiesService.changeProperties(properties);
    }

    onSwitchChange(value: boolean, property: UntypedFormGroup): void {
        property.controls.value.setValue(value);
        this.emitPropertyChange();
    }

    selectedFontChanged({ fontFamily, fontStyle }: ISelectedFont, property: UntypedFormGroup): void {
        const font: IFontStyle = {
            id: fontStyle.id,
            src: fontStyle.fontUrl,
            style: fontStyle.italic ? 'italic' : 'normal',
            weight: fontStyle.weight,
            fontFamilyId: fontFamily.id
        };

        property.controls.value.setValue(font);
        this.emitPropertyChange();
    }

    selectedImageChanged(event: IAssetSelectionEvent, property: UntypedFormGroup): void {
        if (event) {
            const imageAsset = event.asset;
            if (imageAsset) {
                const image = {
                    id: imageAsset.id,
                    src: imageAsset.url
                } satisfies IWidgetImage;

                property.controls.value.setValue(image);
                this.emitPropertyChange();
            }
        } else {
            const image = {
                id: '',
                src: ''
            } satisfies IWidgetImage;
            property.controls.value.setValue(image);
            this.emitPropertyChange();
        }
    }

    imageRemoved(property: UntypedFormGroup): void {
        const image = {
            id: '',
            src: ''
        } satisfies IWidgetImage;
        property.controls.value.setValue(image);
        this.emitPropertyChange();
    }

    getImageAssetById(id: string, property: UntypedFormGroup): PartialLibraryAsset<ImageLibraryAsset> {
        // Id might be the string "undefined" when no image is selected
        let image: ImageLibraryAsset | undefined;
        if (id !== 'undefined') {
            image = this.brandLibrary.images.find(_image => _image.id === id);
        }

        if (!image) {
            const { src } = property.controls.value.value as IWidgetImage;
            return {
                thumbnail: { url: src, width: 0, height: 0 },
                original: { url: src, width: 0, height: 0 },
                url: src
            };
        }

        return image;
    }

    feedSelectionChanged(feed: IBfFeed, property: UntypedFormGroup): void {
        (property.controls.value.value as IFeed).id = feed.id;
        this.propertyChanged();
    }

    async deleteProperty(property: UntypedFormGroup): Promise<void> {
        const result: UIConfirmDialogResult = await this.uiConfirmDialogService.confirm({
            headerText: 'Delete custom property',
            text: 'Are you sure you want to delete this custom property?',
            confirmText: 'Yes',
            cancelText: 'No'
        });
        if (result === 'confirm') {
            try {
                this.propertyGroups.splice(this.propertyGroups.indexOf(property), 1);
                this.emitPropertyChange();
            } catch (err) {
                this.logger.error(err);
            }
        }
    }

    duplicateProperty(property: UntypedFormGroup): void {
        if (this.hasValidationErrors()) {
            return;
        }

        const newProperty: IWidgetCustomProperty = {
            name: property.controls.name.value,
            unit: property.controls.unit.value,
            value: property.controls.value.value,
            label: property.controls.label.value
        };
        const formGroup = this.createFormGroup(newProperty);
        formGroup.controls.unit.markAsDirty();
        this.propertyGroups.splice(this.propertyGroups.indexOf(property), 0, formGroup);
        this.emitPropertyChange();
    }

    insertProperty(property: UntypedFormGroup, event: MouseEvent): void {
        event.stopPropagation();
        const widgetProperty = {
            label: property.controls.label.value,
            unit: property.controls.unit.value,
            value: property.controls.value.value,
            name: property.controls.type.value
        } as IWidgetElementProperty;
        this.widgetEditor.insertProperty(widgetProperty);
    }

    toggleDetails(state: boolean): void {
        this.showDetails = state;
        this.widgetEditor.updateCodeSize();
    }

    addSelectOption(property: UntypedFormGroup, propertyIndex: number): void {
        (property.controls.value.value as IWidgetSelectOption[]).push({ value: '', selected: false });
        setTimeout(() => {
            property.controls.value.value[0].selected = true;
            const propertyElemenets = this.properties.toArray();
            const options =
                propertyElemenets[propertyIndex].nativeElement.querySelectorAll('.option input');
            (options[options.length - 1] as HTMLInputElement).focus();
            this.emitPropertyChange();
        });
    }

    deleteSelectOption(property: UntypedFormGroup, index: number): void {
        if (property.controls.unit.value === 'select') {
            if ((property.controls.value.value as IWidgetSelectOption[]).length === 1) {
                property.controls.value.value[0] = { value: '', selected: true };
            } else {
                (property.controls.value.value as IWidgetSelectOption[]).splice(index, 1);
            }
        }
        this.emitPropertyChange();
    }

    selectValueChanged(): void {
        this.emitPropertyChange();
    }

    trackByIndex(index: number): any {
        return index;
    }

    toOptionListName(i: number): string {
        return i + 1 + (i === 0 ? '. (default)' : '.');
    }

    onStartRearrangeProperty(property: UntypedFormGroup, event: MouseEvent): void {
        event.stopPropagation();
        this.movingProperty = property;
        this.movingPropertyEvent = event;

        if (event.button !== 0 || this.properties.length <= 1) {
            return;
        }

        document.addEventListener('mousemove', this.onRearrangeProperty);
        document.addEventListener('mouseup', this.onStopRearrangeProperty);
    }

    onRearrangeProperty = (event: MouseEvent): void => {
        if (Math.abs(event.pageY - this.movingPropertyEvent.pageY) < 5) {
            return;
        }

        this.propertyGroups.forEach(prop => prop.controls.expanded.setValue(false));

        setTimeout(() => {
            const propElems = this.properties.map((propRef: ElementRef) => propRef.nativeElement);
            const currIndex = this.propertyGroups.indexOf(this.movingProperty);
            this.currentMovingElem = propElems[currIndex] as HTMLElement;
            const movingElemRect = this.currentMovingElem.getBoundingClientRect();
            const mouseY = event.pageY - this.movingPropertyEvent.offsetY - 50;

            if (!this.movingPropertyClone) {
                this.movingPropertyClone = this.currentMovingElem.cloneNode(true) as HTMLElement;
                this.propertyList.nativeElement.appendChild(this.movingPropertyClone);
                this.movingPropertyClone.style.position = 'absolute';
                this.movingPropertyClone.style.left = '0px';
                this.movingPropertyClone.style.zIndex = '999';
                this.movingPropertyClone.style.top = `${mouseY}px`;
                this.currentMovingElem.style.opacity = '0';
                this.movingPropertyClone.classList.add('rearrange');
            }

            const listTopOffset = this.propertyList.nativeElement.getBoundingClientRect().top;
            const listHeight = this.propertiesWrapper.nativeElement.getBoundingClientRect().height;

            const lastProp = propElems[propElems.length - 1];

            if (
                mouseY > listTopOffset - 25 &&
                mouseY < listHeight + listTopOffset - movingElemRect.height - 15
            ) {
                this.movingPropertyClone.style.top = `${mouseY}px`;
                this.movingPropertyClone.style.left = '0px';
            }

            const nextElem = propElems[currIndex + 1] as HTMLElement;
            const prevElem = propElems[currIndex - 1] as HTMLElement;

            // Move it down (increased index)
            if (nextElem && mouseY > movingElemRect.top - nextElem.getBoundingClientRect().height + 5) {
                moveItemInArray(this.propertyGroups, currIndex, currIndex + 1);
                this.emitPropertyChange();
            }
            // Move it up (decreased index)
            else if (
                prevElem &&
                prevElem !== lastProp &&
                mouseY <
                    prevElem.getBoundingClientRect().top - prevElem.getBoundingClientRect().height * 1.5
            ) {
                moveItemInArray(this.propertyGroups, currIndex, currIndex - 1);
                this.emitPropertyChange();
            }
        });
    };

    onStopRearrangeProperty = (): void => {
        if (this.movingPropertyClone) {
            this.propertyList.nativeElement.removeChild(this.movingPropertyClone);
        }
        if (this.currentMovingElem) {
            this.properties.forEach(
                (propRef: ElementRef) => (propRef.nativeElement.style.opacity = '')
            );
            this.movingPropertyClone = undefined;
        }
        document.removeEventListener('mousemove', this.onRearrangeProperty);
        document.removeEventListener('mouseup', this.onStopRearrangeProperty);
    };

    validateKey = (control: UntypedFormControl): any => {
        const keys: string[] = [];
        this.propertyGroups.forEach(property => {
            if (property.controls.name !== control) {
                keys.push(property.value.name);
            }
        });
        const validation = { keyIsReserved: false, keyIsDuplicate: false, keyIsInvalid: false };

        if (this.reservedPropertyKeys.includes(control.value)) {
            validation.keyIsReserved = true;
        }
        if (keys.includes(control.value)) {
            validation.keyIsDuplicate = true;
        }

        if (!control.value.match(/\b([a-zA-Z][^\s-]*)/gi) || !control.value.match(/^[A-Za-z0-9]*$/gi)) {
            validation.keyIsInvalid = true;
        }

        if (validation.keyIsDuplicate || validation.keyIsReserved || validation.keyIsInvalid) {
            return validation;
        }

        return null;
    };

    propertyErrorMessage(property: UntypedFormGroup): string {
        if (property.errors) {
            if (property.errors.keyIsReserved) {
                return 'This is a reserved key, please choose a different one.';
            } else if (property.errors.keyIsDuplicate) {
                return 'All keys must be unique.';
            } else if (property.errors.keyIsInvalid) {
                return 'This is an invalid keyname. It must start with a letter and may only contain a-Z and 0-9.';
            }
        }
        return '';
    }

    /**
     * Returns true if there are validation errors
     */
    hasValidationErrors(): boolean {
        let isInvalid = false;

        // Trigger validations on all groups and controls
        this.propertyGroups.forEach(formGroup => {
            formGroup.updateValueAndValidity();
            Object.keys(formGroup.controls).forEach(field => {
                const control = formGroup.get(field);
                if (control instanceof AbstractControl) {
                    control.updateValueAndValidity();
                    if (control.invalid) {
                        const errors = control.errors;
                        control.setErrors({ errorFlash: true, ...errors });
                        setTimeout(() => control.setErrors(errors), 300);
                    }
                }
            });
            if (formGroup.invalid) {
                isInvalid = true;
            }
        });

        return isInvalid;
    }

    onEditorLoaded(): void {
        this.editorLoaded = true;
        this.emitPropertyChange();
    }

    async createOrUpdateBannerflowLibraryWidget(
        type: 'create' | 'update',
        element: IBrandLibraryElement | INewBrandLibraryElement,
        widgetCode: WidgetCode
    ): Promise<void> {
        const thumbnailUrl =
            this.uploadedWidgetImageAsset?.thumbnail.url ?? this.widgetAsset?.thumbnail;

        if (!thumbnailUrl) {
            const errorMessage =
                'Please give your widget a thumbnail before publishing it to the bannerflow library.';
            this.uiNotificationService.open(errorMessage, {
                type: 'error',
                autoCloseDelay: 5000
            });
            throw new WidgetEditorError(errorMessage);
        }

        const codeProperties: IElementProperty[] = [];

        for (const key in widgetCode) {
            codeProperties.push(
                createElementProperty({
                    name: key,
                    unit: 'string',
                    value: widgetCode[key]
                })
            );
        }

        try {
            if (type === 'create') {
                const properties = element.properties
                    .filter(prop => prop.name !== AssetReference.Widget)
                    .map(({ id, clientId, name, value, label, unit }) => ({
                        id,
                        clientId,
                        value,
                        label,
                        name,
                        unit
                    }));

                const widget: LibraryWidget = {
                    name: element.name,
                    comment: 'Publish to store',
                    thumbnailUrl,
                    exportable: true,
                    properties: [...properties, ...codeProperties]
                };

                this.bannerFlowLibraryWidget = await this.widgetDataService.post(widget);

                element.properties.push(
                    createElementProperty({
                        name: AssetReference.BannerflowLibraryWidget,
                        unit: 'id',
                        value: this.bannerFlowLibraryWidget.id
                    })
                );

                setTimeout(() => {
                    this.isBfLibraryElement = true;
                    this.isAvailableForExport = widget.exportable;
                });
                return;
            }
        } catch (e) {
            this.logger.error(e);
            throw new WidgetEditorError('Could not create bannerflow library widget.');
        }

        try {
            if (type === 'update' && this.bannerFlowLibraryWidget) {
                const properties = element.properties
                    .filter(prop => !isWidgetReference(prop) && !isBFLibraryWidgetReference(prop))
                    .map(({ id, clientId, value, name, unit, label }) => ({
                        id,
                        clientId,
                        value,
                        label,
                        name,
                        unit
                    }));

                const widget: LibraryWidget = {
                    name: element.name,
                    comment: this.bfWidgetLibraryPublishComment || 'Updated widget',
                    thumbnailUrl: thumbnailUrl,
                    exportable: this.isAvailableForExport,
                    properties: [...properties, ...codeProperties],
                    state: this.isBfLibraryElement
                        ? LibraryWidgetState.Active
                        : LibraryWidgetState.Inactive,
                    id: this.bannerFlowLibraryWidget?.id
                };

                let result: UIConfirmDialogResult = 'confirm';
                if (this.bannerFlowLibraryWidget.state !== widget.state) {
                    const isInactive =
                        this.bannerFlowLibraryWidget.state === LibraryWidgetState.Inactive;

                    const headerText = isInactive
                        ? 'Publish widget to Bannerflow library'
                        : 'Unpublish widget from Bannerflow library';
                    const text = isInactive
                        ? `Are you sure you want to make this widget available the in public Bannerflow library?`
                        : `Are you sure you want to make this widget unavailable the in public Bannerflow library?`;

                    result = await this.uiConfirmDialogService.confirm({
                        headerText,
                        text,
                        confirmText: 'Yes',
                        cancelText: 'No'
                    });
                }

                if (result === 'confirm') {
                    let text =
                        this.bannerFlowLibraryWidget.state === LibraryWidgetState.Active
                            ? 'Widget is now available in the bannerflow widget library.'
                            : 'Widget is now hidden from the bannerflow widget library.';

                    if (this.bannerFlowLibraryWidget.state === widget.state) {
                        text = 'Widget is now updated in the bannerflow widget library.';
                    }

                    this.bannerFlowLibraryWidget = await this.widgetDataService.put(widget);

                    // Avoid duplicate references
                    element.properties = element.properties
                        .filter(property => !isWidgetCodeProperty(property))
                        .filter(prop => !isBFLibraryWidgetReference(prop));

                    element.properties.push(
                        createElementProperty({
                            name: AssetReference.BannerflowLibraryWidget,
                            unit: 'id',
                            value: this.bannerFlowLibraryWidget.id
                        })
                    );

                    this.uiNotificationService.open(text, {
                        autoCloseDelay: 5000,
                        type: 'success'
                    });
                }
            }
        } catch (e) {
            this.logger.error(e);
            throw new WidgetEditorError('Could not update bannerflow library widget.');
        }

        this.bfWidgetLibraryPublishComment = '';
        this.isBfLibraryElement = this.bannerFlowLibraryWidget?.state === LibraryWidgetState.Active;
    }

    async publishBannerflowLibraryWidget(): Promise<void> {
        const result: UIConfirmDialogResult = await this.uiConfirmDialogService.confirm({
            headerText: `${
                this.bannerFlowLibraryWidget ? 'Update' : 'Publish'
            } widget to bannerflow library`,
            text: `Are you sure you want to ${
                this.bannerFlowLibraryWidget ? 'update' : 'publish'
            } this widget to the bannerflow library?`,
            confirmText: 'Yes',
            cancelText: 'No'
        });

        if (result === 'confirm') {
            this.save(true);
        }
    }

    exportWidget = (): void => {
        const exportManifest: IWidgetManifest = {
            name: this.element.name,
            code: {
                ts: this.widgetEditor.editorViews.ts.model!.getLinesContent().join('\n'),
                html: this.widgetEditor.editorViews.html.model!.getLinesContent().join('\n'),
                css: this.widgetEditor.editorViews.css.model!.getLinesContent().join('\n')
            },
            properties: this.propertyGroups.map(property => {
                const unit = property.controls.unit.value as WidgetUnits;
                if (unit === 'feed') {
                    (property.value.value as IFeed).id = '';
                    (property.value.value as IFeed).path = '';
                } else if (unit === 'font' || unit === 'image') {
                    property.value.value = '';
                }
                delete property.value.expanded;
                return property.value;
            })
        };
        this.fileDownloadService.dataToFile(exportManifest, this.element.name);
    };

    importWidget = (manifest: IWidgetManifest): void => {
        if (!manifest || typeof manifest !== 'object') {
            throw new Error(
                'Please provide the manifest (as raw json) of your widget you received when you exported it.'
            );
        }

        this.propertyGroups = [];
        manifest.properties.forEach(property => {
            const newProperty: IWidgetCustomProperty = {
                value: property.value,
                name: property.name,
                unit: property.unit,
                label: property.label
            };

            if (newProperty.unit === 'text') {
                newProperty.value =
                    typeof newProperty.value === 'string'
                        ? { text: newProperty.value ?? '' }
                        : newProperty.value;
            }

            if (newProperty.unit === 'color' && newProperty.value) {
                newProperty.value =
                    typeof newProperty.value === 'string'
                        ? parseColor(newProperty.value)
                        : new Color(newProperty.value as IColor);
                if (!newProperty.value.type) {
                    (newProperty.value as IColor).type = ColorType.Solid;
                }
            }

            if (newProperty.unit === 'font') {
                const fontFamily =
                    typeof newProperty.value === 'object'
                        ? this.fontFamilies.find(
                              ({ id }) => id === (newProperty.value as IFontStyle).fontFamilyId
                          )
                        : undefined;

                const firstFontFamily = this.fontFamilies?.[0];
                let firstFontStyle: IFontFamilyStyle;
                let backupFont: IFontStyle = {} as IFontStyle;

                if (firstFontFamily) {
                    firstFontStyle = firstFontFamily.fontStyles[0];
                    backupFont = {
                        fontFamilyId: firstFontFamily.id,
                        id: firstFontStyle.id,
                        src: firstFontStyle.fontUrl,
                        style: firstFontStyle.italic ? 'italic' : 'normal',
                        weight: firstFontStyle.weight
                    };
                } else {
                    backupFont = {} as IFontStyle;
                }

                if (!fontFamily) {
                    newProperty.value = backupFont;
                } else {
                    const fontStyle = fontFamily.fontStyles.find(
                        ({ id }) => id === (newProperty.value as IFontStyle).id
                    );
                    if (!fontStyle) {
                        newProperty.value = backupFont;
                    }
                }
            }

            if (newProperty.unit === 'feed') {
                (newProperty.value as IFeed).id = '';
                (newProperty.value as IFeed).path = '';
            }

            if (newProperty.unit === 'image') {
                newProperty.value = '';
            }

            const formGroup = this.createFormGroup(newProperty);
            this.propertyGroups.push(formGroup);
        });

        this.element.name = manifest.name;

        this.propertyChanged();
        this.widgetEditor.setCodeModels(manifest.code);
    };

    ngOnDestroy(): void {
        this.hotkeyBetterService.off('Escape', this.deselectShortcut);
        this.hotkeyBetterService.popContext();
    }
}

interface IWidgetManifest {
    name: string;
    code: {
        ts: string;
        html: string;
        css: string;
    };
    properties: IWidgetElementProperty[];
}

const reservedPropertyKeys: (OneOfElementPropertyKeys | 'ts')[] = [
    'font',
    'feed',
    'fill',
    'name',
    'parentNodeId',
    'time',
    'duration',
    'radius',
    'mirrorX',
    'mirrorY',
    'opacity',
    'originX',
    'originY',
    'rotationX',
    'rotationY',
    'rotationZ',
    'scaleX',
    'scaleY',
    'filters',
    'states',
    'actions',
    'animations',
    'border',
    'shadows',
    'masking',
    'ratio',
    'locked',
    'hidden',
    'width',
    'height',
    'x',
    'y',
    'content',
    'characterStyles',
    'textColor',
    'textShadows',
    'uppercase',
    'underline',
    'strikethrough',
    'horizontalAlignment',
    'verticalAlignment',
    'lineHeight',
    'characterSpacing',
    'fontSize',
    'maxRows',
    'padding',
    'textOverflow',
    'imageAsset',
    'imageSettings',
    'widgetReference',
    'bannerflowLibraryWidgetReference',
    'html',
    'js',
    'css',
    'ts',
    'customProperties',
    'videoSettings',
    'videoAsset',
    'checksum'
] as const;
