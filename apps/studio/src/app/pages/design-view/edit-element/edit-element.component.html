<!-- <PERSON><PERSON>r top -->
<ui-header
    [full]="true"
    [showLogo]="false">
    @if (element) {
        <edit-element-topbar
            [element]="element"
            (fileTabChanged)="fileChanged($event)"
            (deleteElement)="delete()"></edit-element-topbar>
    }
</ui-header>
<div
    class="left ui-scrollbar"
    data-test-id="edit-element"
    [style.display]="showDetails ? '' : 'none'">
    @if (isWidget) {
        <widget-thumbnail-upload [element]="element"> </widget-thumbnail-upload>
    }

    <div class="details">
        <ui-input
            label="Name"
            [id]="'widget-name'"
            #name
            [value]="element.name"
            placeholder="Widget name"
            [validation]="nameValidation"
            [class.errorFlash]="nameValidation.errors?.errorFlash"
            [maxlength]="250"
            autocomplete="off"></ui-input>

        <ng-container *permissions="'BannerflowLibrary'">
            @if (isWidget) {
                <div class="bannerflowLibrarySettings">
                    <div class="heading">Bannerflow library:</div>
                    @if (bannerFlowLibraryWidget) {
                        <div class="row">
                            <div>Published:</div>
                            <ui-toggle-switch [(selected)]="isBfLibraryElement"></ui-toggle-switch>
                        </div>
                        <div class="row">
                            <div>Available for export:</div>
                            <ui-toggle-switch [(selected)]="isAvailableForExport"></ui-toggle-switch>
                        </div>
                    }
                    @if (!bannerFlowLibraryWidget) {
                        <div class="row">
                            <ui-button
                                type="primary"
                                text="Publish to bannerflow library"
                                style="width: 100%"
                                (click)="publishBannerflowLibraryWidget()"></ui-button>
                        </div>
                    }
                    @if (bannerFlowLibraryWidget) {
                        <div class="row publish-comment">
                            Comment:
                            <ui-input [(value)]="bfWidgetLibraryPublishComment"></ui-input>
                        </div>
                        <div class="row">
                            <ui-button
                                type="primary"
                                text="Update"
                                (click)="publishBannerflowLibraryWidget()"></ui-button>
                        </div>
                    }
                </div>
            }
        </ng-container>
    </div>

    <!-- Widget properties -->
    @if (isWidget) {
        <div
            class="widget-properties"
            ui-theme="tiny"
            #propertyList>
            <div class="add-property">
                Custom properties
                <ui-svg-icon
                    icon="plus"
                    [id]="'widget-add-btn'"
                    (click)="addWidgetProperty()"></ui-svg-icon>
            </div>
            <div
                class="property-sections"
                #propertiesWrapper>
                @for (property of propertyGroups; track property; let propIndex = $index) {
                    <div
                        class="property-section"
                        [formGroup]="propertyGroups[propIndex]"
                        [class.invalid]="property.invalid && property.controls.name.dirty"
                        #properties>
                        <div
                            class="property-details"
                            (click)="toggleExpand(property)"
                            (mousedown)="onStartRearrangeProperty(property, $event)">
                            <ui-svg-icon
                                icon="rearrange"
                                class="rearrange-icon"></ui-svg-icon>
                            <div class="property-label">{{ property.value.label }}</div>
                            <div style="display: flex">
                                <div class="property-type">{{ property.value.unit }}</div>
                                @if (property.value.type) {
                                    <div
                                        class="insert-property"
                                        uiTooltip="Insert property"
                                        (click)="insertProperty(property, $event)">
                                        &#64;{{ property.value.type }}
                                    </div>
                                }
                            </div>
                        </div>
                        <section-expand
                            [expanded]="property.controls.expanded.value"
                            arrowPosition="18%">
                            <div class="property-group">
                                <div class="property">
                                    <div class="property-label">Label</div>
                                    <ui-input
                                        [value]="property.controls.label.value"
                                        formControlName="label"
                                        [validation]="$any(property).controls.label"
                                        (blur)="onLabelBlur(property)"
                                        [autofocus]="true"
                                        placeholder="Property label"
                                        [class.errorFlash]="property.controls.label.errors?.errorFlash"
                                        (valueChange)="propertyChanged()"
                                        autocomplete="off"></ui-input>
                                </div>
                                <div class="property">
                                    <div class="property-label">Key</div>
                                    <ui-input
                                        [value]="property.controls.name.value"
                                        formControlName="name"
                                        placeholder="key"
                                        allowedChars="([a-zA-Z0-9])"
                                        [uiTooltip]="
                                            property.invalid ? propertyErrorMessage(property) : ''
                                        "
                                        [uiTooltipDisabled]="
                                            !property.invalid || !propertyErrorMessage(property).length
                                        "
                                        [uiTooltipWidth]="200"
                                        [validation]="$any(property).controls.label"
                                        (valueChange)="propertyChanged()"
                                        [class.errorFlash]="property.controls.name.errors?.errorFlash"
                                        autocomplete="off">
                                    </ui-input>
                                </div>
                                <div class="property type">
                                    <div class="property-label">Type</div>
                                    <ui-select
                                        [useTargetWidth]="true"
                                        width="140"
                                        [selected]="property.controls.unit.value"
                                        (selectedChange)="propertyUnitChanged(property, $event)">
                                        @for (type of propertyTypes; track $index) {
                                            <ui-option [value]="type">
                                                {{ (type === 'select' ? ' list' : type) | titlecase }}
                                            </ui-option>
                                        }
                                    </ui-select>
                                </div>
                                <div class="property default-value">
                                    @if (
                                        property.controls.unit.value !== 'select' &&
                                        property.controls.unit.value !== 'image'
                                    ) {
                                        <div
                                            class="property-label"
                                            [class.alignTop]="property.controls.unit.value === 'font'">
                                            Default
                                        </div>
                                    }

                                    @if (property.controls.unit.value === 'text') {
                                        <ui-textarea
                                            [value]="property.controls.value.value"
                                            (valueChange)="propertyChanged(property, $event)"
                                            [maxCharacters]="3000"
                                            autocomplete="off">
                                        </ui-textarea>
                                    }

                                    @if (property.controls.unit.value === 'number') {
                                        <ui-number-input
                                            [value]="property.controls.value.value"
                                            (valueChange)="propertyChanged(property, $event)"
                                            [allowEmpty]="false"
                                            autocomplete="off"
                                            style="padding-left: 7rem"></ui-number-input>
                                    }

                                    @if (property.controls.unit.value === 'boolean') {
                                        <ui-toggle-switch
                                            [selected]="property.controls.value.value"
                                            (selectedChange)="onSwitchChange($event, property)">
                                        </ui-toggle-switch>
                                    }

                                    @if (property.controls.unit.value === 'color') {
                                        <color-button
                                            data-test-id="color-button"
                                            [id]="'edit-element-color-button-' + propIndex"
                                            [color]="property.controls.value.value"
                                            (click)="
                                                colorService.toggleColorPicker(
                                                    'editElementColor' + propIndex
                                                )
                                            ">
                                        </color-button>
                                    }

                                    @if (property.controls.unit.value === 'font') {
                                        <div class="select-wrapper margin-bottom">
                                            <font-picker
                                                [selectedFontFamilyId]="
                                                    property.controls.value.value.fontFamilyId
                                                "
                                                [selectedFontStyleId]="property.controls.value.value.id"
                                                [labels]="false"
                                                (selectedFontChange)="
                                                    selectedFontChanged($event, property)
                                                "></font-picker>
                                        </div>
                                    }

                                    @if (property.controls.unit.value === 'select') {
                                        <div class="select">
                                            @for (
                                                value of property.controls.value.value;
                                                track trackByIndex($index);
                                                let i = $index
                                            ) {
                                                <div class="option">
                                                    <span>{{ toOptionListName(i) }}</span>
                                                    <ui-input
                                                        autocomplete="off"
                                                        [(value)]="
                                                            property.controls.value.value[i].value
                                                        "
                                                        (keyup.enter)="
                                                            addSelectOption(property, propIndex)
                                                        "
                                                        (valueChange)="selectValueChanged()"></ui-input>
                                                    @if (property.controls.value.value.length > 1) {
                                                        <ui-svg-icon
                                                            icon="delete"
                                                            (click)="
                                                                deleteSelectOption(property, i)
                                                            "></ui-svg-icon>
                                                    }
                                                </div>
                                            }
                                            <div class="new-option">
                                                <div
                                                    (click)="addSelectOption(property, propIndex)"
                                                    style="display: flex">
                                                    <ui-svg-icon icon="plus-small"></ui-svg-icon> Add
                                                    option
                                                </div>
                                            </div>
                                        </div>
                                    }

                                    @if (property.controls.unit.value === 'image') {
                                        <asset-property
                                            class="asset-picker"
                                            [context]="AssetPropertyContext.Widget"
                                            label="Default"
                                            [allowUpload]="false"
                                            [asset]="
                                                getImageAssetById(
                                                    property.controls.value.value.id,
                                                    property
                                                )
                                            "
                                            (assetSelected)="selectedImageChanged($event, property)"
                                            (assetRemoved)="imageRemoved(property)">
                                        </asset-property>
                                    }

                                    @if (property.controls.unit.value === 'feed') {
                                        <div class="feed-wrapper">
                                            <feed-picker
                                                [value]="property.controls.value.value"
                                                (feedSelectionChanged)="
                                                    feedSelectionChanged($event, property)
                                                "></feed-picker>
                                        </div>
                                    }
                                </div>
                                <div class="color-picker">
                                    @if (
                                        property.controls.unit.value === 'color' &&
                                        ('editElementColor' + propIndex | picker | async)
                                    ) {
                                        <section-expand
                                            arrowPosition="206px"
                                            [showBackground]="true"
                                            [removeContentWhenCollapsed]="false"
                                            [expanded]="true">
                                            <color-section
                                                [name]="'editElementColor' + propIndex"
                                                [preventCloseElements]="['.widget-properties']"
                                                [color]="property.controls.value.value"
                                                (onColorChanged)="updateColor(property, $event)">
                                            </color-section>
                                        </section-expand>
                                    }
                                </div>
                                <div class="property-buttons">
                                    <ui-button
                                        text="Duplicate"
                                        (click)="duplicateProperty(property)"></ui-button>
                                    <ui-button
                                        text="Remove"
                                        icon="delete"
                                        (click)="deleteProperty(property)"></ui-button>
                                </div>
                            </div>
                        </section-expand>
                    </div>
                }
            </div>
        </div>
    }
    <div class="buttons">
        <ui-button
            text="Cancel"
            (click)="checkPristineState()"></ui-button>
        <ui-button
            text="Save"
            [id]="'widget-save-btn'"
            #saveButton
            [submit]="save"
            [done]="onSaveDone"
            type="primary"></ui-button>
        <br />
    </div>
</div>
<div class="center">
    @if (isImage || isVideo) {
        <edit-element-preview [element]="element"></edit-element-preview>
    }

    @if (isWidget) {
        <widget-editor
            #widgetEditor
            [element]="element"
            [customProperties]="widgetCustomProperties"
            (editorLoaded)="onEditorLoaded()"
            (saveElement)="saveElement($event)"
            (toggleDetailsPanel)="toggleDetails($event)">
        </widget-editor>
    }
</div>
