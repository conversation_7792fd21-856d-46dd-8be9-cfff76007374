:host {
    position: absolute;
    left: 0;
    right: 0;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-image: linear-gradient(
            90deg,
            var(--studio-color-transparent-white-95),
            var(--studio-color-transparent-white-95)
        ),
        linear-gradient(90deg, var(--studio-color-black) 50%, var(--studio-color-background-second) 0),
        linear-gradient(180deg, var(--studio-color-black) 50%, var(--studio-color-background-second) 0);
    background-blend-mode: normal, difference, normal;
    background-size: 2em 2em;
}

div {
    max-width: 70%;
    max-height: 70%;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
}

.video-wrapper {
    display: flex;
    height: 100%;
}

video {
    width: 100%;
    height: 100%;
}
