:host {
    position: relative;
    display: flex;
    min-width: 100px;
    height: 100%;
    flex-wrap: nowrap;
    user-select: none;
    background: var(--studio-color-surface-second);
}

.active-dropdown-item {
    background-color: var(--studio-color-surface-selected);
}

.edit-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.1rem 2rem 0;
    width: 25rem;
    flex-shrink: 0;
    box-shadow: 0.1rem 0 0 0 var(--studio-color-border-second);

    .more-menu {
        cursor: pointer;
    }

    .logo {
        height: 19px;
        display: flex;
        align-items: center;

        ui-logo {
            display: inline-block;
            width: 18px;
            height: 100%;
            vertical-align: middle;
        }

        p {
            margin-left: 2.7rem;
        }
    }
}

.left {
    color: var(--studio-color-text-second);
    align-items: center;

    .tabs {
        display: flex;
        height: 100%;

        .tab {
            height: 100%;
            padding: 2rem 2.5rem;
            cursor: pointer;
            position: relative;

            &.selected {
                color: var(--studio-color-text);

                &:after {
                    display: block;
                    content: '';
                    height: 2px;
                    width: 100%;
                    position: absolute;
                    bottom: -1px;
                    left: 0;
                    background-color: var(--studio-color-active);
                }
            }
        }
    }
}

.widget-topbar {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.center {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: center;
    height: 100%;
    flex-grow: 1;
}

.buttons {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: 20px;
    min-width: 220px;

    ui-button {
        margin-left: 8px;
    }

    .exit {
        display: inline-block;
        font-size: 3rem;
        line-height: 4rem;
        vertical-align: top;
        padding: 0 0rem 0 1rem;
        color: var(--studio-color-text-discrete);
        outline: none;

        &:focus {
            outline: none;
        }

        &:hover {
            color: var(--studio-color-text);
        }
    }
}

.media-asset-details {
    display: flex;
    padding: 0 3rem;
    align-items: center;
    font-size: 1.2rem;
    justify-content: space-between;
    width: 100%;
    color: var(--studio-color-grey-dark);

    .right {
        justify-self: flex-end;
        display: flex;

        .name {
            margin-right: 1.5rem;
            color: var(--studio-color-primary);
            cursor: pointer;
        }
    }
}
