:host {
    display: flex;
    height: 100%;
    width: 100%;
}

.ui-header {
    top: -5rem;
    z-index: 102;
}

.element-container {
    display: block;
}

.left {
    width: 25rem;
    background: var(--studio-color-surface-second);
    box-shadow: 0.1rem 0 0 0 var(--studio-color-border-second);
    flex-direction: column;
    height: 100%;
    z-index: 102;
    overflow-y: auto;
    position: relative;
}

.center {
    flex: 1;
    position: relative;
    min-width: 0;
}

.right {
    background-color: var(--studio-color-surface);
    box-shadow: -0.1rem 0 0 0 var(--studio-color-border-second);
    width: 22rem;
    flex-shrink: 0;
    z-index: 1;
}

.details {
    padding: 1rem 1.3rem;

    .bannerflowLibrarySettings {
        margin-bottom: 1rem;

        .ui-toggle-switch {
            margin-top: 0.5rem;
        }

        .heading {
            margin-top: 2rem;
            font-weight: bold;
        }

        .row {
            margin-top: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
    }

    .meta {
        display: flex;
        justify-content: space-between;
        margin-bottom: 3.7rem;
        color: var(--studio-color-text-secondary);
    }
}

.buttons {
    padding: 1rem 1.3rem;
    margin-top: 2.5rem;
    display: flex;
    justify-content: space-around;
}

.widget-properties {
    margin-top: 2.4rem;

    .add-property {
        display: flex;
        justify-content: space-between;
        padding: 0 1.3rem;
        margin-bottom: 1.6rem;

        ui-svg-icon {
            cursor: pointer;
            color: var(--studio-color-text-second);

            &:hover {
                color: var(--studio-color-text);
            }
        }
    }

    .property-section {
        display: flex;
        justify-content: center;
        flex-direction: column;
        width: 100%;
        transition: all 0.1s;

        &:hover {
            background: var(--studio-color-background);

            .rearrange-icon {
                display: block;
            }
        }

        &.rearrange {
            box-shadow: var(--default-box-shadow);
            background: var(--studio-color-surface) !important;

            .property-details {
                .rearrange-icon {
                    display: block;
                    color: var(--studio-color-grey-dark);
                }
            }
        }

        .rearrange-icon {
            position: absolute;
            left: 6px;
            top: 6px;
            display: none;
            color: var(--studio-color-grey);
            font-size: 1.4rem;
        }

        .property-details {
            display: flex;
            justify-content: space-between;
            padding: 0.6rem 1.6rem 0.6rem 2.4rem;
            cursor: pointer;
            position: relative;

            .property-label {
                overflow-x: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .property-type {
                color: var(--studio-color-grey-84);
                margin-right: 0.7rem;
            }

            .insert-property {
                color: var(--studio-color-primary);
                cursor: pointer;
                max-width: 85px;
                overflow-x: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .color-picker {
            color-section {
                display: block;
                padding: 1rem 2.5rem 2rem 2.5rem;
            }
        }

        .asset-picker {
            justify-content: end;
        }

        .property-group {
            padding: 2rem 0;
            position: relative;

            .property {
                display: flex;
                justify-content: space-between;
                margin-bottom: 1rem;
                padding: 0 1.6rem 0 2.2rem;

                &:last-child {
                    margin-bottom: 2rem;
                }

                .input,
                .ui-select,
                .select-wrapper {
                    width: 14rem;
                }

                .ui-select {
                    ::ng-deep {
                        .button {
                            min-width: 0;
                        }
                    }
                }

                &-label {
                    line-height: 1;
                    display: flex;
                    align-items: center;

                    &.alignTop {
                        display: block;
                        padding-top: 4px;
                    }
                }

                .select {
                    display: flex;
                    flex-direction: column;

                    .option {
                        display: flex;
                        align-items: center;
                        margin-bottom: 1rem;

                        > span {
                            width: 7.2rem;
                            text-align: right;
                            padding-right: 1rem;
                            color: var(--studio-color-text-secondary);
                        }

                        .input {
                            width: 11.2rem;
                        }

                        ui-svg-icon {
                            margin-left: 0.8rem;
                            font-size: 1.4rem;
                            color: var(--studio-color-text-secondary);
                            cursor: pointer;
                        }
                    }

                    .new-option {
                        justify-content: flex-end;
                        display: flex;
                        color: var(--studio-color-primary);
                        margin-top: 0.5rem;
                        margin-bottom: 0.75rem;

                        > div {
                            cursor: pointer;
                        }

                        ui-svg-icon {
                            margin-right: 3px;
                        }
                    }
                }
            }

            .property-buttons {
                display: flex;
                justify-content: space-between;
                margin-top: 2rem;
                padding: 0 1.6rem 0 2.2rem;
            }
        }
    }
}

.input {
    &.errorFlash {
        &::ng-deep .input {
            transition: background 0.1s;
            background: var(--studio-color-red-light);
        }
    }
}
