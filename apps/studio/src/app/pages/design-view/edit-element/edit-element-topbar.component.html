<div class="edit-header">
    <div class="logo">
        <ui-logo [small]="true"></ui-logo>
        <p>Edit {{ element.type === 'bannerflowLibraryWidget' ? 'Library Widget' : element.type }}</p>
    </div>
    <div>
        <ui-svg-icon
            icon="kebab"
            class="more-menu"
            [uiDropdownTarget]="moreMenu"></ui-svg-icon>
        <ui-dropdown
            #moreMenu
            style="display: none">
            <ui-dropdown-item
                id="edit-element-delete"
                (click)="delete()"
                >Delete</ui-dropdown-item
            >
        </ui-dropdown>
    </div>
</div>

<!-- For media assets -->
@if (mediaAsset && isMediaElement()) {
    <div class="media-asset-details">
        <div class="size">{{ mediaAsset.width }}x{{ mediaAsset.height }}</div>
        <div class="right">
            <div
                id="edit-element-media-download"
                class="name"
                (click)="downloadMedia()">
                {{ mediaAsset.name }}
            </div>
            <div>{{ mediaAsset.fileSize | uiFormatBytes }}</div>
        </div>
    </div>
}

<!-- For widgets -->
@if (isWidget()) {
    <div class="widget-topbar">
        <div class="left">
            <div
                id="edit-element-widget-change-tab"
                class="tabs">
                @for (file of editElement.fileTabs; track file.id) {
                    <div
                        class="tab"
                        [class.selected]="file.selected"
                        (click)="changeTab(file)">
                        {{ file.name }}
                    </div>
                }
            </div>
        </div>
    </div>
}
