import { animate, state, style, transition, trigger } from '@angular/animations';
import {
    Component,
    EventEmitter,
    forwardRef,
    Inject,
    Input,
    OnDestroy,
    OnInit,
    Output,
    ViewChild
} from '@angular/core';
import { UIButtonComponent } from '@bannerflow/ui';
import { isImageElement, isVideoElement, mergeWidgetKind } from '@creative/nodes/helpers';
import {
    IBrandLibraryElement,
    INewBrandLibraryElement,
    OneOfLibraryAssets
} from '@domain/brand/brand-library';
import { ElementKind } from '@domain/elements';
import { CreativesetDataService } from '@studio/common';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { BrandLibraryDataService } from '../../../shared/media-library/brand-library.data.service';
import { FileDownloadService } from '../../../shared/services/filedownload.service';
import { EditElementComponent } from './edit-element.component';

@Component({
    selector: 'edit-element-topbar',
    templateUrl: './edit-element-topbar.component.html',
    styleUrls: ['./edit-element-topbar.component.scss'],
    animations: [
        // Temporary animation for logo / hamburger until we get the design from UX
        trigger('logoAnimation', [
            transition(':enter', [
                style({
                    opacity: '0',
                    transform: 'scale(0) rotate(-180deg)'
                }),
                animate(150)
            ]),
            transition(':leave', [
                animate(
                    150,
                    style({
                        transform: 'scale(-1.5) rotate(180deg)',
                        opacity: '0'
                    })
                )
            ]),
            state('*', style({ position: 'absolute' }))
        ])
    ],
    standalone: false
})
export class EditElementTopbarComponent implements OnInit, OnDestroy {
    @ViewChild('saveButton', { static: true }) saveButton: UIButtonComponent;
    @Input() element: IBrandLibraryElement | INewBrandLibraryElement;
    @Output() fileTabChanged = new EventEmitter();
    @Output() deleteElement = new EventEmitter();
    mediaAsset: OneOfLibraryAssets;

    private unsubscribe$ = new Subject<void>();

    constructor(
        @Inject(forwardRef(() => EditElementComponent)) public editElement: EditElementComponent,
        private creativesetDataService: CreativesetDataService,
        private fileDownloader: FileDownloadService,
        private brandLibraryDataService: BrandLibraryDataService
    ) {}

    async ngOnInit(): Promise<void> {
        if (this.isMediaElement()) {
            await this.brandLibraryDataService.brandLibraryLoaded;
            this.mediaAsset = this.brandLibraryDataService.getAssetByElement(
                this.element as IBrandLibraryElement
            )!;
        }

        if (this.isWidget()) {
            this.editElement.codeViewChange
                .pipe(takeUntil(this.unsubscribe$))
                .subscribe((file: any) => {
                    this.editElement.fileTabs.forEach(f => (f.selected = false));
                    file.selected = true;
                });
        }
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next();
        this.unsubscribe$.complete();
    }

    changeTab(file: File): void {
        this.editElement.fileTabs.forEach(f => (f.selected = false));
        file.selected = true;
        this.editElement.codeViewChange.next(file);
    }

    delete(): void {
        this.deleteElement.emit();
    }

    isMediaElement(): boolean {
        return isImageElement(this.element) || isVideoElement(this.element);
    }

    isWidget(): boolean {
        return mergeWidgetKind(this.element.type) === ElementKind.Widget;
    }

    downloadMedia(): void {
        this.fileDownloader.download(this.mediaAsset.url, this.mediaAsset.name);
    }
}

export class File {
    constructor(
        public name: string,
        public id: string,
        public type: string,
        public selected?: boolean
    ) {}
}
