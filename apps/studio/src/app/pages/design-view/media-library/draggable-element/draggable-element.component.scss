@use 'variables' as *;

:host {
    position: absolute;
    pointer-events: none;
    cursor: pointer;
}

.draggable-element {
    position: relative;
    z-index: 104;

    &.widget {
        background-color: var(--studio-color-second);

        .background-image {
            background-size: cover;
        }
    }

    svg {
        width: 100%;
    }
}

.draggable-element > div {
    border-style: none;
    width: 100%;
    height: 100%;
    z-index: 1;

    &.checkered-background {
        z-index: 3;
        background-size: 8px 8px;
        background-position: center;
        background-repeat: repeat;
        background-image: $chessBackgroundUrl !important;
    }

    &.background-image {
        &::ng-deep {
            > div {
                // Necessary for overwriting styles from renderer that we don't want on preview image
                pointer-events: none !important;
                visibility: visible !important;
            }
        }

        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        z-index: 4;
    }
}
