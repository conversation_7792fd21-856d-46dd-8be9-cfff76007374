import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DraggableElementComponent } from './draggable-element.component';

const bannerFlowHostedPNG = 'https://c.bannerflow.net/image.png';

describe('LibraryElementComponent', () => {
    let component: DraggableElementComponent;
    let fixture: ComponentFixture<DraggableElementComponent>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            declarations: [DraggableElementComponent],
            imports: [],
            providers: [],
            schemas: [NO_ERRORS_SCHEMA]
        }).compileComponents();

        fixture = TestBed.createComponent(DraggableElementComponent);
        component = fixture.componentInstance;
    });

    it('should create component', () => {
        expect(component).toBeTruthy();
    });

    it('should show default checkered image when theres no data', () => {
        component.src = undefined;
        component.isWidget = false;
        component.thumbnailElement = undefined;
        fixture.detectChanges();

        const element: HTMLElement = fixture.nativeElement;
        const defaultImageEl = element.querySelector('.checkered-background');

        expect(defaultImageEl).toBeTruthy();
    });

    it('should show default checkered image when widget asset has no source url', () => {
        component.src = undefined;
        component.isWidget = true;
        fixture.detectChanges();

        const element: HTMLElement = fixture.nativeElement;
        const defaultImageEl = element.querySelector('.checkered-background');

        expect(defaultImageEl).toBeTruthy();
    });

    it('should show thumbnail image when widget has a source url', () => {
        component.src = bannerFlowHostedPNG;
        component.isWidget = true;
        fixture.detectChanges();

        const element: HTMLElement = fixture.nativeElement;
        const backgroundImageEl = element.querySelector<HTMLDivElement>('.background-image');

        expect(backgroundImageEl).toBeTruthy();
        expect(backgroundImageEl!.style.backgroundImage).toBe(`url(${bannerFlowHostedPNG})`);
    });
});
