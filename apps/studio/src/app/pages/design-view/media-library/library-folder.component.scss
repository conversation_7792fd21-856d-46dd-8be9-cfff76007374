:host {
    width: 100%;
}

:host.selected,
.ml-folders-folder:hover {
    .ml-folders-folder__control {
        opacity: 1;
    }
}

.ml-folders-folder {
    display: flex;
    align-items: center;
    width: 100%;
    height: 26px;
    padding: 0 7px;

    &--isEditingName {
        box-shadow: inset 0 0 0 1px var(--studio-color-selection);
    }

    &__back {
        height: 26px;
        width: 26px;
        display: flex;
        align-items: center;

        border-right: 1px solid var(--studio-color-border-second);
    }

    &__name {
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        &-input {
            width: 100%;
            height: 100%;
            color: var(--text-color) !important;

            ::ng-deep input {
                padding: 0 !important;
                border: 0 !important;
            }

            ::ng-deep input:focus {
                background: var(--background-color) !important;
            }
        }
    }

    &__icon {
        margin: 0 7px 0 0;
    }

    &__control {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        cursor: pointer;
        opacity: 0;
    }
}

ui-dropdown-item.disabled {
    cursor: unset;
    pointer-events: initial;

    ::ng-deep .icon {
        --color: unset !important;
    }
}
