import { inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { cloneDeep } from '@apollo/client/utilities';
import { UIConfirmDialogService, UINotificationService } from '@bannerflow/ui';
import { resolveElementTextStyle } from '@creative/elements/rich-text/utils';
import { stripCustomPropertyPrefix } from '@creative/elements/widget/utils';
import { createInlineStyledTextFromText, isImageNode } from '@creative/nodes/helpers';
import {
    convertAnimationToDto,
    convertStateToDto,
    convertVideoAssetToDto,
    convertVideoSettingsToDto,
    deserializeWidgetPropertyValue,
    INLINE_STYLED_TEXT,
    serializeElementPropertyToStringValue,
    serializeWidgetPropertyValue
} from '@creative/serialization';
import { convertActionToDto } from '@creative/serialization/action-serializer';
import { serializeInlineStyledText } from '@creative/serialization/text-serializer';
import {
    IBrandLibrary,
    IBrandLibraryElement,
    IBrandLibraryWidgetElement,
    INewBrandLibraryElement
} from '@domain/brand/brand-library';
import { ICopyPasteElementSnapshot } from '@domain/copy-paste';
import { ElementPropertyUnit } from '@domain/creativeset';
import { IElementProperty } from '@domain/creativeset/element';
import { AssetReference } from '@domain/creativeset/element-asset';
import { IVersion, IVersionProperty } from '@domain/creativeset/version';
import { ElementKind } from '@domain/elements';
import {
    ICreativeDataNode,
    ITextViewElement,
    IVideoElementDataNode,
    OneOfElementDataNodes,
    OneOfElementPropertyKeys,
    OneOfTextDataNodes
} from '@domain/nodes';
import { IWidgetElementDataNode, IWidgetSelectOption, WIDGET_PROPERTY_PREFIX } from '@domain/widget';
import { isVersionedProperty } from '@studio/common/utils/versions.utils';
import { createBrandlibraryElement, createElementProperty } from '@studio/utils/element.utils';
import { getAssetOfMediaElement, isMediaNode, isMediaReference } from '@studio/utils/media';
import { omit } from '@studio/utils/utils';
import { firstValueFrom, Subject } from 'rxjs';
import { BrandLibraryDataService } from '../../../shared/media-library/brand-library.data.service';
import { VersionsService } from '../../../shared/versions/state/versions.service';
import { CopyPasteService } from '../services/copy-paste.service';
import { EditorStateService } from '../services/editor-state.service';
import { ElementCreatorService } from '../services/element-creator.service';
import { HistoryService, IEditorSnapshot } from '../services/history.service';
import { BrandLibraryElementDeletionService } from './brandlibrary-element-deletion.service';

export type BrandLibraryElementDataNode = Exclude<OneOfElementDataNodes, IWidgetElementDataNode>;

@Injectable()
export class BrandLibraryElementService {
    private uiConfirmDialogService = inject(UIConfirmDialogService);
    private uiNotificationService = inject(UINotificationService);
    private editorStateService = inject(EditorStateService);
    private elementCreatorService = inject(ElementCreatorService);
    private versionsService = inject(VersionsService);
    private brandLibraryDataService = inject(BrandLibraryDataService);
    private historyService = inject(HistoryService);
    private brandLibraryElementDeletionService = inject(BrandLibraryElementDeletionService);
    private copyPasteService = inject(CopyPasteService);

    private versions: IVersion[] = [];
    private versionProperties: IVersionProperty[] = [];

    brandLibraryUpdated$ = new Subject<void>();
    selectedElements$ = new Subject<IBrandLibraryElement[]>();

    constructor() {
        this.versionsService.versions$
            .pipe(takeUntilDestroyed())
            .subscribe(versions => (this.versions = versions));

        this.versionsService.selectedVersionProperties$
            .pipe(takeUntilDestroyed())
            .subscribe(versionProperties => (this.versionProperties = versionProperties));
    }

    async add(
        element: BrandLibraryElementDataNode,
        creativeDocument: ICreativeDataNode,
        displayNotification = true
    ): Promise<void> {
        const viewElement = this.editorStateService.renderer.getViewElementById<ITextViewElement>(
            element.id
        );

        if (!viewElement || !this.brandLibraryDataService.brandLibrary) {
            return;
        }

        const dataNode = viewElement.__data;

        // Create the element that is going to be saved in brandLibrary
        const brandLibraryElement = this.createNewBrandLibraryElement(
            resolveElementTextStyle(viewElement).__data
        );

        // Get current ids from brandLibrary
        const currentIds = this.brandLibraryDataService.brandLibrary.elements.map(e => e.id);

        await firstValueFrom(this.brandLibraryDataService.createElement(brandLibraryElement, dataNode));
        const newBrandLibrary = this.brandLibraryDataService.brandLibrary;

        // Get the newly created element from brandLibrary
        const newBrandlibraryElement = newBrandLibrary.elements.find(
            el => currentIds.indexOf(el.id) < 0
        );
        // Get the element that has been uploaded
        const dataElement = creativeDocument.elements.find(e => e.id === element.id);

        if (newBrandlibraryElement && dataElement) {
            // Apply the new brandLibrary element id to the element's parentId
            this.applyParentId(dataElement, newBrandlibraryElement, currentIds, newBrandLibrary);
        }

        this.brandLibraryElementDeletionService.brandLibraryUpdated$.next();

        if (displayNotification) {
            this.displayNotification('Element has been added to brand library');
        }
    }

    private createNewBrandLibraryElement(
        element: BrandLibraryElementDataNode
    ): INewBrandLibraryElement {
        const brandLibraryElement = createBrandlibraryElement({ type: element.kind });
        this.setElementProperties(element, brandLibraryElement, true);
        return brandLibraryElement;
    }

    async update(
        element: OneOfElementDataNodes,
        brandLibraryElement: IBrandLibraryElement,
        disableConfirm = false,
        updateName = false,
        displayNotification = true
    ): Promise<void> {
        const viewElement = this.editorStateService.renderer.getViewElementById(element.id);
        if (!viewElement) {
            return;
        }
        resolveElementTextStyle(viewElement);
        if (!disableConfirm) {
            const confirm = await this.confirmDialog();
            if (confirm) {
                await this.updateElement(brandLibraryElement, element, updateName);
            } else {
                return;
            }
        } else {
            await this.updateElement(brandLibraryElement, element, updateName);
        }

        if (displayNotification) {
            return this.displayNotification('Element has been updated.');
        }
    }

    async updateName(
        element: OneOfElementDataNodes,
        brandLibraryElement: IBrandLibraryElement,
        updateName = false
    ): Promise<void> {
        await this.updateElement(brandLibraryElement, element, updateName);
        this.displayNotification('Element has been updated.');
    }

    async duplicate(element: IBrandLibraryElement): Promise<void> {
        const brandLibraryElement = createBrandlibraryElement(element);
        await firstValueFrom(this.brandLibraryDataService.createElement(brandLibraryElement));
        this.brandLibraryElementDeletionService.brandLibraryUpdated$.next();
    }

    private applyParentId(
        element: OneOfElementDataNodes,
        newBrandLibraryElement: IBrandLibraryElement,
        preIds: string[],
        brandLibrary: Readonly<IBrandLibrary>
    ): void {
        if (element && newBrandLibraryElement) {
            element.parentId = newBrandLibraryElement.id;
        }

        // If no other elements where present in the library
        else if (preIds.length === 0 && element && brandLibrary.elements.length === 1) {
            element.parentId = [...brandLibrary.elements][0].id;
        }

        const snapshots: (IEditorSnapshot | ICopyPasteElementSnapshot)[] =
            this.historyService.getAllSnapshots();

        if (this.copyPasteService.copySnapshot?.context === 'element') {
            snapshots.push(this.copyPasteService.copySnapshot);
        }

        for (const snapshot of snapshots) {
            const snapshotDocumentElement = snapshot.creativeDataNode.elements.find(
                el => el.id === element.id
            );
            if (snapshotDocumentElement) {
                snapshotDocumentElement.parentId = newBrandLibraryElement.id;
            }
        }
    }

    private async updateElement(
        creativesetElement: IBrandLibraryElement,
        element: OneOfElementDataNodes,
        updateName: boolean
    ): Promise<void> {
        creativesetElement.properties = [];
        this.setElementProperties(element, creativesetElement, updateName);
        const updatedBrandLibrary = await firstValueFrom(
            this.brandLibraryDataService.updateElement(creativesetElement)
        );
        if (updatedBrandLibrary) {
            this.brandLibraryDataService.loadBrandLibrary();
            await firstValueFrom(this.brandLibraryDataService.brandLibrary$);
            this.brandLibraryElementDeletionService.brandLibraryUpdated$.next();
        }
    }

    // TODO: make generic logic in serializer?
    private setElementProperties(
        element: OneOfElementDataNodes,
        brandLibraryElement: INewBrandLibraryElement,
        updateName = false
    ): void {
        this.serializeBrandElementProperties(element, brandLibraryElement);

        // Patch elements that has missing media reference on node
        const mediaReference = brandLibraryElement.properties.find(isMediaReference);
        if (isMediaNode(element) && !mediaReference) {
            const name = isImageNode(element) ? AssetReference.Image : AssetReference.Video;
            const asset = getAssetOfMediaElement(element);

            brandLibraryElement.properties.push(
                createElementProperty({
                    unit: 'id',
                    name,
                    value: asset!.id
                })
            );
        }

        if (updateName) {
            brandLibraryElement.name = element.name;
        }

        switch (element.kind) {
            case ElementKind.Rectangle:
                brandLibraryElement.type = ElementKind.Rectangle;
                break;
            case ElementKind.Ellipse:
                brandLibraryElement.type = ElementKind.Ellipse;
                break;
            case ElementKind.Button:
                brandLibraryElement.type = ElementKind.Button;
                break;
            case ElementKind.Text:
                brandLibraryElement.type = ElementKind.Text;
                break;
            case ElementKind.Image:
                brandLibraryElement.type = ElementKind.Image;
                break;
            case ElementKind.Video:
                brandLibraryElement.type = ElementKind.Video;
                break;
            default:
                break;
        }
    }

    serializeBrandElementProperties(
        elementData: OneOfElementDataNodes,
        brandLibraryElement: IBrandLibraryElement | INewBrandLibraryElement
    ): void {
        const propertyKeys = Object.keys(elementData) as OneOfElementPropertyKeys[];
        for (const key of propertyKeys) {
            if (!isValidPropertyWithValue(elementData, key)) {
                continue;
            }
            let value: any;
            const unit = typeof elementData[key] as ElementPropertyUnit;
            switch (key) {
                case 'content': {
                    const content =
                        (elementData as OneOfTextDataNodes).__dirtyContent || elementData[key];
                    brandLibraryElement.properties.push(
                        createElementProperty({
                            name: INLINE_STYLED_TEXT,
                            unit: 'object',
                            value: serializeInlineStyledText(createInlineStyledTextFromText(content))
                        })
                    );
                    continue;
                }
                case 'states':
                case 'animations':
                case 'actions':
                    if (key === 'states') {
                        value = elementData.states.map(convertStateToDto);
                    } else if (key === 'animations') {
                        value = elementData.animations.map(animation =>
                            convertAnimationToDto(animation)
                        );
                    } else if (key === 'actions') {
                        value = elementData.actions
                            .filter(action =>
                                action.operations.every(op => op.target === elementData.id)
                            )
                            .map(action => convertActionToDto(action));
                    }

                    brandLibraryElement.properties.push(
                        createElementProperty({
                            name: key,
                            unit: 'array',
                            value: serializeElementPropertyToStringValue(key, value)
                        })
                    );
                    continue;
                case 'videoAsset':
                case 'videoSettings':
                    if (key === 'videoAsset') {
                        const videoAsset = (elementData as IVideoElementDataNode)?.videoAsset;
                        value = convertVideoAssetToDto(videoAsset);
                    } else if (key === 'videoSettings') {
                        value = convertVideoSettingsToDto(elementData[key]);
                    }
                    brandLibraryElement.properties.push(
                        createElementProperty({
                            name: key,
                            unit,
                            value: serializeElementPropertyToStringValue(key, value)
                        })
                    );
                    continue;
                case 'videoReference':
                case 'imageReference':
                    brandLibraryElement.properties.push(
                        createElementProperty({
                            name: key,
                            unit: 'id',
                            value: elementData[key]
                        })
                    );
                    continue;
                default:
                    value = elementData[key];
                    break;
            }

            brandLibraryElement.properties.push(
                createElementProperty({
                    name: key,
                    unit,
                    value
                })
            );
        }
    }

    async updateWidgetFromLibrary(
        widget: IWidgetElementDataNode,
        libraryWidget: IBrandLibraryWidgetElement
    ): Promise<void> {
        const element = widget.globalElement;

        const updatedWidget = await this.getUpdatedWidgetElementFromLibrary(
            widget,
            libraryWidget,
            true
        );

        if (!updatedWidget) {
            throw new Error('Could not get updated widget from library');
        }

        // Patch props that we don't want to update
        updatedWidget.customProperties.forEach(property => {
            const oldProperty = widget.customProperties.find(cp => cp.name === property.name);

            if (oldProperty) {
                if (property.unit === 'text' || property.unit === 'feed') {
                    property.versionPropertyId = oldProperty.versionPropertyId;
                }
            }

            if (isVersionedProperty(property)) {
                const elementProperty = element.properties.find(el => el.name === property.name);
                const versionProperty = this.versionProperties.find(
                    el => el.id === property.versionPropertyId
                );

                if (!elementProperty) {
                    /**
                     * Create a new version property if the property has a reference
                     * to one that doesn't exist in the case of corrupt data
                     */
                    if (!versionProperty) {
                        this.editorStateService.propertyAsVersionableProperty(
                            property,
                            property.unit === 'feed' ? 'feed' : 'widgetText'
                        );
                    }

                    element.properties.push(
                        createElementProperty({
                            ...omit(property, 'id')
                        })
                    );

                    return;
                }

                if (versionProperty && property.unit !== elementProperty.unit) {
                    this.versionsService.upsertVersionProperty(
                        this.versions.map(({ id }) => id),
                        {
                            ...versionProperty,
                            value: property.value,
                            name: property.unit === 'text' ? 'widgetText' : property.unit
                        }
                    );
                }

                property.versionPropertyId = elementProperty.versionPropertyId;
                property.value = elementProperty.versionPropertyId ? undefined : elementProperty.value;
                elementProperty.label = property.label;
                elementProperty.unit = property.unit;
            }
        });

        const propertiesToPersist = /^(widgetReference|widgetContentBlobReference)$/;

        const newProperties: IElementProperty[] = [];

        for (const property of element.properties) {
            if (propertiesToPersist.test(property.name)) {
                newProperties.push(property);
                continue;
            }

            const propertyExist = updatedWidget.customProperties.some(
                customProperty =>
                    customProperty.name.replace(WIDGET_PROPERTY_PREFIX, '') === property.name &&
                    customProperty.unit === property.unit
            );

            if (propertyExist) {
                newProperties.push(property);
            }
        }

        element.properties = newProperties;
        widget.customProperties = updatedWidget.customProperties;
    }

    async getUpdatedWidgetElementFromLibrary(
        documentElement: IWidgetElementDataNode,
        elementFromLibrary: IBrandLibraryWidgetElement,
        skipEmit?: boolean
    ): Promise<IWidgetElementDataNode> {
        const brandLibraryWidget = cloneDeep(elementFromLibrary);
        brandLibraryWidget.name = documentElement.name;

        for (const property of brandLibraryWidget.properties) {
            const oldProperty = documentElement.customProperties.find(
                ({ name }) => name === stripCustomPropertyPrefix(property.name)
            );

            if (!oldProperty) {
                continue;
            }

            /**
             * Get the version property value, needed when property unit changes
             * and multiple different versions and sizes are affected
             */
            const versionProperty = this.versionProperties.find(
                ({ id }) => id === oldProperty.versionPropertyId
            );

            const unit = property.unit;
            const isSameUnit = oldProperty.unit === unit;
            let value = isSameUnit ? (versionProperty?.value ?? oldProperty.value) : property.value;

            switch (unit) {
                case 'select': {
                    // Add new options from BL, and preserve the selected options
                    const oldOptions = value as IWidgetSelectOption[];
                    const newOptions = deserializeWidgetPropertyValue(
                        property
                    ) as IWidgetSelectOption[];

                    const oldSelection = oldOptions.find(option => option.selected);
                    const newSelection = newOptions.find(
                        option => option.value === oldSelection?.value
                    );

                    if (newSelection) {
                        // 1st option is selected by default
                        newOptions[0].selected = false;
                        newSelection.selected = true;
                    }

                    property.value = serializeWidgetPropertyValue('select', newOptions);
                    break;
                }
                default:
                    if (!isSameUnit) {
                        value = deserializeWidgetPropertyValue(property);
                    }
                    property.value = serializeWidgetPropertyValue(unit, value);
                    break;
            }
        }

        const newWidget = await this.elementCreatorService.createWidget(
            omit(documentElement, 'customProperties', 'id', 'html', 'css', 'js'),
            { element: brandLibraryWidget, skipEmit }
        );

        if (!skipEmit) {
            this.patchVersionProperties(documentElement, newWidget);
        }

        return newWidget;
    }

    /**
     * Patches the version properties of the widget with previous property values.
     */
    private patchVersionProperties(
        widget: IWidgetElementDataNode,
        newWidget: IWidgetElementDataNode
    ): void {
        for (const oldCustomProperty of widget.customProperties) {
            for (const version of this.versions) {
                const newCustomProperty = newWidget.customProperties.find(
                    ({ name }) => name === oldCustomProperty.name
                );

                const isOldPropertyVersioned = isVersionedProperty(oldCustomProperty);
                const isNewPropertyVersioned = isVersionedProperty(newCustomProperty);

                // Case 1: Old property was versioned but new one isn't - remove old version property
                if (isOldPropertyVersioned && !isNewPropertyVersioned) {
                    this.versionsService.removeVersionPropertiesByIds([
                        oldCustomProperty.versionPropertyId!
                    ]);
                    continue;
                }

                const prevVersionValue = version.properties.find(
                    ({ id }) => id === oldCustomProperty.versionPropertyId
                )?.value;

                // Skip if there's no previous value or new property isn't versioned
                if (!prevVersionValue || !isNewPropertyVersioned) {
                    continue;
                }

                // Case 2: Both old and new properties are versioned with previous value
                const newVersionProperty = version.properties.find(
                    ({ id }) => id === newCustomProperty?.versionPropertyId
                );

                if (newVersionProperty) {
                    // If units differ then upsert the entire property
                    if (oldCustomProperty.unit !== newCustomProperty.unit) {
                        this.versionsService.upsertVersionProperty(version.id, newVersionProperty);
                    } else {
                        this.versionsService.upsertVersionProperty(version.id, {
                            ...newVersionProperty,
                            value: prevVersionValue
                        });
                    }
                } else {
                    this.versionsService.addVersionProperty(version.id, {
                        id: newCustomProperty.versionPropertyId!,
                        value: prevVersionValue,
                        name: newCustomProperty.unit === 'text' ? 'widgetText' : newCustomProperty.unit
                    });

                    this.versionsService.removeVersionPropertiesByIds([
                        oldCustomProperty.versionPropertyId!
                    ]);
                }
            }
        }
    }

    async confirmDialog(): Promise<boolean> {
        const result = await this.uiConfirmDialogService.confirm({
            headerText: 'Update brand element',
            text: 'Do you want to update the brand element?',
            confirmText: 'Yes'
        });
        return result !== 'cancel';
    }

    private displayNotification(message: string): void {
        return this.uiNotificationService.open(message, {
            autoCloseDelay: 5000,
            placement: 'top',
            type: 'info'
        });
    }
}

function isValidPropertyWithValue(elementData: OneOfElementDataNodes, property: string): boolean {
    return (
        elementData[property] !== undefined &&
        property !== 'name' &&
        property !== 'constraint' &&
        property !== 'kind' &&
        !property.includes('__')
    );
}
