import { AssetReference } from '@domain/creativeset/element-asset';
import { LibraryKind } from '@domain/media-library';
import { ElementKind } from '@domain/elements';
import { createBrandLibraryElementMock, createLibraryWidgetMock } from '@mocks/brand-library.mock';
import { createElementPropertyMock } from '@mocks/element.mock';
import {
    isEffectLibraryElement,
    isEffectWidget,
    isElementTypeMatchLibraryType
} from './media-library.helpers';

describe('media library helpers', () => {
    describe('isEffectWidget', () => {
        it('should return true if widget is an effect', () => {
            const effectWidget = createLibraryWidgetMock({ id: 'effect1' });
            expect(isEffectWidget(effectWidget)).toBe(true);
        });
        it('should return false if widget is not an effect', () => {
            const widget = createLibraryWidgetMock({ id: 'non-effect-id' });
            expect(isEffectWidget(widget)).toBe(false);
        });
    });

    describe('isEffectLibraryElement', () => {
        it('should return false is element is not of type "BannerflowLibraryWidget"', () => {
            const customWidgetElement = createBrandLibraryElementMock(ElementKind.Widget);
            expect(isEffectLibraryElement(customWidgetElement)).toBe(false);
        });
        it('should return false if element is missing "bannerflowLibraryWidgetReference" property', () => {
            const corruptEffectElement = createBrandLibraryElementMock(
                ElementKind.BannerflowLibraryWidget,
                { id: 'effect1' }
            );
            expect(isEffectLibraryElement(corruptEffectElement)).toBe(false);
        });

        it('should return true if element is listed as effect & contains reference property', () => {
            const widgetReferenceProperty = createElementPropertyMock({
                name: AssetReference.BannerflowLibraryWidget,
                value: 'effect1'
            });
            const effectElement = createBrandLibraryElementMock(ElementKind.BannerflowLibraryWidget, {
                id: 'effect1',
                properties: [widgetReferenceProperty]
            });

            expect(isEffectLibraryElement(effectElement)).toBe(true);
        });
    });

    describe('isElementTypeMatchLibraryType', () => {
        const imageElement = createBrandLibraryElementMock(ElementKind.Image);
        const videoElement = createBrandLibraryElementMock(ElementKind.Video);
        const widgetElement = createBrandLibraryElementMock(ElementKind.BannerflowLibraryWidget);
        const effectElement = createBrandLibraryElementMock(ElementKind.BannerflowLibraryWidget, {
            id: 'effect1',
            properties: [
                createElementPropertyMock({
                    name: AssetReference.BannerflowLibraryWidget,
                    value: 'effect1'
                })
            ]
        });

        describe('effects', () => {
            it('should return false if libraryKind is "Widget" and element is an effect widget', () => {
                const libraryKind = LibraryKind.Widget;
                expect(isElementTypeMatchLibraryType(effectElement, libraryKind)).toBe(false);
            });

            it('should return true if libraryKind is "Effects" and element is an effect widget', () => {
                const libraryKind = LibraryKind.Effects;
                expect(isElementTypeMatchLibraryType(effectElement, libraryKind)).toBe(true);
            });
            it('should return false if libraryKind is "Any" and element is an effect widget', () => {
                const libraryKind = LibraryKind.Any;
                expect(isElementTypeMatchLibraryType(effectElement, libraryKind)).toBe(false);
            });

            it('should return false if libraryKind is "Effects" and element is a non-effect widget', () => {
                const libraryKind = LibraryKind.Effects;
                expect(isElementTypeMatchLibraryType(widgetElement, libraryKind)).toBe(false);
            });
        });

        it('should return true if libraryKind is "Image" and element is an image', () => {
            const libraryKind = LibraryKind.Image;
            expect(isElementTypeMatchLibraryType(imageElement, libraryKind)).toBe(true);
        });

        it('should return false if libraryKind is "Image" and element is a video', () => {
            const libraryKind = LibraryKind.Image;
            expect(isElementTypeMatchLibraryType(videoElement, libraryKind)).toBe(false);
        });

        it('should return true if libraryKind is "Any" and element is an image', () => {
            const libraryKind = LibraryKind.Any;
            expect(isElementTypeMatchLibraryType(imageElement, libraryKind)).toBe(true);
        });
    });
});
