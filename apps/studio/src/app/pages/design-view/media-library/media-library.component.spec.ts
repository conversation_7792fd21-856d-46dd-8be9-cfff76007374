import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { EventEmitter, NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { ActivatedRoute } from '@angular/router';
import { UIListComponent, UIModule } from '@bannerflow/ui';
import { ElementSelection } from '@creative/nodes';
import { IBrandLibraryElement } from '@domain/brand/brand-library';
import { ElementKind } from '@domain/elements';
import { createEditorFixture } from '@fixtures/editor.fixture';
import { createRendererFixture } from '@fixtures/renderer.fixture';
import { createBrandLibraryElementMock, createBrandLibraryMock } from '@mocks/brand-library.mock';
import { createMockCreativesetDataService } from '@mocks/creativeset.mock';
import { createMockEditorStateService } from '@mocks/editor.mock';
import { createMockBrandLibraryDataService } from '@mocks/services/brand-library-data-service.mock';
import { createBrandLibraryElementMockService } from '@mocks/services/brand-library-element-service.mock';
import { createMockElementRenderingService } from '@mocks/services/element-rendering-service.mock';
import { createMockMediaLibraryService } from '@mocks/services/media-library-service.mock';
import { createVersionServiceMock } from '@mocks/services/versions-service.mock';
import { CreativesetDataService, UserService } from '@studio/common';
import { BrandService } from '@studio/common/brand';
import { provideEnvironment } from '@studio/common/environment/environment.provider';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { AssetUploadEvent, EventLoggerService } from '@studio/monitoring/events';
import { ApolloTestingModule } from 'apollo-angular/testing';
import { BehaviorSubject, of } from 'rxjs';
import { environment } from '../../../../environments/environment.test';
import { FiltersService } from '../../../shared/filters/state/filters.service';
import { BrandLibraryDataService } from '../../../shared/media-library/brand-library.data.service';
import { MediaLibraryService } from '../../../shared/media-library/state/media-library.service';
import { defineMatchMedia } from '../../../shared/mocks/matchMedia.mock';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../../shared/mocks/store.mock';
import { HeavyVideoService } from '../../../shared/services/heavy-video.service/heavy-video.service';
import { VersionsService } from '../../../shared/versions/state/versions.service';
import { DesignViewComponent } from '../design-view.component';
import { AssetUploadService } from '../services/asset-upload.service';
import { EditorStateService } from '../services/editor-state.service';
import { ElementCreatorService } from '../services/element-creator.service';
import { ElementSelectionService } from '../services/element-selection.service';
import { HistoryService } from '../services/history.service';
import { MutatorService } from '../services/mutator.service';
import { BrandLibraryElementService } from './brandlibrary-element.service';
import { BrandLibraryFolderService } from './brandlibrary-folder.service';
import { ElementRenderingService } from './element-renderering-service';
import { LibraryElementRenderingService } from './library-element-renderer.service';
import { MediaLibraryComponent } from './media-library.component';

describe('MediaLibraryComponent', () => {
    let component: MediaLibraryComponent;
    let fixture: ComponentFixture<MediaLibraryComponent>;
    let eventLoggerService: EventLoggerService;
    let assetUploadService: AssetUploadService;
    let brandLibraryElementService: BrandLibraryElementService;
    let brandLibraryDataService: BrandLibraryDataService;

    const mockBrandLibraryElements = [
        createBrandLibraryElementMock(ElementKind.Rectangle, { name: 'Test element' })
    ];

    const creativesetDataServiceMock = createMockCreativesetDataService({
        getAccountSlug: jest.fn()
    });

    beforeAll(() => {
        defineMatchMedia();
    });

    beforeEach(() => {
        const renderer = createRendererFixture();
        TestBed.configureTestingModule({
            declarations: [MediaLibraryComponent],
            imports: [UIModule, NoopAnimationsModule, ApolloTestingModule],
            providers: [
                provideHttpClient(),
                provideHttpClientTesting(),
                provideEnvironment(environment),
                EnvironmentService,
                { provide: MediaLibraryService, useValue: createMockMediaLibraryService() },
                {
                    provide: UserService,
                    useValue: {
                        hasPermission: (): boolean => true
                    }
                },
                {
                    provide: BrandLibraryFolderService,
                    useValue: {
                        selectedFolder$: of(undefined)
                    }
                },
                { provide: HeavyVideoService, useValue: {} },
                { provide: DesignViewComponent, useValue: {} },
                {
                    provide: EditorStateService,
                    useValue: createMockEditorStateService({ renderer })
                },
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER,
                { provide: CreativesetDataService, useValue: creativesetDataServiceMock },
                {
                    provide: ElementRenderingService,
                    useValue: createMockElementRenderingService()
                },
                {
                    provide: BrandLibraryElementService,
                    useValue: createBrandLibraryElementMockService()
                },
                {
                    provide: BrandLibraryDataService,
                    useValue: createMockBrandLibraryDataService({
                        brandLibrary: createBrandLibraryMock({
                            elements: mockBrandLibraryElements
                        })
                    })
                },
                { provide: MutatorService, useValue: {} },
                { provide: ElementCreatorService, useValue: {} },
                { provide: LibraryElementRenderingService, useValue: {} },
                EventLoggerService,
                {
                    provide: AssetUploadService,
                    useValue: {
                        uploadProgress$: of({}),
                        uploadAssets: jest.fn()
                    }
                },
                BrandService,
                { provide: MediaLibraryService, useValue: createMockMediaLibraryService() },
                {
                    provide: ElementSelectionService,
                    useValue: {
                        currentSelection: new ElementSelection()
                    }
                },
                { provide: ActivatedRoute, useValue: new ActivatedRoute() },
                { provide: HistoryService, useValue: {} },
                { provide: DesignViewComponent, useValue: createEditorFixture() },
                {
                    provide: FiltersService,
                    useValue: {
                        selectedVersions$: new BehaviorSubject({}),
                        filtersState$: of({})
                    }
                },
                {
                    provide: VersionsService,
                    useValue: createVersionServiceMock()
                }
            ],
            schemas: [NO_ERRORS_SCHEMA]
        }).compileComponents();

        fixture = TestBed.createComponent(MediaLibraryComponent);
        component = fixture.componentInstance;
        eventLoggerService = TestBed.inject(EventLoggerService);
        assetUploadService = TestBed.inject(AssetUploadService);
        brandLibraryElementService = TestBed.inject(BrandLibraryElementService);
        brandLibraryDataService = TestBed.inject(BrandLibraryDataService);

        fixture.detectChanges();

        component.list = {
            currentNodeChange: new EventEmitter(undefined)
        } as UIListComponent;
    });

    it('should create component', () => {
        expect(component).toBeTruthy();
    });

    describe('uploadFilesAndClearFileInput', () => {
        it('should call uploadFiles and clear the file input value', () => {
            const spyEventLogger = jest.spyOn(eventLoggerService, 'log');
            const spyUploadAssets = jest.spyOn(assetUploadService, 'uploadAssets');
            const fileInput = {
                files: [new File([], 'filename.mp4', { type: 'video' })],
                value: 'value'
            } as unknown as HTMLInputElement;
            const parentFolderId = '123';

            component.uploadFilesAndClearFileInput(fileInput, parentFolderId);

            expect(spyEventLogger).toHaveBeenCalledWith(new AssetUploadEvent());
            expect(spyUploadAssets).toHaveBeenCalled();
            expect(fileInput.value).toBe('');
        });
    });

    describe('duplicateElement', () => {
        let duplicateSpy: jest.SpyInstance;
        let blElement: IBrandLibraryElement;
        beforeEach(() => {
            duplicateSpy = jest.spyOn(brandLibraryElementService, 'duplicate');
            blElement = brandLibraryDataService.brandLibrary!.elements[0]!;
        });

        it('should generate a unique name for new brand library element', () => {
            const expectedElement = {
                ...blElement,
                name: `${blElement.name} (1)`
            };
            component.duplicateElement(blElement);
            expect(duplicateSpy).toHaveBeenCalledWith(expectedElement);
        });

        it('should use a default name if name does not exist', () => {
            const expectedElement = {
                ...blElement,
                name: 'Rectangle'
            };
            blElement.name = '';
            component.duplicateElement(blElement);
            expect(duplicateSpy).toHaveBeenCalledWith(expectedElement);
        });
    });
});
