@use 'variables' as *;

:host {
    width: 100%;

    &.widget .library-element-image__preview {
        background-size: cover;
    }

    &.effect {
        &.is-importing-effect {
            .library-element__import-overlay {
                opacity: 1;
            }
        }

        .library-element:hover {
            .library-element__import-overlay {
                opacity: 1;
            }
        }
    }
}

.hidden {
    display: none !important;
}

.library-element {
    width: 100%;

    &:hover {
        cursor: pointer;

        .library-element__badge {
            opacity: 1;
        }
    }

    &--preview {
        padding-bottom: 10px;

        .library-element__body {
            height: 135px;
            padding: 0 10px;
        }
    }

    &__input {
        width: 100%;
    }

    &__body {
        height: 70px;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        overflow: hidden;
        padding: 0 5px 5px;

        &--widget {
            height: 135px;
        }
    }

    ::ng-deep &__badge {
        position: absolute;
        bottom: 5px;
        right: 5px;
        width: 22px;
        height: 22px;
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 2px;
        background-color: var(--studio-color-background-second);
        opacity: 0;
        transition: all 0.25s;

        &.always-visible {
            opacity: 1;
        }

        &.non-exportable {
            background-color: rgba($color: #000000, $alpha: 0.32);
            left: 5px;
            right: auto;
            color: white !important;
        }

        ui-logo {
            width: 14px;
            height: 14px;
        }
    }

    &__import-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba($color: #000000, $alpha: 0.75);
        color: white;
        z-index: 2;
        display: flex;
        text-transform: uppercase;
        font-weight: bolder;
        text-align: center;
        align-items: center;
        margin: 0 auto;
        opacity: 0;
        transition: all 0.25s;
        justify-content: center;
    }

    &-widget-placeholder {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: var(--studio-color-background-second);
        z-index: -1;
        display: flex;
        align-items: center;
        justify-content: center;

        &__icon {
            color: var(--studio-color-border-second);
            width: 40px;
            height: 40px;
            font-size: 40px;

            transform: translate(-2px, -2px);
        }
    }

    &-image {
        position: relative;
        height: 100%;
        width: 100%;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: $chessBackgroundUrl;
            background-position: center;
            background-repeat: repeat;
            background-size: 8px;
            opacity: 0.5;
            z-index: 0;
        }

        background-color: var(--studio-color-background-second);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1;

        &__preview {
            height: 100%;
            width: 100%;
            background-size: contain;
            background-position: center;
            background-repeat: no-repeat;
            z-index: 1;
        }
    }

    &-video-duration-list {
        color: var(--studio-color-text-third);
        margin-left: 6px;
    }

    &-video-duration-preview {
        background: linear-gradient(
            289.84deg,
            var(--studio-color-transparent-black-40) 0%,
            var(--studio-color-transparent) 73.49%
        );
        position: absolute;
        right: 0;
        bottom: 0;
        z-index: 1;
        width: 100%;
        height: 26px;
        display: flex;
        align-items: center;
        justify-content: right;
        padding: 6px;
        color: var(--studio-color-white);
        font-weight: 700;
        font-size: 12px;
    }
}
