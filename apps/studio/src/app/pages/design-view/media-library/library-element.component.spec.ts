import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { ElementRef, NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { UIModule } from '@bannerflow/ui';
import { ElementKind } from '@domain/elements';
import {
    createBrandLibraryElementMock,
    createImageLibraryAssetMock,
    createVideoLibraryAssetMock
} from '@mocks/brand-library.mock';
import { createMockCreativesetDataService } from '@mocks/creativeset.mock';
import { createEditorSaveStateServiceMock } from '@mocks/services/editor-save-state.service.mock';
import { CreativesetDataService, UserService } from '@studio/common';
import { provideEnvironment } from '@studio/common/environment/environment.provider';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { cloneDeep } from '@studio/utils/clone';
import { ApolloTestingModule } from 'apollo-angular/testing';
import { BehaviorSubject, EMPTY, Observable, of } from 'rxjs';
import { environment } from '../../../../environments/environment.test';
import { BrandLibraryDataService } from '../../../shared/media-library/brand-library.data.service';
import { MediaLibraryService } from '../../../shared/media-library/state/media-library.service';
import { defineMatchMedia } from '../../../shared/mocks/matchMedia.mock';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../../shared/mocks/store.mock';
import { BrandLibraryElementTooltipPipe } from '../../../shared/pipes/brand-library-element-tooltip.pipe';
import { FileDownloadService } from '../../../shared/services/filedownload.service';
import { WidgetDataService } from '../../../shared/services/widget.data.service';
import { EditCreativeService } from '../../manage-view/services/edit-creative.service';
import { AssetPickerUploadService } from '../asset-picker/asset-picker-upload.service';
import { DesignViewComponent } from '../design-view.component';
import { PropertiesService } from '../properties-panel/properties.service';
import { AssetUploadService } from '../services/asset-upload.service';
import { EditorSaveStateService } from '../services/editor-save-state.service';
import { EditorStateService } from '../services/editor-state.service';
import { ElementReplaceService } from '../services/element-replace.service';
import { ElementSelectionService } from '../services/element-selection.service';
import { BrandLibraryElementService } from './brandlibrary-element.service';
import { ElementRenderingService } from './element-renderering-service';
import { LibraryElementRenderingService } from './library-element-renderer.service';
import { LibraryElementComponent } from './library-element.component';
import { MediaLibraryComponent } from './media-library.component';

class MockElementRef extends ElementRef {
    constructor() {
        super(null);
    }
}

const mockPropertiesService: Partial<PropertiesService> = {
    observeDataElementOrStateChange: (): Observable<never> => EMPTY,
    getPlaceholderValue: (): number => 0.85
};

const mockElementRenderingService = {
    isStyledElement: (): boolean => false,
    createElement: jest.fn(),
    renderer: {
        getViewElementById: jest.fn()
    }
};

const mockBrandLibraryDataService: Partial<BrandLibraryDataService> = {
    getAssetByElement: jest.fn()
};

const mockBrandLibraryElementService: Partial<BrandLibraryElementService> = {
    brandLibraryUpdated$: new BehaviorSubject<void>(undefined)
};

const bannerFlowHostedPNG = 'https://c.bannerflow.net/image.png';
const bannerFlowHostedMP4 = 'https://c.bannerflow.net/video.mp4';

describe('LibraryElementComponent', () => {
    let component: LibraryElementComponent;
    let fixture: ComponentFixture<LibraryElementComponent>;

    beforeAll(() => {
        defineMatchMedia();
    });

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [LibraryElementComponent],
            imports: [
                UIModule,
                NoopAnimationsModule,
                BrandLibraryElementTooltipPipe,
                ApolloTestingModule
            ],
            providers: [
                provideHttpClient(),
                provideHttpClientTesting(),
                provideEnvironment(environment),
                EnvironmentService,
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER,
                { provide: ElementRef, useValue: new MockElementRef() },
                { provide: FileDownloadService, useValue: {} },
                { provide: MediaLibraryService, useValue: {} },
                { provide: ElementReplaceService, useValue: {} },
                { provide: DesignViewComponent, useValue: {} },
                { provide: AssetPickerUploadService, useValue: {} },
                { provide: EditCreativeService, useValue: {} },
                { provide: EditorStateService, useValue: {} },
                {
                    provide: UserService,
                    useValue: {
                        hasPermission: jest.fn().mockReturnValue(false),
                        loaded$: of(true)
                    }
                },
                { provide: AssetUploadService, useValue: {} },
                { provide: LibraryElementRenderingService, useValue: {} },
                { provide: WidgetDataService, useValue: { get: jest.fn() } },
                {
                    provide: MediaLibraryComponent,
                    useValue: {
                        inDialog: false,
                        bannerflowLibraryWidgets: [],
                        bannerflowLibraryEffects: []
                    }
                },
                { provide: PropertiesService, useValue: mockPropertiesService },
                { provide: CreativesetDataService, useValue: createMockCreativesetDataService() },
                { provide: ElementRenderingService, useValue: mockElementRenderingService },
                { provide: BrandLibraryElementService, useValue: mockBrandLibraryElementService },
                { provide: BrandLibraryDataService, useValue: mockBrandLibraryDataService },
                { provide: ElementSelectionService, useValue: {} },
                { provide: EditorSaveStateService, useValue: createEditorSaveStateServiceMock() }
            ],
            schemas: [NO_ERRORS_SCHEMA]
        }).compileComponents();

        fixture = TestBed.createComponent(LibraryElementComponent);
        component = fixture.componentInstance;
    });

    it('should create component', () => {
        expect(component).toBeTruthy();
    });

    describe('Image element', () => {
        const mockImageAsset = createImageLibraryAssetMock();
        mockImageAsset.thumbnail.url = bannerFlowHostedPNG;

        it('should render thumbnail and icon for an image element', async () => {
            mockBrandLibraryDataService.getAssetByElement = jest.fn().mockReturnValue(mockImageAsset);

            const { nativeElement } = fixture.debugElement;
            const mockImageElement = createBrandLibraryElementMock(ElementKind.Image);
            component.element = {
                ...mockImageElement,
                progress: 1
            };

            await component.ngOnInit();

            const previewElement = nativeElement.querySelector('.library-element-image__preview');
            const backgroundImageStyle = previewElement.style.backgroundImage;
            expect(nativeElement.querySelector('ui-svg-icon .icon-image')).toBeTruthy();
            expect(backgroundImageStyle).toEqual(`url(${bannerFlowHostedPNG})`);
        });
    });

    describe('Video element', () => {
        const mockVideoAsset = cloneDeep(createVideoLibraryAssetMock());
        mockVideoAsset.thumbnail.url = bannerFlowHostedMP4;

        it('should render thumbnail and icon for an video element', async () => {
            mockBrandLibraryDataService.getAssetByElement = jest.fn().mockReturnValue(mockVideoAsset);

            const { nativeElement } = fixture.debugElement;
            const mockVideoElement = createBrandLibraryElementMock(ElementKind.Video);
            component.element = {
                ...mockVideoElement,
                progress: 1
            };

            await component.ngOnInit();

            const previewElement = nativeElement.querySelector('.library-element-image__preview');
            const backgroundImageStyle = previewElement.style.backgroundImage;

            expect(nativeElement.querySelector('ui-svg-icon .icon-video')).toBeTruthy();
            expect(backgroundImageStyle).toEqual(`url(${bannerFlowHostedMP4})`);
        });
    });
});
