<div
    class="field-info"
    #fieldName>
    <span class="name">&#64;{{ item.name }}</span>
</div>
<div
    class="values"
    [class.images]="this.isValidMediaUrl">
    @for (itemValue of item.values | slice: 0 : maximumItems; track itemValue; let i = $index) {
        <div class="value-wrapper">
            <div
                class="value"
                [class.image]="this.isValidMediaUrl"
                [style.left]="i * 30 + 'px'"
                [style.zIndex]="item.values.length - i">
                @if (!this.isValidMediaUrl) {
                    {{ itemValue }}
                }
                @if (isImage) {
                    <div class="background"></div>
                    <div
                        class="img"
                        [style.backgroundImage]="'url(' + itemValue + ')'"></div>
                }
                @if (isVideo) {
                    @if (!videosData.length) {
                        <ui-loader></ui-loader>
                    }
                    @if (videosData.length) {
                        <div class="background"></div>
                        @if (videosData[i]) {
                            <div
                                class="img"
                                [style.backgroundImage]="'url(' + videosData[i].thumbnail + ')'"></div>
                            <div class="video-duration">
                                {{ videosData[i].duration * 1000 | date: 'mm:ss' }}
                            </div>
                        }
                    }
                }
            </div>
            @if (i + 1 === maximumItems && item.values.length > maximumItems && isImage && !inDialog) {
                <div
                    class="value more"
                    [style.left]="i * 30 + 90 + 'px'">
                    ...
                </div>
            }
        </div>
    }
</div>
@if (item.values.length > maximumItems && !isValidMediaUrl && !inDialog) {
    <div class="value more">...</div>
}
