@use 'variables' as *;

$widthHeight: 84px;

:host {
    display: block;
    border-bottom: 1px solid var(--studio-color-border-second);
    padding: 0.7rem 0.9rem;
    cursor: pointer;

    &:not(.disabled):hover {
        background: var(--studio-color-surface);
    }

    &.disabled {
        pointer-events: none;
        background: var(--studio-color-surface-second);

        .field-info,
        .value {
            opacity: 0.6;
        }
    }

    &.selected {
        background: var(--studio-color-blue-light) !important;
    }

    .field-info {
        margin-bottom: 0.7rem;
    }

    .field-info {
        display: flex;
        justify-content: space-between;

        .name {
            color: var(--studio-color-primary);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 1px;
            min-width: 100%;
        }

        .amount {
            color: var(--studio-color-text-secondary);
        }
    }

    .values {
        &.images {
            position: relative;
            height: $widthHeight;
        }

        .value-wrapper {
            margin-bottom: 0.7rem;

            &:last-child {
                margin-bottom: 0;
            }

            .value {
                color: var(--studio-color-text-secondary);
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;

                &.image {
                    margin-bottom: 0;
                    position: absolute;
                    width: $widthHeight;
                    height: $widthHeight;
                    top: 0;
                    box-shadow: 0px 0px 0px 2px var(--studio-color-surface);
                    border-radius: var(--default-border-radius);
                }

                .img {
                    background-size: cover;
                    background-position: center;
                    background-repeat: no-repeat;
                    width: $widthHeight;
                    height: $widthHeight;
                    position: absolute;
                    border-radius: 2px;

                    &.hasMore {
                        &::after {
                            content: '';
                            display: block;
                        }
                    }
                }

                .background {
                    display: block;
                    content: '';
                    width: 100%;
                    position: absolute;
                    z-index: 0;
                    height: 100%;
                    background-size: 8px 8px;
                    background-position: center;
                    background-repeat: repeat;
                    background-color: var(--studio-color-surface-second);
                    background-image: $chessBackgroundUrl;
                }

                .more {
                    margin-bottom: -0.7rem;
                }

                .video-duration {
                    background: linear-gradient(
                        289.84deg,
                        var(--studio-color-transparent-black-40) 0%,
                        var(--studio-color-transparent) 73.49%
                    );
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    z-index: 1;
                    width: 100%;
                    height: 26px;
                    display: flex;
                    align-items: center;
                    justify-content: right;
                    padding: 6px;
                    color: var(--studio-color-white);
                    font-weight: 700;
                    font-size: 12px;
                }
            }

            .more {
                position: absolute;
                bottom: -3px;
            }
        }
    }

    &.grid {
        padding: 0;
        display: grid;
        grid-template-columns: 1fr 7fr;
        border-left: 1px solid var(--studio-color-border-second);

        .field-info,
        .value {
            padding: 1rem;
            margin-bottom: 0;
        }

        .field-info .amount {
            display: none;
        }

        .values {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
            justify-content: center;
            align-items: center;

            &.images {
                height: $widthHeight + 20px;
            }

            .value-wrapper {
                position: relative;
                overflow: visible;

                .value.image {
                    height: 100%;
                    width: 100%;
                    display: flex;
                    justify-content: center;
                    position: static;
                    box-shadow: none;

                    .img {
                        position: static;
                    }

                    .background {
                        display: none;
                    }
                }
            }
        }
    }
}

:host-context(.feed-view) {
    &.grid {
        &:first-child {
            .value-wrapper {
                &:last-child {
                    &::after {
                        min-height: var(--feed-view-height);
                        height: 100%;
                        width: 1px;
                        background: var(--studio-color-border-second);
                        display: block;
                        content: '';
                        top: 0;
                        position: absolute;
                        right: 0;
                        z-index: 16;
                    }
                }

                &::before {
                    height: var(--feed-view-height);
                    width: 1px;
                    background: var(--studio-color-border-second);
                    display: block;
                    content: '';
                    top: 0;
                    position: absolute;
                    left: 0;
                    z-index: 16;
                }
            }
        }
    }
}
