:host {
    height: 100%;
    background: var(--studio-color-white-off-light);
    display: flex;
    box-shadow: 0.1rem 0 0 0 var(--studio-color-border-second);
    z-index: 1;
    position: relative;
    flex-direction: column;

    &.hidden {
        display: none !important;
    }

    &.isEditingName {
        ::ng-deep {
            .cdk-tree-node {
                box-shadow: unset !important;
            }

            .ui-list.one-column {
                .list-body.grid {
                    padding: 0 0 0 1px !important;
                }
            }
        }
    }
}

.hidden {
    display: none !important;
}

.ml-inner {
    display: flex;
    flex-direction: column;
    position: relative;
    flex: 1;
    overflow-y: auto;
}

.media-library-selector {
    display: flex;
    width: 100%;
    color: var(--studio-color-text-second);

    &__control {
        width: 2.7rem;
        height: 2.7rem;
        display: flex;
        align-items: center;
        border-left: 1px solid var(--studio-color-border-second);

        &:first-child {
            border: none;
        }

        &--isActive {
            width: 100%;

            .media-library-selector__text {
                display: block;
            }
        }
    }

    &__icon {
        width: 2.7rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &__text {
        display: none;
        text-transform: uppercase;
    }
}

.ml-folders {
    border-bottom: 1px solid var(--studio-color-border-second);

    ::ng-deep {
        .cell.empty {
            display: none;
        }

        ui-list-cell {
            padding: 0;

            &.isDraggingOn {
                background-color: var(--studio-color-primary);

                .ml-folders-folder {
                    color: var(--studio-color-background-second);
                }
            }
        }
    }

    &-header {
        display: flex;
        align-items: center;
        height: 2.7rem;
        color: var(--studio-color-text-second);

        &.isDraggingOn {
            background-color: var(--studio-color-primary);
            color: var(--studio-color-background-second);
        }

        &__back {
            height: 2.7rem;
            width: 2.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-right: 1px solid var(--studio-color-border-second);
            cursor: pointer;
        }

        &__name {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            width: calc(100% - 2.7rem);
        }
    }

    &-header {
        &__name {
            padding-left: 1rem;
        }
    }

    &:has(.ml-folders-selected) {
        position: sticky;
        top: 0;
        background: var(--studio-color-white-off-light);
        z-index: 2;
    }
}

.list-title {
    font-size: 1.2rem;
    color: var(--studio-color-text-second);
    display: grid;
    grid-template-columns: 9px 19px 5px auto;
    padding-top: 6px;
    align-items: center;
}

.fileDropOverlay {
    width: 100%;
    height: 100%;
    position: absolute;
    background: var(--studio-color-transparent-white-75);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    color: var(--studio-color-primary);
    z-index: 4;

    .arrow {
        transform: rotateZ(180deg);
        font-size: 6rem;
        pointer-events: none;
    }
}

.list-wrapper {
    height: 100%;
    min-height: 300px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.list-top {
    display: flex;
    border-top: 1px solid var(--studio-color-border-second);

    .crumbs {
        width: 100%;
        border-bottom: 1px solid var(--studio-color-border-second);
        height: 27px;
        display: flex;
        align-items: center;

        .feed-navigation {
            display: flex;
            align-items: center;
            color: var(--studio-color-text-secondary);
            cursor: pointer;
            width: 100%;
            padding: 0 0.5rem 0 0.5rem;

            .gutter {
                margin-right: 0.7rem;
                margin-left: 0.1rem;
            }

            &__name {
                overflow: hidden;
            }
        }
    }

    .search {
        width: 100%;
        border-bottom: 1px solid var(--studio-color-border-second);
        height: 27px;
        display: flex;
        align-items: center;
        padding-left: 1px;

        &.active {
            background: var(--studio-color-background-second);
        }

        .search-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--studio-color-text-third);
            margin-left: 8px;
            position: absolute;
            z-index: 1;

            &.darker {
                color: var(--studio-color-text-second);
            }

            ui-svg-icon {
                font-size: 12px;
            }
        }

        ui-input {
            height: 100%;
            padding-left: 1px;

            & ::ng-deep .input {
                border: 1px solid transparent;
                background: transparent;
                height: 100%;
                padding-left: 25px;

                &:focus {
                    border: 1px solid var(--studio-color-focus);
                }
            }
        }
    }

    .input {
        width: 100%;
    }

    .sort-icon,
    .add-icon {
        cursor: pointer;
        flex-shrink: 0;
        width: 27px;
        height: 27px;
        border-bottom: 1px solid var(--studio-color-border-second);
        border-left: 1px solid var(--studio-color-border-second);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--studio-color-text-second);

        &.active {
            background: var(--studio-color-grey-93);
        }

        &:hover,
        &.active {
            color: var(--studio-color-black);
        }

        ::ng-deep {
            ui-svg-icon {
                margin-left: -0.5px;
            }
        }
    }
}

.list-bottom {
    display: flex;
    justify-content: space-between;
    height: 32px;
    align-items: center;
    border-top: 1px solid var(--studio-color-border-second);
    padding-right: 10px;

    &--feeds {
        justify-content: flex-end;
    }

    .view-modes {
        display: flex;
    }

    .icon {
        cursor: pointer;
        width: 14px;
        height: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--studio-color-text-third);
        margin-left: 10px;

        &:hover {
            color: var(--black);
        }

        &.active {
            color: var(--studio-color-primary);
        }

        &.disabled {
            opacity: 0.6;
            cursor: default;

            &:hover {
                color: var(--studio-color-text-second);
            }
        }

        ::ng-deep {
            ui-svg-icon {
                width: 1.4rem;
                height: 1.4rem;
            }
        }
    }
}

.asset-list {
    display: flex;
    overflow-y: auto;
    flex-direction: column;
    flex: 1;
    position: relative;
    margin-top: -1px;

    ui-loader {
        --background-color: var(--studio-color-white-off-light);
    }

    &.isDragging {
        pointer-events: none;
    }

    ::ng-deep {
        .ui-list {
            border: none;

            .cell {
                height: 100%;
                border: 0;
            }

            .row {
                cursor: pointer;
                background: var(--background-alt);

                &:hover {
                    background: var(--studio-color-grey-semilight);
                }
            }
        }

        .cdk-tree-node {
            border-radius: unset !important;
            color: var(--studio-color-text-discrete);

            &.selected {
                z-index: 1 !important;
            }

            &:not(.selected):hover {
                box-shadow: none !important;
                color: var(--black);
                background-color: var(--studio-color-border-second);
            }
        }

        .cell.empty {
            display: none;
        }
    }

    /* Brand library new styles */
    ::ng-deep {
        .ui-list.border {
            border-bottom: 1px solid var(--studio-color-border-second);
        }

        .list-body.grid {
            padding: 2px;
            grid-gap: 0;
            grid-template-columns: repeat(auto-fill, 50%);
            grid-auto-rows: unset;

            &:before {
                display: none;
            }
        }

        .ui-list.one-column {
            .list-body.grid {
                grid-template-columns: 100%;

                .box {
                    justify-content: flex-start;
                    align-items: flex-start;

                    .grid-cell {
                        justify-content: flex-start;
                        align-items: flex-start;
                    }
                }
            }
        }

        .row.folder {
            min-height: 24px;
        }

        ui-list-folder-cell {
            padding: 5px 7px !important;

            &:hover {
                .name {
                    text-decoration: none;
                }

                .folder-icon {
                    color: var(--studio-color-black);
                }
            }
        }
    }

    /* The same styles for feeds as for list-elements */
    ::ng-deep {
        library-element.selected,
        .library-element:hover {
            .library-element-row__control {
                width: 14px;
                opacity: 100%;
            }
        }

        .grid-cell:hover {
            .library-element-row__link {
                display: block;
            }
        }

        .library-element-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 7px;
            overflow: hidden;
            flex: 1;

            &__icon {
                margin-right: 7px;
            }

            &__wrapper {
                display: flex;
                overflow: hidden;
            }

            &__name {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                width: 100%;
            }

            &__control {
                width: 0;
                opacity: 0;
                transition:
                    width 0.1s,
                    opacity 0.1s;
                overflow: hidden;
            }

            &__link {
                display: none;
                color: var(--studio-color-text-second);
                cursor: pointer;
            }
        }
    }
}

.asset-list-empty {
    width: 100%;
    position: absolute;
    top: 58px;
    bottom: 58px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    z-index: 3;

    .top {
        color: var(--studio-color-text-third);
    }

    .middle {
        color: var(--studio-color-text-second);
        font-size: 1.2rem;
        white-space: pre-line;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translateY(-50%) translateX(-50%);
        width: 100%;

        .title {
            font-size: 1.4rem;
            text-transform: uppercase;
            margin-bottom: 10px;
            white-space: normal;
        }
    }

    .bottom {
        .text {
            color: var(--studio-color-text-second);
            padding: 10px 0 20px 0;
        }

        &.hidden {
            visibility: hidden;
            display: initial;
        }
    }
}

::ng-deep {
    .cdk-overlay-pane.ml-tooltip {
        margin-top: 3px;
        max-width: 250px;
    }

    .content {
        &.ml-tooltip,
        &.ml-tooltip-video {
            word-break: break-word;
            text-align: center;
            padding: 0 !important;

            span {
                display: inline-block;
                padding: 4px 10px;
            }
        }

        &.ml-tooltip-video {
            text-align: left;

            .heavy-video {
                color: var(--ui-color-text-second);
                padding: 0;
            }
        }

        .non-exportable-icon {
            width: 14px;
            height: 14px;
            transform: translateY(2px);
            margin-right: 3px;
            stroke: black;
            stroke-width: 0;
        }
    }
}

:host.inDialog {
    &::ng-deep {
        .list-body.grid {
            grid-gap: 5px;
            grid-template-columns: repeat(auto-fill, 100px);
        }
    }

    &.images,
    &.videos {
        width: 100%;
        flex-direction: row;

        .list-wrapper {
            flex-direction: row;
            width: 100%;
        }

        .list-top {
            width: 20rem;
            border-right: 1px solid var(--studio-color-border-second);
            border-top: 0;
            height: 100%;
            flex-direction: column;
        }

        .list-bottom {
            display: none;
        }
    }

    .asset-list-empty {
        .border {
            border-bottom: 1px solid var(--studio-color-border-second);
        }
    }

    .action-bar {
        height: 100%;
        display: flex;
        align-items: center;

        justify-content: center;

        .settings {
            padding: 0 20px;
            display: flex;
            align-items: center;
        }

        .buttons {
            .ui-button {
                width: 180px;

                &:last-child {
                    margin-left: 40px;
                }
            }
        }
    }

    .main-content {
        display: grid;
        grid-template-rows: 6fr 130px;
        width: 100%;
        height: 100%;

        .ui-list {
            width: 100%;
            border-bottom: 1px solid var(--studio-color-border-second);
        }
    }

    &.feeds {
        width: 100%;
        height: 100%;
        background: none;
        display: flex;
        box-shadow: none;
        z-index: 1;
        position: relative;
        flex-direction: row;

        .list-wrapper {
            height: 100%;
            flex-direction: column;
            width: 20rem;
            background: var(--studio-color-white-off-light);
            display: flex;
            box-shadow: 0.1rem 0 0 0 var(--studio-color-border-second);
        }

        .feed-view {
            overflow: auto;
        }

        .action-bar {
            border-top: 1px solid var(--studio-color-border-second);
        }

        .list-bottom {
            display: none;
        }
    }

    .ml-folders {
        overflow: auto;

        /* makes style look like the non-dialog version of folders */
        ::ng-deep {
            .ui-list {
                border: none;

                .cell {
                    height: 100%;
                }

                .row {
                    cursor: pointer;
                    background: var(--background-alt);

                    &:hover {
                        background: var(--studio-color-grey-semilight);
                    }
                }
            }

            .cdk-tree-node {
                border-radius: unset !important;
                color: var(--studio-color-text-discrete);

                &.selected {
                    z-index: 1 !important;
                }

                &:not(.selected):hover {
                    box-shadow: none !important;
                    color: var(--black);
                    background-color: var(--studio-color-border-second);
                }
            }

            .cell.empty {
                display: none;
            }
        }
    }
}

.feed-item-row {
    display: grid;
    align-items: center;
    width: 100%;
    height: 100%;
    grid-template-columns: 14px 1fr auto;
    grid-column-gap: 0.7rem;

    &.error {
        grid-template-columns: 14px auto 14px;
    }

    .name {
        overflow: hidden;
    }

    .icon {
        color: var(--studio-color-text);
        opacity: var(--folder-opacity);
    }

    &.error {
        .error {
            display: block;
        }
    }

    .error {
        display: none;
    }
}

.new-folder-wrap {
    padding: 0 1rem;
}

.no-result {
    padding: 2rem 0;
    text-align: center;
    color: var(--studio-color-text-second);
    opacity: 0.7;
}
