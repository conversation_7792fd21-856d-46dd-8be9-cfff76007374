import { HttpClientTestingModule } from '@angular/common/http/testing';
import { EventEmitter, NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { provideFeatureFlags } from '@bannerflow/feature-flags';
import { SentinelService } from '@bannerflow/sentinel';
import { UIModule } from '@bannerflow/ui';
import { ElementKind } from '@domain/elements';
import { createRendererFixture } from '@fixtures/renderer.fixture';
import { createBrandLibraryElementMock } from '@mocks/brand-library.mock';
import { createMockCreativesetDataService } from '@mocks/creativeset.mock';
import { createMockEditorStateService } from '@mocks/editor.mock';
import { createDataNodeMock } from '@mocks/element.mock';
import { createBrandLibraryElementMockService } from '@mocks/services/brand-library-element-service.mock';
import { createEditorEventServiceMock } from '@mocks/services/editor-event-service.mock';
import { createFontFamiliesServiceMock } from '@mocks/services/font-families-service.mock';
import { createMockMediaLibraryService } from '@mocks/services/media-library-service.mock';
import { createMutatorServiceMock } from '@mocks/services/mutator-service.mock';
import { BrandService, CreativesetDataService, FontFamiliesService, UserService } from '@studio/common';
import { provideEnvironment } from '@studio/common/environment/environment.provider';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { provideMock } from '@studio/testing/utils/provide-mock';
import { ApolloTestingModule } from 'apollo-angular/testing';
import { of } from 'rxjs';
import { environment } from '../../../../environments/environment.test';
import { BrandLibraryDataService } from '../../../shared/media-library/brand-library.data.service';
import { MediaLibraryService } from '../../../shared/media-library/state/media-library.service';
import { defineMatchMedia } from '../../../shared/mocks/matchMedia.mock';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../../shared/mocks/store.mock';
import { DesignViewComponent } from '../design-view.component';
import { BrandLibraryElementService } from '../media-library/brandlibrary-element.service';
import { PropertiesService } from '../properties-panel/properties.service';
import { AssetUploadService } from '../services/asset-upload.service';
import { CopyPasteService } from '../services/copy-paste.service';
import { EditorEventService } from '../services/editor-event/editor-event.service';
import { EditorStateService } from '../services/editor-state.service';
import { ElementCreatorService } from '../services/element-creator.service';
import { ElementHighlightService } from '../services/element-highlight.service';
import { ElementSelectionBoundingBoxService } from '../services/element-selection-bounding-box.service';
import { ElementSelectionService } from '../services/element-selection.service';
import { HistoryService } from '../services/history.service';
import { MutatorService } from '../services/mutator.service';
import { SelectionNetService } from '../services/selection-net.service';
import { AnimationRecorderService } from '../timeline/animation-recorder.service';
import { KeyframeService } from '../timeline/timeline-element/keyframe.service';
import { ElementMenuComponent } from './context-menu/element-menu/element-menu.component';
import { StudioWorkspaceService } from './services/studio-workspace.service';
import { WorkspaceUploadAssetService } from './services/workspace-upload-asset.service';
import { StudioWorkspaceComponent } from './studio-workspace.component';
import { WorkspaceGradientHelperService } from './workspace-gradient-helper.service';
import { ZoomControlService } from './zoom-control/zoom-control.service';

describe('StudioWorkspaceComponent', () => {
    let component: StudioWorkspaceComponent;
    let mutatorService: MutatorService;
    let fixture: ComponentFixture<StudioWorkspaceComponent>;

    beforeAll(() => {
        defineMatchMedia();
    });

    beforeEach(() => {
        const editorStateServiceMock = createMockEditorStateService({
            renderer: createRendererFixture()
        });

        TestBed.configureTestingModule({
            declarations: [StudioWorkspaceComponent, DesignViewComponent, ElementMenuComponent],
            imports: [UIModule, ApolloTestingModule, HttpClientTestingModule, NoopAnimationsModule],
            providers: [
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER,
                ElementSelectionService,
                provideEnvironment(environment),
                provideMock(EnvironmentService),
                provideMock(ElementSelectionBoundingBoxService),
                provideMock(WorkspaceUploadAssetService),
                provideMock(SentinelService),
                provideMock(KeyframeService),
                provideMock(SelectionNetService),
                provideMock(CopyPasteService),
                provideMock(HistoryService),
                provideMock(StudioWorkspaceService),
                provideMock(WorkspaceGradientHelperService, { setup: jest.fn(), off: jest.fn() }),
                provideMock(ElementHighlightService, { clearHighlight: jest.fn() }),
                provideMock(EditorEventService, createEditorEventServiceMock()),
                provideMock(BrandLibraryElementService, createBrandLibraryElementMockService()),
                provideMock(ElementCreatorService, { create$: of() }),
                provideMock(PropertiesService, {
                    propertiesService: {
                        getCalculatedStateAtCurrentTime: jest.fn().mockReturnValue({})
                    }
                }),
                provideMock(DesignViewComponent, {
                    updateElementsVersionedPropertiesForSelectedVersion: jest.fn(),
                    document: editorStateServiceMock.creativeDataNode
                }),
                provideMock(
                    MutatorService,
                    createMutatorServiceMock({
                        renderer: editorStateServiceMock.renderer
                    })
                ),
                provideMock(EditorStateService, editorStateServiceMock),
                provideMock(UserService, { isEmployee$: of(false) }),
                provideMock(CreativesetDataService, createMockCreativesetDataService()),
                provideMock(BrandService, { accountSlug$: of() }),
                provideMock(AssetUploadService, { uploadProgress$: of() }),
                provideMock(MediaLibraryService, createMockMediaLibraryService()),
                provideMock(FontFamiliesService, createFontFamiliesServiceMock()),
                provideMock(AnimationRecorderService, { recording$: of() }),
                provideMock(ZoomControlService, { zoomControlHovered: new EventEmitter<boolean>() }),
                provideMock(BrandLibraryDataService, { fetchBrandLibrary$: of() }),
                provideFeatureFlags({
                    enabled: false
                })
            ],
            schemas: [NO_ERRORS_SCHEMA]
        }).compileComponents();

        fixture = TestBed.createComponent(StudioWorkspaceComponent);
        component = fixture.componentInstance;
        component.centerCanvas = jest.fn();
        component.setZoom = jest.fn();
        component['initializeGizmoDrawer'] = jest.fn();
        component['setupHotkeyListeners'] = jest.fn();
        component['removeHotkeyListeners'] = jest.fn();
        component['placeElementOnPlayhead'] = jest.fn();
        component.gizmoDrawer = { destroy: jest.fn(), draw: jest.fn() } as any;
        component.renderer = editorStateServiceMock.renderer;
        mutatorService = TestBed.inject(MutatorService);
        fixture.detectChanges();
    });

    it('should create component', () => {
        expect(component).toBeTruthy();
    });

    describe('addNodeToCanvas', () => {
        it('should start text editing when one text element added to canvas', () => {
            const startEditTextSpy = jest.spyOn(mutatorService, 'startEditText');

            const blElementMock = createBrandLibraryElementMock(ElementKind.Text);
            const dataNodeMock = createDataNodeMock({ kind: ElementKind.Text });

            component.brandLibraryElementService.selectedElements$.next([blElementMock]);

            component['addNodeToCanvas'](dataNodeMock);

            expect(startEditTextSpy).toHaveBeenCalledTimes(1);

            component['addNodeToCanvas'](dataNodeMock);

            expect(startEditTextSpy).toHaveBeenCalledTimes(2);

            startEditTextSpy.mockClear();
        });

        it('should not start text editing when multiple text elements are added to canvas', () => {
            const startEditTextSpy = jest.spyOn(mutatorService, 'startEditText');

            component.brandLibraryElementService.selectedElements$.next([
                createBrandLibraryElementMock(ElementKind.Text, { id: '1' }),
                createBrandLibraryElementMock(ElementKind.Text, { id: '2' })
            ]);

            component['addNodeToCanvas'](createDataNodeMock({ id: '1', kind: ElementKind.Text }));
            component['addNodeToCanvas'](createDataNodeMock({ id: '2', kind: ElementKind.Text }));

            expect(startEditTextSpy).toHaveBeenCalledTimes(0);

            startEditTextSpy.mockClear();
        });
    });
});
