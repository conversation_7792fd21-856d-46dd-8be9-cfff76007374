import { UIInputComponent } from '@bannerflow/ui';
import {
    Component,
    Input,
    Output,
    EventEmitter,
    OnChanges,
    Renderer2,
    ElementRef,
    ViewChild,
    AfterViewInit,
    OnDestroy
} from '@angular/core';
import { ZoomControlService } from './zoom-control.service';
import { fromEvent, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
    selector: 'zoom-control',
    templateUrl: './zoom-control.component.html',
    styleUrls: ['./zoom-control.component.scss'],
    host: {
        '(mouseleave)': 'mouseEnter($event)',
        '(mouseenter)': 'mouseEnter($event)'
    },
    standalone: false
})
export class ZoomControlComponent implements OnChanges, AfterViewInit, OnDestroy {
    @Input() zoom: number;
    @ViewChild('numberInput') input: UIInputComponent;
    hideUnitlabel = false;
    @Output() isHovered = new EventEmitter<boolean>();
    private unsubscribe$ = new Subject<void>();

    constructor(
        private host: ElementRef,
        private zoomControlService: ZoomControlService,
        private renderer: Renderer2
    ) {}

    ngOnChanges(): void {
        this.zoom = Math.round(this.zoom);
        this.shouldHideUnitlabel(String(this.zoom).length);
    }

    ngAfterViewInit(): void {
        fromEvent<KeyboardEvent>(this.input.valueContainer().nativeElement, 'keydown')
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe((event: KeyboardEvent) => {
                const selection = window.getSelection();
                const isSelectedValue = selection && selection.toString();
                const { value } = this.input.valueContainer().nativeElement;

                if (isSelectedValue) {
                    return;
                }

                if (value.length >= 4 && !isNaN(Number(event.key))) {
                    event.preventDefault();
                }
            });
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next();
        this.unsubscribe$.complete();
    }

    updateZoom(): void {
        const zoom = this.zoom / 1000;

        if (zoom <= 32) {
            this.zoomControlService.changeZoom(this.zoom / 100);
        }
    }

    decrease(): void {
        const normalizeZoom = this.zoom / 100 / 2;
        const decrease = this.zoomControlService.shouldSnap(normalizeZoom) ? 1 : normalizeZoom;
        this.zoomControlService.changeZoom(decrease);
    }

    increase(): void {
        const normalizeZoom = (this.zoom / 100) * 2;
        const increase = this.zoomControlService.shouldSnap(normalizeZoom) ? 1 : normalizeZoom;
        if (increase <= 32) {
            this.zoomControlService.changeZoom(increase);
        }
    }

    mouseEnter = (event: PointerEvent): void => {
        const isHovered = event.type === 'mouseleave' ? false : true;
        if (isHovered) {
            this.renderer.setStyle(this.host.nativeElement, 'cursor', 'pointer');
        }
        this.zoomControlService.zoomControlHovered.emit(isHovered);
    };

    focusHandler = (): void => {
        this.hideUnitlabel = true;
    };

    blurHandler = (): void => {
        this.shouldHideUnitlabel(this.input.valueContainer().nativeElement.value.length);
    };

    private shouldHideUnitlabel(length: number): void {
        this.hideUnitlabel = length >= 4 ? true : false;
    }
}
