import {
    AfterViewInit,
    Directive,
    ElementRef,
    Host,
    inject,
    Input,
    OnD<PERSON>roy,
    Renderer2
} from '@angular/core';
import { IBoundingBox, IPosition, ISize } from '@domain/dimension';
import { combineLatest, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { HotkeyBetterService } from '../../../../shared/services/hotkeys/hotkey.better.service';
import { EditorEventService } from '../../services/editor-event/editor-event.service';
import { EditorStateService } from '../../services/editor-state.service';
import { ElementSelectionBoundingBoxService } from '../../services/element-selection-bounding-box.service';
import { ElementSelectionService } from '../../services/element-selection.service';
import { StudioTimelineService } from '../../timeline/studio-timeline/studio-timeline.service';
import { DEFAULT_TIMELINE_COLLAPSED_HEIGHT } from '../../timeline/timeline.constants';
import { StudioWorkspaceComponent } from '../studio-workspace.component';
import { Zoom, ZoomControlService } from './zoom-control.service';

@Directive({
    selector: '[appZoomControl]',
    exportAs: 'appZoomControl',
    host: {
        '(pointerup)': 'click($event)',
        '(window:blur)': 'blur($event)',
        '(window:keyup)': 'macOsFix($event)'
    },
    standalone: false
})
export class ZoomControlDirective implements OnDestroy, AfterViewInit {
    @Input() zoom: number;
    @Input() isZooming: boolean;
    @Input() mouseOverTimeline: boolean;
    private host = inject(ElementRef<HTMLElement>);
    @Host() private workspace = inject(StudioWorkspaceComponent);
    private zoomControlService = inject(ZoomControlService);
    private editorStateService = inject(EditorStateService);
    private renderer = inject(Renderer2);
    private hotkeyBetterService = inject(HotkeyBetterService);
    private editorEventService = inject(EditorEventService);
    private elementSelectionService = inject(ElementSelectionService);
    private elementSelectionBoundingBoxService = inject(ElementSelectionBoundingBoxService);
    private studioTimelineService = inject(StudioTimelineService);

    private savedZoomPosition: IPosition | undefined;
    private savedZoom = 1;
    private isOverlayPresent: boolean;
    private overlay: HTMLDivElement;
    private activeCursor: string;
    private isComponentHovered = false;
    private isMediaLibraryOpen = false;

    private unsubscribe$ = new Subject<void>();

    constructor() {
        this.host.nativeElement.addEventListener('wheel', this.scroll, {
            capture: true,
            passive: false
        });

        combineLatest([this.editorEventService.workspaceInit$, this.editorEventService.timelineInit$])
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe(() => {
                const creativeSizeAndPosition = {
                    ...this.workspace.canvasSize,
                    ...this.workspace.canvasCenterPosition
                };
                this.toBoundingBox(creativeSizeAndPosition, newPosition => {
                    if (newPosition.zoom < 1) {
                        this.setZoomAndPosition(newPosition);
                    }
                });
            });

        this.workspace.mediaLibraryService.isOpen$.subscribe(isOpen => {
            this.isMediaLibraryOpen = isOpen;
        });
    }

    ngAfterViewInit(): void {
        this.renderer.setAttribute(this.host.nativeElement, 'tabindex', '0');

        this.zoomControlService.zoomControlHovered
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe(isHovered => (this.isComponentHovered = isHovered));

        this.setupChangeZoomSubscription();

        this.hotkeyBetterService.on('EnableZoom', this.enable);
        this.hotkeyBetterService.on('EnableZoomOut', this.enableZoomOut);
        this.hotkeyBetterService.on('DisableZoom', this.disable);
        this.hotkeyBetterService.on('ZoomIn', this.zoomIn);
        this.hotkeyBetterService.on('ZoomOut', this.zoomOut);
        this.hotkeyBetterService.on('ZoomWheel', this.wheel);
        this.hotkeyBetterService.on('ZoomPreview', this.sectionDown);
        this.hotkeyBetterService.on('ZoomPreviewEnd', this.sectionUp);
        this.hotkeyBetterService.on('ZoomToBox', this.zoomToSelectionBox);
        this.hotkeyBetterService.on('ZoomReset', this.resetZoom);
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next();
        this.unsubscribe$.complete();

        this.host.nativeElement.removeEventListener('wheel', this.scroll);

        this.hotkeyBetterService.off('EnableZoom', this.enable);
        this.hotkeyBetterService.off('EnableZoomOut', this.enableZoomOut);
        this.hotkeyBetterService.off('DisableZoom', this.disable);
        this.hotkeyBetterService.off('ZoomIn', this.zoomIn);
        this.hotkeyBetterService.off('ZoomOut', this.zoomOut);
        this.hotkeyBetterService.off('ZoomWheel', this.wheel);
        this.hotkeyBetterService.off('ZoomPreview', this.sectionDown);
        this.hotkeyBetterService.off('ZoomPreviewEnd', this.sectionUp);
        this.hotkeyBetterService.off('ZoomToBox', this.zoomToSelectionBox);
        this.hotkeyBetterService.off('ZoomReset', this.resetZoom);
    }

    private enable = (): void => {
        this.workspace.isZooming = true;
        this.setCursor('zoom-in');
    };

    private enableZoomOut = (): void => {
        this.workspace.isZooming = true;
        this.setCursor('zoom-out');
    };

    private disable = (): void => {
        this.reset();
    };

    private zoomIn = (event?: PointerEvent): void => {
        if (event) {
            const zoomBox = this.workspace.zoomBox;
            zoomBox
                ? this.handleZoomToBoundingBox(zoomBox, event)
                : this.toCursor(this.zoom * 2, event);
        } else {
            const newZoom = this.zoom * 2;
            const increase = this.zoomControlService.shouldSnap(newZoom) ? 1 : newZoom;
            this.zoomControlService.changeZoom(increase);
        }
    };
    private zoomOut = (event?: PointerEvent): void => {
        if (event) {
            this.toCursor(this.zoom / 2, event);
        } else {
            const newZoom = this.zoom / 2;
            const decrease = this.zoomControlService.shouldSnap(newZoom) ? 1 : newZoom;
            this.zoomControlService.changeZoom(decrease);
        }
    };
    /**
        @description: Triggered when user presses Alt + Tab
        Need to clear the active keys when user leaves the page
     */
    blur(event: KeyboardEvent): void {
        event.preventDefault();
        this.hotkeyBetterService.clearActiveKeys();
        this.hotkeyBetterService.clearActiveControlKeys();
        this.reset();
    }

    private wheel = (event: WheelEvent): void => {
        const mousePosition = this.workspace.getMousePositionRelativeToWorkspace(event);
        const canvasPosition = this.workspace.canvasLayerComponent.getPosition();
        const canvasPositionAndSize = { ...canvasPosition, ...this.workspace.canvasSize };
        const zoomAndPosition = this.zoomControlService.calculateZoomToMousePosition(
            event,
            mousePosition,
            canvasPositionAndSize,
            this.zoom
        );
        this.zoomControlService.changeZoom(this.zoom);
        this.setZoomAndPosition(zoomAndPosition);
    };

    /**
     * @description:
        Workaround for MAC-OS. Meta-key blocks keyup event
     */
    macOsFix = (event: KeyboardEvent): void => {
        if (event.key?.toUpperCase() === 'Z') {
            this.reset();
        }
    };
    sectionDown = (): void => {
        if (this.zoom !== 1) {
            this.savedZoomPosition = this.workspace.canvasLayerComponent.getPosition();
            this.savedZoom = this.zoom;
        }

        this.centerAndResetZoom();
    };

    sectionUp = (): void => {
        if (this.savedZoomPosition && this.savedZoom) {
            this.setZoomAndPosition({ ...this.savedZoomPosition, zoom: this.savedZoom });
        }
    };

    click(event: PointerEvent): void {
        if (this.workspace.isZooming && !this.isComponentHovered && !this.workspace.isPanning) {
            this.hotkeyBetterService.emitKey('Click', window as any, event);
        }
    }

    scroll = (event: WheelEvent): void => {
        event.preventDefault();
        this.hotkeyBetterService.emitKey('Wheel', window as any, event);
    };

    resetZoom = (): void => {
        this.centerAndResetZoom();
    };

    private setupChangeZoomSubscription(): void {
        this.zoomControlService.changeZoomObservable
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe(zoom => {
                const workspaceSize = this.getWorkspaceSize();
                const mousePosition = { x: workspaceSize.width / 2, y: workspaceSize.height / 2 };
                const canvasPosition = this.workspace.canvasLayerComponent.getPosition();
                const canvasPositionAndSize = { ...canvasPosition, ...this.workspace.canvasSize };
                const position = this.zoomControlService.calculateZoomToCenter(
                    zoom,
                    mousePosition,
                    canvasPositionAndSize,
                    this.zoom
                );
                this.setZoomAndPosition({ ...position, zoom });
            });
    }

    private handleZoomToBoundingBox(zoomBox: IBoundingBox, event: PointerEvent): void {
        if (zoomBox.width < 5 || zoomBox.height < 5) {
            this.toCursor(this.zoom * 2, event);
        } else {
            this.toBoundingBox(zoomBox);
        }
    }

    private centerAndResetZoom(): void {
        if (this.editorStateService.zoom !== 1) {
            const positionAndZoom: IPosition & Zoom = { ...this.calculateNewPosition(1), zoom: 1 };
            this.setZoomAndPosition(positionAndZoom);
            this.workspace.centerCanvas();
        }
    }

    private zoomToSelectionBox = (): void => {
        const selectionBoundingBox =
            this.elementSelectionBoundingBoxService.boundingBoxes?.lockedAndHiddenExcluded;
        const selection = this.elementSelectionService.currentSelection;
        if (selectionBoundingBox && selection && selection.length > 0) {
            this.toBoundingBox(selectionBoundingBox);
        } else {
            const creativeSizeAndPosition = {
                ...this.workspace.canvasSize,
                ...this.workspace.canvasCenterPosition
            };
            this.toBoundingBox(creativeSizeAndPosition);
        }
    };

    private setCursor(cursor: 'zoom-in' | 'zoom-out'): void {
        if (this.activeCursor !== cursor) {
            this.removeOverlay();
        }
        if (!this.isOverlayPresent) {
            this.createZoomCursorOverlay(cursor);
        }
    }

    private createZoomCursorOverlay(cursor: string): void {
        const { height, width } = this.host.nativeElement.getBoundingClientRect();
        this.overlay = this.renderer.createElement('div');

        this.renderer.setAttribute(this.overlay, 'id', 'zooming-cursor-fix');

        this.renderer.setStyle(this.overlay, 'width', `${width}px`);
        this.renderer.setStyle(this.overlay, 'height', `${height}px`);
        this.renderer.setStyle(this.overlay, 'background-color', `transparent`);
        this.renderer.setStyle(this.overlay, 'cursor', cursor);
        this.renderer.setStyle(this.overlay, 'z-index', 999);
        this.renderer.setStyle(this.overlay, 'position', 'absolute');

        this.renderer.appendChild(this.host.nativeElement, this.overlay);
        this.isOverlayPresent = true;
        this.activeCursor = cursor;
    }

    private removeOverlay(): void {
        if (this.isOverlayPresent) {
            this.renderer.removeChild(this.host.nativeElement, this.overlay);
            this.isOverlayPresent = false;
            this.activeCursor = '';
        }
    }

    private toBoundingBox(
        zoomBox: IBoundingBox,
        getPositionAndZoom?: (arg: IPosition & Zoom) => void
    ): void {
        const size = this.getWorkspaceSize();

        const positionAndZoom = this.zoomControlService.calculateBoundingBox(
            zoomBox,
            size,
            this.workspace.canvasSize,
            this.workspace.getPositionRelativeToWorkspace(zoomBox),
            this.workspace.canvasLayerComponent.getPosition(),
            this.zoom
        );

        if (getPositionAndZoom) {
            getPositionAndZoom(positionAndZoom);
        } else {
            if (this.isMediaLibraryOpen) {
                positionAndZoom.x +=
                    this.workspace.designView.mediaLibrary.host.nativeElement.offsetWidth;
            }
            this.setZoomAndPosition(positionAndZoom);
        }
    }

    private reset = (): void => {
        this.workspace.isZooming = false;
        this.removeOverlay();
    };

    private setZoomAndPosition = ({ x, y, zoom }: IPosition & Zoom): void => {
        if (zoom <= 60 && !this.mouseOverTimeline) {
            this.workspace.canvasLayerComponent.setPosition({ x, y });
            this.workspace.setZoom(parseFloat(zoom.toFixed(2)));
        }
    };

    private getWorkspaceSize(): ISize {
        const size = this.workspace.host.nativeElement.getBoundingClientRect() as ISize;

        const { resizeExpandedHeight, defaultHeight, collapsed } = this.studioTimelineService;

        let timelineHeight = resizeExpandedHeight || defaultHeight;
        if (collapsed) {
            timelineHeight = DEFAULT_TIMELINE_COLLAPSED_HEIGHT;
        }

        size.height -= timelineHeight;

        return size;
    }

    private toCursor(zoom: number, event: PointerEvent): void {
        const { x, y } = this.workspace.getMousePositionRelativeToWorkspace(event);
        const position = this.zoomControlService.shouldSnap(zoom)
            ? this.calculateNewPosition(1, x, y)
            : this.calculateNewPosition(zoom, x, y);
        this.setZoomAndPosition(position);
    }

    private calculateNewPosition(zoom: number, x = 0, y = 0): IPosition & Zoom {
        const canvasPosition = this.workspace.canvasLayerComponent.getPosition();
        const ratio = 1 - zoom / this.zoom;
        const newPosition = this.zoomControlService.calculateNewCanvasPosition(
            canvasPosition,
            { x, y },
            ratio,
            this.workspace.canvasSize
        );
        return { ...newPosition, zoom };
    }
}
