:host {
    display: block;
    position: relative;
    float: right;
    z-index: 1001;
    margin: 10px;
    user-select: none;
}

.wrapper {
    display: grid;
    grid-template-columns: auto auto auto;
    height: 15px;
    align-items: center;
    justify-items: center;
    font-size: 6px;
    grid-gap: 5px;

    .zoom-input {
        width: 39px;
    }

    .zoom-control {
        display: flex;
        height: 15px;
        justify-content: center;
        align-items: center;
    }

    ui-number-input {
        ::ng-deep input {
            height: 15px;
            padding: 0.5rem 0.5rem;
        }

        ::ng-deep .unit-label {
            line-height: 14px;
        }

        &:hover {
            ::ng-deep .unit-label {
                display: block;
            }
        }
    }
}
