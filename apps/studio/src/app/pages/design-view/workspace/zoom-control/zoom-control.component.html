<div class="wrapper">
    <span
        class="zoom-control"
        id="zoom-btn-minus"
        (click)="decrease()">
        <svg
            width="11px"
            height="3px"
            viewBox="0 0 11 3"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink">
            <defs></defs>
            <g
                id="Design-View"
                stroke="none"
                stroke-width="1"
                fill="none"
                fill-rule="evenodd"
                fill-opacity="0.15"
                stroke-linejoin="round"
                stroke-opacity="0.3">
                <g
                    id="Comp-/-Zoom"
                    transform="translate(-2.000000, -7.000000)"
                    fill="#000000"
                    fill-rule="nonzero"
                    stroke="#F5F5F5">
                    <rect
                        id="Mask"
                        x="2.5"
                        y="7.5"
                        width="10"
                        height="2"></rect>
                </g>
            </g>
        </svg>
    </span>
    <ui-number-input
        class="zoom-input"
        id="zoom-input"
        #numberInput
        [(value)]="zoom"
        (valueChange)="updateZoom()"
        (focus)="focusHandler()"
        (blur)="blurHandler()"
        (submit)="updateZoom()"
        [allowEmpty]="false"
        [selectTextOnFocus]="true"
        [min]="1"
        [max]="3200"
        [disableUndo]="true"
        [arrowButtons]="false"
        [keyboardEmit]="true"
        [unitLabel]="hideUnitlabel ? '' : '%'">
    </ui-number-input>
    <span
        class="zoom-control"
        id="zoom-btn-plus"
        (click)="increase()">
        <svg
            width="11px"
            height="11px"
            viewBox="0 0 11 11"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink">
            <defs></defs>
            <g
                id="Design-View"
                stroke="none"
                stroke-width="1"
                fill="none"
                fill-rule="evenodd"
                fill-opacity="0.15"
                stroke-linejoin="round"
                stroke-opacity="0.3">
                <g
                    id="Comp-/-Zoom"
                    transform="translate(-60.000000, -3.000000)"
                    fill="#000000"
                    stroke="#F5F5F5">
                    <path
                        d="M66.5,7.5 L70.5,7.5 L70.5,9.5 L66.5,9.5 L66.5,13.5 L64.5,13.5 L64.5,9.5 L60.5,9.5 L60.5,7.5 L64.5,7.5 L64.5,3.5 L66.5,3.5 L66.5,7.5 Z"
                        id="Mask"></path>
                </g>
            </g>
        </svg>
    </span>
</div>
