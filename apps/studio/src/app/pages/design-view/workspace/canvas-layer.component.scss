@use 'variables' as *;

@-webkit-keyframes canvas-creative-show {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@-moz-keyframes canvas-creative-show {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@-ms-keyframes canvas-creative-show {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@-o-keyframes canvas-creative-show {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes canvas-creative-show {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

:host {
    display: block;
    position: absolute;
    z-index: 0;

    &.hidden {
        opacity: 0;
    }
}

.creative {
    width: 100%;
    height: 100%;

    opacity: 0;
    animation: canvas-creative-show 0.2s ease 0.2s;
    animation-fill-mode: forwards;

    background: var(--studio-color-background-second);
    background-image: $chessBackgroundUrl;

    ::ng-deep .in-test & {
        opacity: 1;
        animation-duration: 0 !important;
        transition-duration: 0 !important;
    }

    box-shadow: 0 0 0 1px transparent;

    ::ng-deep {
        .playing {
            cursor: pointer;
            contain: strict;
        }

        .devtools-inspect {
            &::after {
                display: block;
                content: '';
                background: rgb(27 117 202 / 25%);
                width: 100%;
                height: 100%;
                position: absolute;
                pointer-events: none;
                top: 0;
                left: 0;
            }
        }
    }

    &.transparent {
        background: transparent;
    }
}
