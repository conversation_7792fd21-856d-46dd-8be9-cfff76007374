@if (showOverlay$ | async) {
    @if (selectedPlacement$ | async; as selectedPlacement) {
        @switch ((selectedNetwork$ | async)?.type) {
            @case (SocialNetworkType.Meta) {
                <meta-overlay [selectedPlacement]="selectedPlacement"> </meta-overlay>
            }
            @case (SocialNetworkType.TikTok) {
                <tiktok-overlay [selectedPlacement]="selectedPlacement"> </tiktok-overlay>
            }
            @case (SocialNetworkType.Pinterest) {
                <pinterest-overlay [selectedPlacement]="selectedPlacement"> </pinterest-overlay>
            }
            @case (SocialNetworkType.Snapchat) {
                <snapchat-overlay [selectedPlacement]="selectedPlacement"> </snapchat-overlay>
            }
        }
    }
}
