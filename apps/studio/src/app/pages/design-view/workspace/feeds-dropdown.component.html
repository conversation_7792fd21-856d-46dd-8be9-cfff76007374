<span style="display: none">{{ feeds | json }}</span>

@for (feed of feeds; track feed; let i = $index) {
    @if (feed.children && feed.children.length > 0) {
        <ui-dropdown-item
            (mouseenter)="openDropdown()"
            #dropdownOpenTrigger="uiDropdownTarget"
            [uiDropdownTarget]="dynamicContentMenu">
            {{ feed.name }}
            <ui-dropdown
                #dynamicContentMenu
                (mouseleave)="closeDropdown()"
                [useTargetWidth]="false"
                width="160">
                <feeds-dropdown
                    [feeds]="feed.children"
                    #dropdownContent></feeds-dropdown>
            </ui-dropdown>
        </ui-dropdown-item>
    }
    @if (!feed.children || feed.children.length === 0) {
        <ui-dropdown-item>
            {{ feed.name }}
        </ui-dropdown-item>
    }
}
