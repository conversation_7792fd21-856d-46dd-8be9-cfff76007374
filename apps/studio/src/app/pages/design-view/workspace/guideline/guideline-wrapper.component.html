<!-- Guideline Top -->
@if (workspace.gizmoDrawer.drawGuideline) {
    <workspace-guideline
        (dragStart)="addGuideline($event)"
        (tooltip)="onDrawTooltip($event)"
        position="top"
        [style.visibility]="workspace.designView.editingElement ? 'hidden' : ''"></workspace-guideline>
}
<!-- Guideline Left -->
@if (workspace.gizmoDrawer.drawGuideline) {
    <workspace-guideline
        (dragStart)="addGuideline($event)"
        (tooltip)="onDrawTooltip($event)"
        position="left"
        [ngStyle]="{ left: isMediaLibraryOpen ? 200 + 'px' : '0' }">
    </workspace-guideline>
}
<!-- Guideline Right -->
@if (workspace.gizmoDrawer.drawGuideline) {
    <workspace-guideline
        (dragStart)="addGuideline($event)"
        (tooltip)="onDrawTooltip($event)"
        position="right"></workspace-guideline>
}
