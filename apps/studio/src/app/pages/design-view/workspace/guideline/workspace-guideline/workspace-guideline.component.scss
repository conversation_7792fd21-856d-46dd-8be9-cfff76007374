:host {
    z-index: 200;
    user-select: none;
    position: absolute;

    &.position-left {
        left: 0;
        top: 0;
        height: 100%;
    }

    &.position-right {
        right: 0;
        top: 0;
        height: 100%;
    }

    &.position-top {
        top: 0;
        width: 100%;
    }
}

.guideline-trigger {
    display: grid;
    position: absolute;
    z-index: 999;
    user-select: none;

    &.horizontal {
        width: 100%;
        height: 8px;
        top: -2px;
    }

    &.vertical {
        width: 8px;
        height: 100%;
        right: -4px;

        &.right {
            position: relative;
            float: right;
            left: 4px;
            right: auto;
        }
    }

    .tooltip {
        justify-self: center;
        align-self: center;
        height: 50px;
        width: 50px;
    }
}
