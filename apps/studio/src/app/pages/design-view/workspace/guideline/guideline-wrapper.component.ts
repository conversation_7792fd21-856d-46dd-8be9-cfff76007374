import { Component, Input, OnDestroy } from '@angular/core';
import { IPosition } from '@domain/dimension';
import { GuidelineType, TransformMode } from '@domain/workspace';
import { GuidelineTrigger } from '@studio/domain/workspace/guideline.models';
import { ActivityLoggerService } from '@studio/monitoring/activity-logger.service';
import { uuidv4 } from '@studio/utils/id';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { MediaLibraryService } from '../../../../shared/media-library/state/media-library.service';
import { EditorStateService } from '../../services/editor-state.service';
import { ElementSelectionService } from '../../services/element-selection.service';
import { StudioWorkspaceComponent } from '../studio-workspace.component';
import { WorkspaceTransformService } from '../workspace-transform.service';

type TooltipEvent = { show: boolean; type: GuidelineType; trigger: GuidelineTrigger };
@Component({
    selector: 'guideline-wrapper',
    templateUrl: './guideline-wrapper.component.html',
    standalone: false
})
export class GuidelineWrapperComponent implements OnDestroy {
    @Input() workspace: StudioWorkspaceComponent;

    isMediaLibraryOpen?: boolean;

    unsubscribe$ = new Subject<void>();

    constructor(
        private activityLoggerService: ActivityLoggerService,
        private mediaLibraryService: MediaLibraryService,
        private editorStateService: EditorStateService,
        private elementSelectionService: ElementSelectionService,
        private workspaceTransformService: WorkspaceTransformService
    ) {
        this.mediaLibraryService.isOpen$.pipe(takeUntil(this.unsubscribe$)).subscribe(isOpen => {
            this.isMediaLibraryOpen = isOpen;
        });
    }

    addGuideline({ type, event }: { type: GuidelineType; event: MouseEvent }): void {
        const canvasMousePosition = this.workspace.canvasMousePosition;

        if (!canvasMousePosition || !this.workspace.gizmoDrawer.drawGuideline) {
            return;
        }

        const x = Math.round(canvasMousePosition.x);
        const y = Math.round(canvasMousePosition.y);
        this.activityLoggerService.log('Start adding guideline on canvas.', { x, y });
        this.elementSelectionService.clearSelection();
        this.editorStateService.creativeDataNode.guidelines =
            this.editorStateService.creativeDataNode.guidelines.filter(({ preview }) => !preview);
        this.workspace.previewGuidelinePresent = false;
        this.workspace.gizmoDrawer.draw();

        /**
         * Save the snapshot of current state before adding the guideline and ignore
         * the change emitters from the transformbinder.
         * Needed in order to be able to properly Undo (CTRL+Z) new guidelines (they should be removed)
         */
        this.workspace.ignoreGuidelineChange = true;

        this.workspaceTransformService.setTransformMode(TransformMode.Guidelines);
        this.editorStateService.creativeDataNode.guidelines.push({
            id: uuidv4(),
            type,
            position: {
                x: type === GuidelineType.Vertical ? x : 0,
                y: type === GuidelineType.Horizontal ? y : 0
            },
            preview: false
        });

        this.editorStateService.creativeDataNode.guidelines =
            this.editorStateService.creativeDataNode.guidelines;

        this.workspace.transform.setGuidelines(this.workspace.getMousePositionRelativeToCanvas(event));

        window.addEventListener('mouseup', this.onStopAddingGuideline);
    }

    onStopAddingGuideline = (event: MouseEvent): void => {
        const { x, y } = event;
        this.activityLoggerService.log('Stop adding guideline on canvas at position', { x, y });
        this.workspace.ignoreGuidelineChange = false;
        // Remove preview guidelines
        this.editorStateService.creativeDataNode.guidelines =
            this.editorStateService.creativeDataNode.guidelines.filter(g => !g.preview);

        window.removeEventListener('mouseup', this.onStopAddingGuideline);
    };

    onDrawTooltip({ show, type, trigger }: TooltipEvent): void {
        if (this.workspace.gizmoDrawer.drawGuideline) {
            this.workspace.gizmoDrawer.drawGuidelineTooltip = show;
            if (!this.workspace.disableGuidelinePreview) {
                const position = this.getPreviewGuidelinePosition(trigger);

                if (show && !this.workspace.previewGuidelinePresent) {
                    this.editorStateService.creativeDataNode.guidelines.push({
                        type: type,
                        position: position,
                        id: uuidv4(),
                        preview: true
                    });
                    this.workspace.previewGuidelinePresent = true;
                } else if (this.workspace.previewGuidelinePresent && !show) {
                    this.editorStateService.creativeDataNode.guidelines =
                        this.editorStateService.creativeDataNode.guidelines.filter(x => !x.preview);
                    this.workspace.previewGuidelinePresent = false;
                }
            }
            this.workspace.gizmoDrawer.draw();
        }
    }

    private getPreviewGuidelinePosition(trigger: GuidelineTrigger): IPosition {
        if (trigger === GuidelineTrigger.TOP) {
            return this.workspace.getPositionRelativeToCanvas({ x: 0, y: 1 });
        } else if (trigger === GuidelineTrigger.LEFT) {
            if (this.isMediaLibraryOpen) {
                return this.workspace.getPositionRelativeToCanvas({ x: 201, y: 0 });
            }
            return this.workspace.getPositionRelativeToCanvas({ x: 1, y: 0 });
        } else {
            const { width } = this.workspace.host.nativeElement.getBoundingClientRect();
            return this.workspace.getPositionRelativeToCanvas({ x: Math.round(width) - 2, y: 0 });
        }
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next();
        this.unsubscribe$.complete();
    }
}
