import { Component, EventEmitter, Input, OnDestroy, Output } from '@angular/core';
import { GuidelineType } from '@domain/workspace';
import { GuidelineTrigger, IGuidelineTooltipEvent } from '@studio/domain/workspace/guideline.models';

@Component({
    selector: 'workspace-guideline',
    templateUrl: './workspace-guideline.component.html',
    styleUrls: ['./workspace-guideline.component.scss'],
    host: {
        '[class]': '"position-" + position'
    },
    standalone: false
})
export class WorkspaceGuidelineComponent implements OnDestroy {
    @Output() dragStart = new EventEmitter<{ type: GuidelineType; event: MouseEvent }>();
    @Output() tooltip = new EventEmitter<IGuidelineTooltipEvent>();
    @Input() position: 'left' | 'top' | 'right' = 'left';

    GuidelineTriggers = GuidelineTrigger;
    GuidelineType = GuidelineType;
    guidelineActive: boolean;
    timeout: any;

    onDragStart(type: GuidelineType, event: MouseEvent): void {
        this.dragStart.next({ type, event });
    }

    openTooltip(show: boolean, type: GuidelineType, trigger: GuidelineTrigger): void {
        if (!show) {
            clearTimeout(this.timeout);
        }

        if (this.guidelineActive) {
            this.tooltip.next({ show, type: type, trigger });
        } else {
            this.timeout = setTimeout(() => this.tooltip.next({ show, type: type, trigger }), 300);
        }

        this.guidelineActive = show;
    }

    ngOnDestroy(): void {
        clearTimeout(this.timeout);
    }
}
