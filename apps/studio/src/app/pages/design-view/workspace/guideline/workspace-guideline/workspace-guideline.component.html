@if (position === 'top') {
    <div
        appGuideline
        (dragStart)="onDragStart(GuidelineType.Horizontal, $event)"
        (tooltip)="openTooltip($event, GuidelineType.Horizontal, GuidelineTriggers.TOP)"
        class="guideline-trigger horizontal"
        [class.resize-90]="guidelineActive"></div>
}

@if (position === 'left') {
    <div
        appGuideline
        (dragStart)="onDragStart(GuidelineType.Vertical, $event)"
        (tooltip)="openTooltip($event, GuidelineType.Vertical, GuidelineTriggers.LEFT)"
        class="guideline-trigger vertical"
        [class.resize-0]="guidelineActive"></div>
}

@if (position === 'right') {
    <div
        appGuideline
        (dragStart)="onDragStart(GuidelineType.Vertical, $event)"
        (tooltip)="openTooltip($event, GuidelineType.Vertical, GuidelineTriggers.RIGHT)"
        class="guideline-trigger vertical right"
        [class.resize-0]="guidelineActive"></div>
}
