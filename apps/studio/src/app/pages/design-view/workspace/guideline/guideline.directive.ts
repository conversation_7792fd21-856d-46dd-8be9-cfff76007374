import { Directive, Output, EventEmitter, HostListener } from '@angular/core';

@Directive({
    selector: '[appGuideline]',
    standalone: false
})
export class GuidelineDirective {
    @Output() dragStart = new EventEmitter<MouseEvent>();
    @Output() tooltip = new EventEmitter<boolean>();

    @HostListener('pointerdown', ['$event'])
    onPointerDown(event: MouseEvent): void {
        this.dragStart.emit(event);
    }

    @HostListener('mousemove')
    onMouseenter(): void {
        this.tooltip.emit(true);
    }

    @HostListener('mouseleave')
    onMouseleave(): void {
        this.tooltip.emit(false);
    }
}
