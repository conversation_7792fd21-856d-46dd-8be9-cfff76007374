import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { UIModule } from '@bannerflow/ui';
import { SharedModule } from '../../../shared/shared.module';
import { AssetPickerDropdownComponent } from '../asset-picker/asset-picker-dropdown/asset-picker-dropdown.component';
import { CanvasLayerComponent } from './canvas-layer.component';
import * as CTX from './context-menu';
import { FeedsDropdownComponent } from './feeds-dropdown.component';
import { GizmoDrawSchedulerService } from './gizmo-draw-scheduler';
import { GuidelineWrapperComponent } from './guideline/guideline-wrapper.component';
import { GuidelineDirective } from './guideline/guideline.directive';
import { WorkspaceGuidelineComponent } from './guideline/workspace-guideline/workspace-guideline.component';
import { PreviewDirective } from './preview.directive';
import { WorkspaceUploadAssetService } from './services/workspace-upload-asset.service';
import * as Social from './social-overlay';
import { StudioWorkspaceComponent } from './studio-workspace.component';
import { ZoomControlModule } from './zoom-control/zoom-control.module';

@NgModule({
    imports: [CommonModule, SharedModule, ZoomControlModule, UIModule, AssetPickerDropdownComponent],
    providers: [WorkspaceUploadAssetService, GizmoDrawSchedulerService],
    declarations: [
        StudioWorkspaceComponent,
        CanvasLayerComponent,
        PreviewDirective,
        WorkspaceGuidelineComponent,
        GuidelineDirective,
        GuidelineWrapperComponent,
        FeedsDropdownComponent,
        CTX.ContextMenuComponent,
        CTX.ElementMenuComponent,
        CTX.CanvasMenuComponent,
        CTX.AnimationMenuComponent,
        CTX.StateMenuComponent,
        Social.SocialOverlayComponent,
        Social.MetaOverlayComponent,
        Social.TiktokOverlayComponent,
        Social.PinterestComponent,
        Social.SnapchatOverlayComponent
    ],
    exports: [StudioWorkspaceComponent]
})
export class WorkspaceModule {}
