import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { provideFeatureFlags } from '@bannerflow/feature-flags';
import { createCreativeDataNodeMock } from '@creative/nodes/__tests__/mocks/data-node.mock';
import { registerDependency } from '@di/di';
import { Token } from '@di/di.token';
import { IElement } from '@domain/creativeset';
import { VersionedElementProperty } from '@domain/creativeset/version';
import { ElementKind } from '@domain/elements';
import { ITextElementDataNode } from '@domain/nodes';
import { IWidgetElementDataNode } from '@domain/widget';
import { createCreativeEnvironmentMock } from '@fixtures/environment.fixture';
import { createRendererFixture } from '@fixtures/renderer.fixture';
import { createBrandManagerBrandMock } from '@mocks/brand.mock';
import { createCreativesetMock } from '@mocks/creativeset.mock';
import { createDesignMock } from '@mocks/design.mock';
import { createMockEditorStateService } from '@mocks/editor.mock';
import { createDataNodeMock, createElementMock, createElementPropertyMock } from '@mocks/element.mock';
import { createCustomPropertyMock } from '@mocks/property.mock';
import { createFontFamiliesServiceMock } from '@mocks/services/font-families-service.mock';
import { createSizeMock } from '@mocks/size.mock';
import { createVersionMock, createVersionPropertyMock } from '@mocks/version.mock';
import { CreativesetDataService, UserService } from '@studio/common';
import { provideEnvironment } from '@studio/common/environment/environment.provider';
import { FontFamiliesService } from '@studio/common/font-families';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { ApolloTestingModule } from 'apollo-angular/testing';
import { of } from 'rxjs';
import { environment } from '../../../../../environments/environment.test';
import { defineMatchMedia } from '../../../../shared/mocks/matchMedia.mock';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../../../shared/mocks/store.mock';
import { NodeCreatorService } from '../../../../shared/services/data-node-creator.service';
import { VersionsService } from '../../../../shared/versions/state/versions.service';
import { EditorEventService } from '../../services/editor-event/editor-event.service';
import { EditorStateService } from '../../services/editor-state.service';
import { ElementCreatorService } from '../../services/element-creator.service';
import { ElementSelectionService } from '../../services/element-selection.service';
import { MutatorService } from '../../services/mutator.service';
import { StudioWorkspaceService } from './studio-workspace.service';

describe('StudioWorkspaceService', () => {
    let service: StudioWorkspaceService;
    let textElementMock: IElement;
    let textNodeMock: ITextElementDataNode;
    let widgetElementMock: IElement;
    let widgetNodeMock: IWidgetElementDataNode;

    beforeEach(() => {
        defineMatchMedia();
        registerDependency(Token.CREATIVE_CONFIG, createCreativeEnvironmentMock());
        textElementMock = createElementMock(ElementKind.Text, {
            properties: [createElementPropertyMock({ versionPropertyId: 'id' })]
        });
        textNodeMock = createDataNodeMock({
            kind: ElementKind.Text,
            fontSize: 20,
            id: textElementMock.id
        });
        widgetElementMock = createElementMock(ElementKind.Widget, {
            properties: [createElementPropertyMock({ versionPropertyId: 'vpId' })],
            id: 'widgetId'
        });
        widgetNodeMock = createDataNodeMock({
            kind: ElementKind.Widget,
            customProperties: [createCustomPropertyMock({ versionPropertyId: 'vpId' })],
            id: widgetElementMock.id,
            globalElement: widgetElementMock
        });
        const designMock = createDesignMock({
            elements: [textElementMock, widgetElementMock],
            document: createCreativeDataNodeMock({ elements: [textNodeMock, widgetNodeMock] })
        });
        const versionsMock = [
            createVersionMock({ properties: [createVersionPropertyMock({ id: 'vpId' })] })
        ];

        const creativesetMock = createCreativesetMock({
            designs: [designMock, { ...designMock, id: 'design2' }],
            elements: [...designMock.elements],
            versions: versionsMock
        });

        const versionMock = creativesetMock.versions[0];

        TestBed.configureTestingModule({
            declarations: [],
            imports: [HttpClientTestingModule, ApolloTestingModule],
            providers: [
                provideEnvironment(environment),
                EnvironmentService,
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER,
                StudioWorkspaceService,
                VersionsService,
                ElementSelectionService,
                ElementCreatorService,
                NodeCreatorService,
                { provide: EditorEventService, useValue: new EditorEventService() },
                {
                    provide: EditorStateService,
                    useValue: {
                        ...createMockEditorStateService({
                            size: createSizeMock({ width: 1200, height: 600 }),
                            creativeDataNode: designMock.document,
                            defaultVersionProperties: creativesetMock.versions.flatMap(
                                ({ properties }) => properties
                            ),
                            currentVersion: {
                                id: +versionMock.id,
                                localizationId: versionMock.localization.id.toString(),
                                name: versionMock.name,
                                targetUrl: versionMock.targetUrl,
                                elements: {}
                            },
                            propertyAsVersionableProperty: (prop, _unit) => {
                                prop.value = undefined;
                                prop.versionPropertyId = 'id';
                                return prop as VersionedElementProperty;
                            },
                            renderer: {
                                ...createRendererFixture(1200, 600, []),
                                creativeDocument: designMock.document,
                                destroyElement_m: jest.fn()
                            }
                        })
                    }
                },
                {
                    provide: CreativesetDataService,
                    useValue: {
                        creativeset: creativesetMock,
                        brand: createBrandManagerBrandMock({ id: creativesetMock.brandId }),
                        versions: versionsMock
                    }
                },
                {
                    provide: MutatorService,
                    useValue: {
                        removeSelection: jest.fn(),
                        renderer: { updateElementOrder_m: jest.fn() }
                    }
                },
                { provide: FontFamiliesService, useValue: createFontFamiliesServiceMock() },
                {
                    provide: UserService,
                    useValue: {
                        isEmployee$: of(false)
                    }
                },
                provideFeatureFlags({
                    enabled: false
                })
            ]
        }).compileComponents();
        service = TestBed.inject(StudioWorkspaceService);
    });

    describe('detach text', () => {
        it('should not rescale text size and should select the new node when detaching', async () => {
            const elementSelectionService = TestBed.inject(ElementSelectionService);
            elementSelectionService.change$.subscribe();
            elementSelectionService.setSelection(textNodeMock);
            expect(textNodeMock.fontSize).toBe(20);
            const detachElementSpy = jest.spyOn(
                service as StudioWorkspaceService & { detachElement: () => void },
                'detachElement'
            );
            await service.detach([textNodeMock]);
            const newTextNode = await detachElementSpy.mock.results[0].value;
            expect(newTextNode.fontSize).toBe(20);
            expect(elementSelectionService.currentSelection.element?.id).toBe(newTextNode.id);
        });
    });

    describe('detach widget', () => {
        it('should get updated versionPropertyId when detaching widget element properties', () => {
            expect(widgetNodeMock.customProperties[0].versionPropertyId).toBe('vpId');
            service['detachElementProperties'](widgetNodeMock);
            expect(widgetNodeMock.customProperties[0].versionPropertyId).toBe('id');
        });
    });
});
