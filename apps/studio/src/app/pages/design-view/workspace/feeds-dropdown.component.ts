import { Component, ElementRef, Input, ViewChild } from '@angular/core';
import { UIDropdownComponent, UIDropdownTargetDirective } from '@bannerflow/ui';
import { IBfFeed } from '@domain/feed';

@Component({
    selector: 'feeds-dropdown',
    templateUrl: 'feeds-dropdown.component.html',
    standalone: false
})
export class FeedsDropdownComponent {
    @Input() feeds: IBfFeed[] | undefined;
    @ViewChild('dynamicContentMenu') dynamicContentMenu: UIDropdownComponent;
    @ViewChild('dropdownOpenTrigger') dropdownOpenTrigger: UIDropdownTargetDirective;
    @ViewChild('dropdownContent') dropdownContent: ElementRef;
    openDropdown(): void {
        this.dropdownOpenTrigger.openDropdown();
    }

    closeDropdown(): void {
        this.dropdownOpenTrigger.closeDropdown();
    }
}
