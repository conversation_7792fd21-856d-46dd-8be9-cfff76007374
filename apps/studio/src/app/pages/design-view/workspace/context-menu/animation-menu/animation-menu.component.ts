import { Component, inject, OnDestroy, ViewChild } from '@angular/core';
import { UIDropdownComponent, UIDropdownTargetDirective, UINewThemeService } from '@bannerflow/ui';
import {
    getAnimationsOfType,
    hasAnimationsOfType,
    isTransitionAnimation,
    isTransitionType
} from '@creative/animation.utils';
import { isGroupDataNode } from '@creative/nodes/helpers';
import { getHotkeysAsKeyValueList } from '@studio/hotkeys/hotkeys';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CopyPasteService } from '../../../services/copy-paste.service';
import { AnimationService, KeyframeService } from '../../../timeline';
import { ContextMenuComponent } from '../context-menu.component';

@Component({
    selector: 'animation-menu',
    templateUrl: './animation-menu.component.html',
    styleUrls: ['../context-menu.component.scss'],
    standalone: false
})
export class AnimationMenuComponent implements OnDestroy {
    private uiNewThemeService = inject(UINewThemeService);
    public contextMenu = inject(ContextMenuComponent);
    private keyframeService = inject(KeyframeService);
    private animationService = inject(AnimationService);
    private copyPasteService = inject(CopyPasteService);
    @ViewChild('menu') menu: UIDropdownComponent;
    @ViewChild('menuTrigger') menuTrigger: UIDropdownTargetDirective;
    unsubscribe$ = new Subject<void>();
    keyboardShortcuts = getHotkeysAsKeyValueList(['Editor', 'Workspace', 'Timeline']);
    keyframesSelected: number;
    copiedKeyframeSelection: number;
    hasSelectedAnimation: boolean;
    canAddKeyframe = false;
    isTransitionAnimation = false;
    elementHasAnimation: boolean;
    elementCanDistributeKeyframes: boolean;
    isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    constructor() {
        this.animationService.selectedAnimation$
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe(animation => {
                if (animation && this.keyframeService.keyframes.size > 0) {
                    this.hasSelectedAnimation = animation.keyframes.some(
                        kf => kf.id === [...this.keyframeService.keyframes][0].id
                    );
                } else {
                    this.hasSelectedAnimation = false;
                }
                if (animation) {
                    this.isTransitionAnimation = isTransitionAnimation(animation);
                }
                this.canAddKeyframe = !!animation && !isTransitionType(animation.type);
            });

        this.keyframeService.selectedKeyframes$
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe(keyframes => {
                this.keyframesSelected = keyframes.size;
            });

        this.animationService.change$.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {
            const snapshot = this.copyPasteService.copySnapshot;
            if (snapshot?.context === 'keyframe') {
                this.copiedKeyframeSelection = snapshot.keyframeSelection?.keyframes.length || 0;
                this.hasAnimation();
            }
        });
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next();
        this.unsubscribe$.complete();
    }

    hasAnimation(): void {
        const element = this.contextMenu.singleElementToChange;

        if (isGroupDataNode(element)) {
            return;
        }

        this.elementHasAnimation = Boolean(element && hasAnimationsOfType(element, 'keyframe'));

        this.elementCanDistributeKeyframes = Boolean(
            element &&
                getAnimationsOfType(element, 'keyframe').reduce(
                    (memo, animation) => memo + animation.keyframes.length,
                    0
                ) > 2
        );
    }

    addAnimation(): void {
        this.animationService.addAnimation$.next();
    }

    deleteAnimation(): void {
        this.animationService.deleteAnimations$.next();
    }

    deleteKeyframes(): void {
        this.keyframeService.deleteKeyframes$.next();
    }

    addKeyframe(withState?: boolean): void {
        this.keyframeService.addKeyframe$.next(withState);
    }

    copyKeyframes(): void {
        this.keyframeService.copyKeyframes$.next();
    }

    pasteKeyframes(): void {
        this.keyframeService.pasteKeyframes$.next();
    }

    distributeKeyframes(): void {
        this.keyframeService.distributeKeyframes$.next();
    }
}
