import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { provideFeatureFlags } from '@bannerflow/feature-flags';
import { UIModule } from '@bannerflow/ui';
import { ElementKind } from '@domain/elements';
import { createDataNodeMock } from '@mocks/element.mock';
import { createFontFamiliesServiceMock } from '@mocks/services/font-families-service.mock';
import { CreativesetDataService, FontFamiliesService, UserService } from '@studio/common';
import { provideEnvironment } from '@studio/common/environment/environment.provider';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { ApolloTestingModule } from 'apollo-angular/testing';
import { of } from 'rxjs';
import { environment } from '../../../../../../environments/environment.test';
import { AIStudioDialogService } from '../../../../../shared/ai-studio/ai-studio-dialog.service';
import { AIStudioService } from '../../../../../shared/ai-studio/ai-studio.service';
import { GenAIService } from '../../../../../shared/ai-studio/state/gen-ai.service';
import { BrandLibraryDataService } from '../../../../../shared/media-library/brand-library.data.service';
import { MediaLibraryService } from '../../../../../shared/media-library/state/media-library.service';
import { defineMatchMedia } from '../../../../../shared/mocks/matchMedia.mock';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../../../../shared/mocks/store.mock';
import { NodeCreatorService } from '../../../../../shared/services/data-node-creator.service';
import { GainsightService } from '../../../../../shared/services/gainsight.service';
import { HeavyVideoService } from '../../../../../shared/services/heavy-video.service/heavy-video.service';
import { AssetPickerComponent } from '../../../asset-picker/asset-picker.component';
import { DesignViewComponent } from '../../../design-view.component';
import { BrandLibraryElementService } from '../../../media-library/brandlibrary-element.service';
import { CopyPasteService } from '../../../services/copy-paste.service';
import { EditorEventService } from '../../../services/editor-event/editor-event.service';
import { EditorStateService } from '../../../services/editor-state.service';
import { ElementCreatorService } from '../../../services/element-creator.service';
import { ElementSelectionService } from '../../../services/element-selection.service';
import { HistoryService } from '../../../services/history.service';
import { MutatorService } from '../../../services/mutator.service';
import { VideoEditingService } from '../../../services/video-edit.service';
import {
    AnimationService,
    KeyframeService,
    TimelineElementService,
    TimelineScrollService
} from '../../../timeline/';
import { TimelineTransformService } from '../../../timeline/timeline-transformer.service';
import { StudioWorkspaceService } from '../../services/studio-workspace.service';
import { StudioWorkspaceComponent } from '../../studio-workspace.component';
import { ContextMenuComponent } from '../context-menu.component';
import { ElementMenuComponent } from './element-menu.component';

describe('ElementMenuComponent', () => {
    let component: ElementMenuComponent;
    let fixture: ComponentFixture<ElementMenuComponent>;
    let mutatorService: MutatorService;
    let elementSelectionService: ElementSelectionService;
    let heavyVideoService: HeavyVideoService;

    beforeEach(() => {
        defineMatchMedia();
        TestBed.configureTestingModule({
            imports: [UIModule, ApolloTestingModule],
            declarations: [ElementMenuComponent, AssetPickerComponent],
            providers: [
                provideEnvironment(environment),
                EnvironmentService,
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER,
                AIStudioDialogService,
                AnimationService,
                BrandLibraryDataService,
                BrandLibraryElementService,
                TimelineScrollService,
                NodeCreatorService,
                KeyframeService,
                AIStudioDialogService,
                AIStudioService,
                CopyPasteService,
                CreativesetDataService,
                EditorStateService,
                ElementCreatorService,
                ElementSelectionService,
                GainsightService,
                GenAIService,
                StudioWorkspaceService,
                TimelineElementService,
                TimelineTransformService,
                { provide: EditorEventService, useValue: new EditorEventService() },
                { provide: StudioWorkspaceComponent, useValue: {} },
                { provide: DesignViewComponent, useValue: {} },
                { provide: ContextMenuComponent, useValue: {} },
                { provide: HistoryService, useValue: {} },
                { provide: MutatorService, useValue: { setElementsVisibility: jest.fn() } },
                { provide: BrandLibraryElementService, useValue: {} },
                { provide: MediaLibraryService, useValue: {} },
                { provide: FontFamiliesService, useValue: createFontFamiliesServiceMock() },
                {
                    provide: UserService,
                    useValue: {
                        isEmployee$: of(false)
                    }
                },
                {
                    provide: HeavyVideoService,
                    useValue: {
                        promptHeavyVideoOnVisibilityChange: jest.fn()
                    }
                },
                {
                    provide: VideoEditingService,
                    useValue: {
                        isEditingFeatureEnabled: jest.fn().mockReturnValue(false),
                        isEditing: jest.fn().mockReturnValue(false),
                        split: jest.fn()
                    }
                },
                provideHttpClient(),
                provideHttpClientTesting(),
                provideFeatureFlags({
                    enabled: false
                })
            ],
            schemas: [NO_ERRORS_SCHEMA]
        });

        fixture = TestBed.createComponent(ElementMenuComponent);
        mutatorService = TestBed.inject(MutatorService);
        heavyVideoService = TestBed.inject(HeavyVideoService);
        elementSelectionService = TestBed.inject(ElementSelectionService);
        component = fixture.componentInstance;
    });

    describe('Toggle visibility', () => {
        it('should do nothing if selection is empty', async () => {
            elementSelectionService.setSelection();

            const setElementsVisibilitySpy = jest.spyOn(mutatorService, 'setElementsVisibility');
            await component.toggleVisibility();
            expect(setElementsVisibilitySpy).not.toHaveBeenCalled();
        });

        it('should toggle hidden visibility of selected elements', async () => {
            const visibleNode = createDataNodeMock({ kind: ElementKind.Ellipse, hidden: false });
            const visibleParentNode = createDataNodeMock({
                kind: ElementKind.Group,
                hidden: false,
                nodes: [visibleNode],
                elements: [visibleNode]
            });
            visibleNode.__parentNode = visibleParentNode;
            const selections = [visibleParentNode, visibleNode];
            jest.spyOn(heavyVideoService, 'promptHeavyVideoOnVisibilityChange').mockImplementationOnce(
                nodes => Promise.resolve(nodes)
            );

            elementSelectionService.setSelection(...selections);

            const setElementsVisibilitySpy = jest.spyOn(mutatorService, 'setElementsVisibility');
            await component.toggleVisibility();
            expect(setElementsVisibilitySpy).toHaveBeenCalledWith([visibleParentNode], true);
        });

        it('should toggle show visibility of selected elements', async () => {
            const hiddenNode = createDataNodeMock({ hidden: true });
            const hiddenParentNode = createDataNodeMock({
                kind: ElementKind.Group,
                hidden: true,
                nodes: [hiddenNode],
                elements: [hiddenNode]
            });
            hiddenNode.__parentNode = hiddenParentNode;
            const selections = [hiddenParentNode, hiddenNode];
            jest.spyOn(heavyVideoService, 'promptHeavyVideoOnVisibilityChange').mockImplementationOnce(
                nodes => Promise.resolve(nodes)
            );

            elementSelectionService.setSelection(...selections);

            const setElementsVisibilitySpy = jest.spyOn(mutatorService, 'setElementsVisibility');
            await component.toggleVisibility();
            expect(setElementsVisibilitySpy).toHaveBeenCalledWith([hiddenParentNode], false);
        });

        it('should toggle hidden when selected elements contains both visible/hidden elements', async () => {
            const hiddenNode = createDataNodeMock({ hidden: true });
            const visibleNode = createDataNodeMock({ hidden: false });
            const selections = [visibleNode, hiddenNode];
            jest.spyOn(heavyVideoService, 'promptHeavyVideoOnVisibilityChange').mockImplementationOnce(
                nodes => Promise.resolve(nodes)
            );

            elementSelectionService.setSelection(...selections);

            const setElementsVisibilitySpy = jest.spyOn(mutatorService, 'setElementsVisibility');
            await component.toggleVisibility();
            expect(setElementsVisibilitySpy).toHaveBeenCalledWith([visibleNode], true);
        });

        it('should not toggle hidden heavy video element user does not accept the heavy video prompt', async () => {
            const hiddenNode = createDataNodeMock({ hidden: true });
            const visibleNode = createDataNodeMock({ hidden: false });
            const selections = [visibleNode, hiddenNode];
            jest.spyOn(heavyVideoService, 'promptHeavyVideoOnVisibilityChange').mockImplementationOnce(
                () => Promise.resolve([visibleNode])
            );

            elementSelectionService.setSelection(...selections);

            const setElementsVisibilitySpy = jest.spyOn(mutatorService, 'setElementsVisibility');
            await component.toggleVisibility();
            expect(setElementsVisibilitySpy).toHaveBeenCalledWith([visibleNode], true);
        });
    });
});
