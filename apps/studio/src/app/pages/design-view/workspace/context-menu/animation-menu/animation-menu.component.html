<div
    class="dropdown-trigger"
    #menuTrigger="uiDropdownTarget"
    [uiDropdownTarget]="menu"
    [hasBackdrop]="false"
    (dropdownClosed)="contextMenu.contextMenuClosed()"></div>

<ui-dropdown
    #menu
    size="sm"
    [width]="isNewUI() ? '180' : 'auto'"
    type="menu">
    @if (isNewUI()) {
        <ui-dropdown-item
            id="add-keyframe"
            [disabled]="!canAddKeyframe"
            (click)="addKeyframe()">
            Add keyframe
        </ui-dropdown-item>
        <ui-dropdown-item
            id="add-keyframe"
            [disabled]="!canAddKeyframe"
            (click)="addKeyframe(false)">
            Add empty keyframe
        </ui-dropdown-item>
        <ui-dropdown-item
            [disabled]="(!keyframesSelected && !hasSelectedAnimation) || isTransitionAnimation"
            [label]="keyboardShortcuts.Editor.DeleteElement"
            (click)="deleteKeyframes()">
            Delete {{ keyframesSelected > 1 ? 'keyframes' : 'keyframe' }}
        </ui-dropdown-item>
        <ui-dropdown-item
            [disabled]="(!keyframesSelected && !hasSelectedAnimation) || isTransitionAnimation"
            (click)="copyKeyframes()"
            [label]="keyboardShortcuts.Editor.Copy">
            Copy {{ keyframesSelected > 1 ? 'keyframes' : 'keyframe' }}
        </ui-dropdown-item>
        <ui-dropdown-item
            [disabled]="!copiedKeyframeSelection || isTransitionAnimation"
            (click)="pasteKeyframes()"
            [label]="keyboardShortcuts.Editor.Paste">
            Paste {{ copiedKeyframeSelection > 1 ? 'keyframes' : 'keyframe' }}
        </ui-dropdown-item>
        <ui-dropdown-divider></ui-dropdown-divider>
        <ui-dropdown-item
            [disabled]="!elementCanDistributeKeyframes"
            (click)="distributeKeyframes()">
            Distribute {{ keyframesSelected >= 3 ? 'selected' : 'all' }} keyframes
        </ui-dropdown-item>
        <ui-dropdown-divider></ui-dropdown-divider>
        <ui-dropdown-item
            [disabled]="elementHasAnimation"
            (click)="addAnimation()">
            Add animation
        </ui-dropdown-item>
        <ui-dropdown-item
            (click)="deleteAnimation()"
            [label]="keyboardShortcuts.Editor.DeleteElement">
            Delete animation
        </ui-dropdown-item>
    } @else {
        <ui-dropdown-item
            id="add-keyframe"
            class="custom-dropdown"
            [disabled]="!canAddKeyframe"
            (click)="addKeyframe()">
            <div class="custom-item">
                <div class="custom-column"></div>
                <div class="custom-text">Add keyframe</div>
            </div>
        </ui-dropdown-item>
        <ui-dropdown-item
            id="add-keyframe"
            class="custom-dropdown"
            [disabled]="!canAddKeyframe"
            (click)="addKeyframe(false)">
            <div class="custom-item">
                <div class="custom-column"></div>
                <div class="custom-text">Add empty keyframe</div>
            </div>
        </ui-dropdown-item>
        <ui-dropdown-item
            class="custom-dropdown"
            [disabled]="(!keyframesSelected && !hasSelectedAnimation) || isTransitionAnimation"
            (click)="deleteKeyframes()">
            <div class="custom-item">
                <div class="custom-column"></div>
                <div class="custom-text">
                    Delete {{ keyframesSelected > 1 ? 'keyframes' : 'keyframe' }}
                </div>
                <div [class]="contextMenu.isMac ? 'mac custom-action' : 'custom-action'">
                    {{ keyboardShortcuts.Editor.DeleteElement }}
                </div>
            </div>
        </ui-dropdown-item>
        <ui-dropdown-item
            class="custom-dropdown"
            [disabled]="(!keyframesSelected && !hasSelectedAnimation) || isTransitionAnimation"
            (click)="copyKeyframes()">
            <div class="custom-item">
                <div class="custom-column"></div>
                <div class="custom-text">
                    Copy {{ keyframesSelected > 1 ? 'keyframes' : 'keyframe' }}
                </div>
                <div [class]="contextMenu.isMac ? 'mac custom-action' : 'custom-action'">
                    {{ keyboardShortcuts.Editor.Copy }}
                </div>
            </div>
        </ui-dropdown-item>
        <ui-dropdown-item
            class="custom-dropdown"
            [disabled]="!copiedKeyframeSelection || isTransitionAnimation"
            (click)="pasteKeyframes()">
            <div class="custom-item">
                <div class="custom-column"></div>
                <div class="custom-text">
                    Paste {{ copiedKeyframeSelection > 1 ? 'keyframes' : 'keyframe' }}
                </div>
                <div [class]="contextMenu.isMac ? 'mac custom-action' : 'custom-action'">
                    {{ keyboardShortcuts.Editor.Paste }}
                </div>
            </div>
        </ui-dropdown-item>
        <ui-dropdown-divider></ui-dropdown-divider>
        <ui-dropdown-item
            class="custom-dropdown"
            [disabled]="!elementCanDistributeKeyframes"
            (click)="distributeKeyframes()">
            <div class="custom-item">
                <div class="custom-column"></div>
                <div class="custom-text">
                    Distribute {{ keyframesSelected >= 3 ? 'selected' : 'all' }} keyframes
                </div>
            </div>
        </ui-dropdown-item>
        <ui-dropdown-divider></ui-dropdown-divider>
        <ui-dropdown-item
            class="custom-dropdown"
            [disabled]="elementHasAnimation"
            (click)="addAnimation()">
            <div class="custom-item">
                <div class="custom-column"></div>
                <div class="custom-text">Add animation</div>
            </div>
        </ui-dropdown-item>
        <ui-dropdown-item
            class="custom-dropdown"
            (click)="deleteAnimation()">
            <div class="custom-item">
                <div class="custom-column"></div>
                <div class="custom-text">Delete animation</div>
                <div [class]="contextMenu.isMac ? 'mac custom-action' : 'custom-action'">
                    {{ keyboardShortcuts.Editor.DeleteElement }}
                </div>
            </div>
        </ui-dropdown-item>
    }
</ui-dropdown>
