.dropdown-trigger {
    visibility: hidden;
    position: absolute;
    width: 228px;

    &.feed-anchor {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: default;
        position: absolute !important;
        z-index: 2 !important;
    }
}

.custom-dropdown {
    grid-template-columns: 0 auto 0 !important;

    .custom-item {
        display: inline-grid;
        width: 100%;
        grid-template-columns: 24px auto auto;
        margin: 0;
        align-items: center;

        &.transform {
            grid-template-columns: 24px auto repeat(4, 31px);

            .transform-action {
                display: inline-grid;
                border-right: 1px solid var(--studio-color-grey-96);
                justify-content: center;
                align-items: center;

                &.first {
                    border-left: 1px solid var(--studio-color-grey-96);
                }

                &.last {
                    border-left: unset;
                }

                &.active {
                    .action-icon {
                        color: var(--studio-color-blue);
                    }
                }

                .action-icon {
                    width: 31px;
                    height: 34px;
                    display: grid;
                    align-items: center;
                    justify-items: center;
                    color: var(--studio-color-text);
                }
            }

            .transform-action:hover {
                background-color: var(--studio-color-surface-selected);
            }
        }

        &.transform:hover {
            background: var(--studio-color-background-second);
        }

        .custom-column {
            color: var(--studio-color-grey-84);
            justify-self: center;
            margin-right: 0;
            font-size: 14px;
        }

        .custom-action {
            justify-self: right;
            padding-right: 16px;
            color: var(--studio-color-text-second);

            &.mac::first-letter {
                font-size: 10px;
                letter-spacing: 1px;
            }
        }
    }
}

.disabled-with-tooltip {
    pointer-events: all !important;
    cursor: default;
}
