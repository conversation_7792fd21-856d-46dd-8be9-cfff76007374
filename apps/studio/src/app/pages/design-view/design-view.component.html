@if (isNewUI()) {
    <div class="design-view-container">
        <ng-container *ngTemplateOutlet="headerTemplate"></ng-container>
        <div class="body-container">
            <ng-container *ngTemplateOutlet="bodyTemplate"></ng-container>
        </div>
    </div>
} @else {
    <ui-header
        [full]="true"
        [showLogo]="false"
        [style.visibility]="editingElement ? 'hidden' : ''">
        <ng-container *ngTemplateOutlet="headerTemplate"></ng-container>
    </ui-header>
    <ui-body
        [full]="true"
        [showOverflow]="true"
        #uiBody>
        <ng-container *ngTemplateOutlet="bodyTemplate"></ng-container>
    </ui-body>
}

<ng-template #headerTemplate>
    @if (designViewInit$ | async) {
        <editor-topbar
            id="my-body"
            [isEditingElement]="!!editingElement"
            #topbar></editor-topbar>
    }
</ng-template>
<ng-template #bodyTemplate>
    @if (designViewInit$ | async) {
        @if (isNewUI()) {
            <div class="editor">
                @if (workspaceInit$ | async) {
                    <div
                        class="left"
                        [style.visibility]="editingElement ? 'hidden' : ''">
                        <!-- Toolbar left -->
                        <studio-toolbar
                            uiCard
                            [hoverable]="false"
                            [size]="'sm'"
                            #toolbar
                            (toolbarIsOpen)="toolbarIsOpen($event)"></studio-toolbar>

                        <!-- Media library -->
                        <media-library
                            uiCard
                            [hoverable]="false"
                            [inset]="true"
                            [size]="'sm'"
                            #mediaLibrary
                            [width]="mediaLibraryWidth"></media-library>
                    </div>

                    <!-- Edit asset -->
                    @if (editingElement) {
                        <edit-element
                            class="element-edit"
                            [element]="editingElement"
                            (closeSettings)="onCloseEditElement($event)"
                            data-test-id="element-edit-view"></edit-element>
                    }

                    <div
                        class="bottom"
                        [ngClass]="{ 'library-open': isMediaLibraryOpen }"
                        [style.display]="editingElement ? 'none' : ''">
                        <!-- Timeline -->
                        @if (isNewUI()) {
                            <new-studio-timeline
                                [isNewUI]="true"
                                uiCard
                                [hoverable]="false"
                                [inset]="true"
                                [size]="'sm'"
                                id="timeline"
                                #timeline
                                [isZooming]="workspace.isZooming"
                                [animator]="animator!"
                                [workspace]="workspace"
                                [mouseOverTimeline]="mouseOverTimeline"
                                (mouseenter)="onMouseEnterTimeline()"
                                (mouseleave)="onMouseLeaveTimeline()" />
                        } @else {
                            <studio-timeline
                                [isNewUI]="false"
                                uiCard
                                [hoverable]="false"
                                [inset]="true"
                                [size]="'sm'"
                                id="timeline"
                                #timeline
                                [isZooming]="workspace.isZooming"
                                [animator]="animator!"
                                [workspace]="workspace"
                                [mouseOverTimeline]="mouseOverTimeline"
                                (mouseenter)="onMouseEnterTimeline()"
                                (mouseleave)="onMouseLeaveTimeline()" />
                        }
                    </div>
                    @if (!editingElement) {
                        <div class="right">
                            <!-- Properties right -->
                            @if (editorStateService.size) {
                                <properties-panel
                                    uiCard
                                    [hoverable]="false"
                                    [inset]="true"
                                    [size]="'sm'"
                                    ui-theme="tiny"
                                    [animator]="animator!"></properties-panel>
                            }
                        </div>
                    }
                }
                <ng-container *ngTemplateOutlet="workspaceTemplate"></ng-container>
            </div>
        } @else {
            <div class="editor">
                @if (workspaceInit$ | async) {
                    <!-- Toolbar left -->
                    <div
                        class="left"
                        [style.visibility]="editingElement ? 'hidden' : ''">
                        <!-- Toolbar left -->
                        <studio-toolbar
                            #toolbar
                            (toolbarIsOpen)="toolbarIsOpen($event)"></studio-toolbar>

                        <!-- Media library -->
                        <media-library
                            #mediaLibrary
                            [width]="mediaLibraryWidth"></media-library>
                    </div>

                    <!-- Edit asset -->
                    @if (editingElement) {
                        <edit-element
                            class="element-edit"
                            [element]="editingElement"
                            (closeSettings)="onCloseEditElement($event)"
                            data-test-id="element-edit-view"></edit-element>
                    }

                    <div
                        class="bottom"
                        [ngClass]="{ 'library-open': isMediaLibraryOpen }"
                        [style.display]="editingElement ? 'none' : ''">
                        <!-- Timeline -->
                        <studio-timeline
                            id="timeline"
                            #timeline
                            [isZooming]="workspace.isZooming"
                            [animator]="animator!"
                            [workspace]="workspace"
                            [mouseOverTimeline]="mouseOverTimeline"
                            (mouseenter)="onMouseEnterTimeline()"
                            (mouseleave)="onMouseLeaveTimeline()"></studio-timeline>
                    </div>
                    @if (!editingElement) {
                        <div class="right">
                            <!-- Properties right -->
                            @if (editorStateService.size) {
                                <properties-panel
                                    ui-theme="tiny"
                                    [animator]="animator!"></properties-panel>
                            }
                        </div>
                    }
                }
            </div>
            <ng-container *ngTemplateOutlet="workspaceTemplate"></ng-container>
        }
    }
</ng-template>

<ng-template #workspaceTemplate>
    <div class="body">
        <!-- Zoom control -->
        @if (workspaceInit$ | async) {
            <zoom-control
                [zoom]="editorStateService.zoom * 100"
                (isHovered)="workspace.zoomControlHover($event)"
                (mousedown)="workspace.transform.cancel()"></zoom-control>
        }
        <!-- Workspace -->
        @if (editorStateService.creativeDataNode && designViewInit$ | async) {
            <studio-workspace
                id="workspace"
                #workspace
                appZoomControl
                [zoom]="editorStateService.zoom"
                [isZooming]="workspace.isZooming"
                [class.hide]="editingElement"
                [mouseOverTimeline]="mouseOverTimeline"></studio-workspace>
        }
    </div>
</ng-template>

<ng-template draggableContainer></ng-template>
@if (loading) {
    <ui-loader></ui-loader>
}
<studio-devtools [designView]="this"></studio-devtools>
