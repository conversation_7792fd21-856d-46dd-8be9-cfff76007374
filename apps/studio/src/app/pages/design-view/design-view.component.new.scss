$barSize: 5.6rem;
$propertiesWidth: 27rem;

:where(:root[data-uinew]) :host {
    editor-topbar {
        z-index: 3;
    }
    .design-view-container {
        background: var(--studio-color-background);
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    .body-container {
        position: relative;
        flex: 1;
        display: flex;
        flex-direction: row;
        overflow: hidden;
        background-color: var(--nui-surface-neutral-subtler);
    }
    .editor {
        z-index: 2;
    }

    ui-body {
        &.show-overflow {
            .left {
                z-index: 2;
            }
        }
    }

    ui-loader {
        z-index: 103;
    }

    .editor {
        display: flex;
        flex-direction: row;
        height: 100%;
        user-select: none;
        width: 100%;
        justify-content: space-between;
        align-content: end;

        .left {
            display: flex;
            flex-direction: row;
            position: relative;
            margin: var(--nui-space-200);

            media-library {
                margin-left: var(--nui-space-200);
            }

            &:hover {
                z-index: 102;
            }
        }
        .right {
            margin: var(--nui-space-200);
            flex-shrink: 0;

            properties-panel {
                width: $propertiesWidth;
            }
        }
        .bottom {
            display: flex;
            justify-content: flex-end;
            flex-direction: column;
            margin-bottom: var(--nui-space-200);
            flex: 1;
            align-self: end;
            z-index: 103;
        }
        .element-edit {
            position: absolute;
            width: 100%;
            height: 100%;
        }
    }

    .body {
        position: absolute;
        top: 0;
        bottom: 0;
        left: calc($barSize + var(--nui-space-200));
        right: calc($propertiesWidth + var(--nui-space-200));
        z-index: -1;
    }

    .hide {
        visibility: hidden;
        opacity: 0;
    }

    ::ng-deep .delete-element-dialog-table {
        td {
            text-overflow: ellipsis;
            overflow: hidden;
            max-width: 120px;
            white-space: nowrap;
            padding-right: 10px;
        }
    }

    ::ng-deep .ui-notification.warning {
        padding-right: 9px !important;
    }
}
