import { Pipe, PipeTransform } from '@angular/core';
import { isGroupDataNode } from '@creative/nodes/helpers';
import { IGroupElementDataNode, OneOfDataNodes } from '@domain/nodes';

@Pipe({
    name: 'isGroupNode',
    standalone: false
})
export class IsGroupPipe implements PipeTransform {
    transform(node: OneOfDataNodes): IGroupElementDataNode | undefined {
        if (isGroupDataNode(node)) {
            return node;
        }
    }
}
