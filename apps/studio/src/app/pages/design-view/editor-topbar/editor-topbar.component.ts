import { Component, HostListener, inject, Input, OnInit } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { UINewThemeService, UINotificationService } from '@bannerflow/ui';
import { ICreativeset } from '@domain/creativeset/creativeset';
import { CreativeSize } from '@domain/creativeset/size';
import { CreativesetDataService, FeatureService, StudioHubService, UserService } from '@studio/common';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { StudioFeatureFlags } from '@studio/domain/feature-flags';
import { Breakpoint } from '@studio/utils/breakpoints';
import { Observable } from 'rxjs';
import { map, withLatestFrom } from 'rxjs/operators';
import { FiltersService } from '../../../shared/filters/state/filters.service';
import { DesignViewComponent } from '../design-view.component';
import {
    EditorSaveStateService,
    EditorSaveStatus,
    ICreativeSaveState
} from '../services/editor-save-state.service';
import { EditorStateService } from '../services/editor-state.service';

type ButtonsDisplayText = 'SAVE' | 'SAVED' | 'UPLOADING';

const ButtonsDisplayText: Record<ButtonsDisplayText, string> = {
    SAVE: 'SAVE',
    SAVED: 'SAVED',
    UPLOADING: 'UPLOADING...'
};
const ButtonsDisplayTextNui: Record<ButtonsDisplayText, string> = {
    SAVE: 'Save',
    SAVED: 'Saved',
    UPLOADING: 'Uploading...'
};

@Component({
    selector: 'editor-topbar',
    templateUrl: './editor-topbar.component.html',
    styleUrls: ['./editor-topbar.component.scss'],
    standalone: false
})
export class EditorTopbarComponent implements OnInit {
    private uiNewThemeService = inject(UINewThemeService);
    private environmentService = inject(EnvironmentService);
    private readonly studioHubService = inject(StudioHubService);

    @HostListener('mousedown') onMouseDown = (): void => this.editor.workspace.transform.cancel();
    @Input() id: string;
    @Input() isEditingElement: boolean;
    showButtons = false;
    contextMenuBorder = true;
    isProd = this.environmentService.appEnvironment.stage === 'production';
    creativeset: ICreativeset;
    creativeId: string;
    isEmployee$: Observable<boolean>;
    showUploadButton$ = this.editorSaveStateService.isUploading$;
    disabled$ = this.editorSaveStateService.state$.pipe(map(({ disabled }) => disabled));
    loading$ = this.editorSaveStateService.state$.pipe(
        map(({ status }) => status !== EditorSaveStatus.Idle)
    );
    designApiEnabled = this.featureService.isFeatureEnabled('design-api');
    sizes = this.creativesetDataService.creativeset.sizes;
    selectedSize?: CreativeSize;

    StudioFeatureFlags = StudioFeatureFlags;

    isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    protected hasConnectedUsers = toSignal(
        this.studioHubService.usersUpdated$.pipe(map(users => !!users?.users.length)),
        { initialValue: false }
    );
    Breakpoint = Breakpoint;
    saveButtonDisplayText = this.getButtonDisplayText('SAVED');

    constructor(
        public editor: DesignViewComponent,
        private editorStateService: EditorStateService,
        public uiNotificationService: UINotificationService,
        public creativesetDataService: CreativesetDataService,
        private editorSaveStateService: EditorSaveStateService,
        public userService: UserService,
        private featureService: FeatureService,
        private activatedRoute: ActivatedRoute,
        private filtersSerivce: FiltersService
    ) {
        this.isEmployee$ = this.userService.isEmployee$;
        this.creativeId = this.editorStateService.creativeMetadata.id.toString();
        this.editorSaveStateService.state$.pipe(takeUntilDestroyed()).subscribe(({ disabled }) => {
            this.updateSaveButtonState(disabled);
        });

        this.editorSaveStateService.save$
            .pipe(
                withLatestFrom(this.editorSaveStateService.state$),
                map(([_, saveState]) => saveState),
                takeUntilDestroyed()
            )
            .subscribe(saveState => {
                this.onSaveCreative(saveState);
            });
    }

    ngOnInit(): void {
        this.creativeset = this.creativesetDataService.creativeset;
        this.selectedSize = this.creativeset.sizes.find(
            ({ id }) => id === this.activatedRoute.snapshot.params.size
        );
    }

    onSaveButtonClick(): void {
        this.editorSaveStateService.save({
            saveAll: false,
            saveAndExit: true
        });
    }

    getButtonDisplayText(key: ButtonsDisplayText): string {
        return this.isNewUI() ? ButtonsDisplayTextNui[key] : ButtonsDisplayText[key];
    }

    private onSaveCreative(saveState: ICreativeSaveState): void {
        if (saveState.status === EditorSaveStatus.Uploading) {
            return this.uiNotificationService.open('Please wait until upload is finished.', {
                type: 'info',
                placement: 'top',
                autoCloseDelay: 5000
            });
        }
    }

    exit(): void {
        this.editor.exit();
    }

    onVersionPickerOpen(): void {
        this.editor.versionPickerIsOpen = true;
    }

    onVersionPickerClose(): void {
        this.editor.versionPickerIsOpen = false;
    }

    onSizeChange(size: CreativeSize): void {
        this.selectedSize = size;
        this.filtersSerivce.setSizeFilter([size.id]);
    }

    private updateSaveButtonState(isDisabled: boolean): void {
        this.saveButtonDisplayText = isDisabled
            ? this.getButtonDisplayText('SAVED')
            : this.getButtonDisplayText('SAVE');
    }
}
