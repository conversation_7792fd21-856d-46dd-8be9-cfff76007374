:where(:root:not([data-uinew])) :host {
    display: flex;
    justify-content: space-between;
    min-width: 100px;
    height: 100%;
    flex-wrap: nowrap;
    user-select: none;
    background: var(--studio-color-white-off-light);
}

:where(:root[data-uinew]) :host {
    min-width: 100px;
    display: flex;
    user-select: none;
    height: auto;

    ui-header {
        position: relative;
        height: 56px; // remove after header is done

        // TODO: remove after header is done
        ::ng-deep .content {
            height: 32px;
            align-items: start;
        }
    }
}

.active-dropdown-item {
    background-color: var(--studio-color-surface-selected);
}

.left {
    width: 33%;
    height: 100%;
    color: var(--studio-color-text-secondary);
    display: flex;
    align-items: center;

    .size {
        padding-left: 20px;
        color: var(--studio-color-grey-dark);
        flex-shrink: 0;

        .icon {
            margin-right: 1rem;
            float: left;
        }

        &-name {
            display: block;
            float: left;
            margin-top: 1px;
            margin-left: 1rem;
            max-width: 10.2rem;
        }
    }
}

.center {
    display: flex;
    width: 33%;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0 220px 0 50px;
}

:where(:root:not([data-uinew])) :host .right {
    right: 0;
    display: flex;
    position: relative;
    justify-content: flex-end;
    align-items: center;
    padding-right: 20px;
    min-width: 220px;
    width: 33%;
    height: 100%;

    ui-button {
        margin: 0 8px;
    }

    .exit-icon {
        margin-left: 8px;

        &:hover {
            cursor: pointer;
            --color1: var(--studio-color-text) !important;
        }
    }

    .exit {
        display: inline-block;
        font-size: 3rem;
        line-height: 4rem;
        vertical-align: top;
        padding: 0 0rem 0 1rem;
        color: var(--studio-color-text-discrete);
        outline: none;

        &:focus {
            outline: none;
        }

        &:hover {
            color: var(--studio-color-text);
        }
    }

    ns-notifications-component {
        margin-right: 8px;
    }
}

:where(:root[data-uinew]) :host .topbar-right {
    display: flex;
    flex-direction: row;
    align-items: center;
    column-gap: 1.2rem;

    ui-pr-env-picker {
        margin-top: 3px;
    }

    .save-button {
        // keeping the same size when button content changes to avoid items around it to shift
        width: 52px;
        display: flex;
        justify-content: center;
        flex-direction: column;
    }
}

.imageasset-details {
    display: flex;
    margin-left: 25.4rem;
    padding: 0 3rem;
    align-items: center;
    font-size: 1.2rem;
    justify-content: space-between;
    width: 100%;
    color: var(--studio-color-grey-dark);

    .right {
        justify-self: flex-end;
        display: flex;

        .name {
            margin-right: 1.5rem;
        }
    }
}

::ng-deep {
    .popover-panel {
        background-color: var(--studio-color-grey-97) !important;
        height: 100%;
        position: initial;
    }
}
