@if (isNewUI()) {
    <ui-header
        [full]="true"
        [showLogo]="false"
        [style.visibility]="isEditingElement ? 'hidden' : ''">
        <ng-container left>
            <ng-container *ngTemplateOutlet="creativeTitleTemplate"></ng-container>
        </ng-container>
        <ng-container middle>
            <ng-container *media="Breakpoint.DesktopUp">
                <ng-container *ngTemplateOutlet="versionPickerTemplate"></ng-container>
            </ng-container>
        </ng-container>
        <ng-container right>
            <div class="topbar-right">
                <ng-container *ngTemplateOutlet="studioActionButtonsTemplate"></ng-container>
            </div>
        </ng-container>
    </ui-header>
} @else {
    <div class="left">
        <ng-container *ngTemplateOutlet="creativeTitleTemplate"></ng-container>
    </div>
    <div class="center">
        <ng-container *ngTemplateOutlet="versionPickerTemplate"></ng-container>
    </div>
    <div class="right">
        <ng-container *ngTemplateOutlet="studioActionButtonsTemplate"></ng-container>
    </div>
}

<ng-template #creativeTitleTemplate>
    <dropdown-menu #menu></dropdown-menu>
    @if (menu && menu.dropdown) {
        <topbar-context-menu
            [target]="menu.dropdown"
            [border]="contextMenuBorder"></topbar-context-menu>
    }
    <topbar-breadcrumbs
        [creativeSize]="selectedSize"
        (dblclick)="showButtons = !showButtons"
        (exit)="exit()" />
    @if (showButtons && (!isProd || (isEmployee$ | async))) {
        <creative-preview-access class="test-buttons" />
    }
</ng-template>

<ng-template #versionPickerTemplate>
    @if (creativeset) {
        <version-picker
            [allowManageVersions]="false"
            (pickerOpen)="onVersionPickerOpen()"
            (pickerClose)="onVersionPickerClose()">
        </version-picker>
    }
    @if (designApiEnabled) {
        <ui-select
            [selected]="selectedSize"
            (selectedChange)="onSizeChange($event)">
            @for (size of sizes; track size.id) {
                <ui-option [value]="size"> {{ size.width }}x{{ size.height }} </ui-option>
            }
        </ui-select>
    }
</ng-template>

<ng-template #studioActionButtonsTemplate>
    @if (isEmployee$ | async) {
        <feature-toggle
            uiTooltip="Features in development. Use at your own risk!"
            uiTooltipPosition="bottom" />
        @if (isNewUI()) {
            <ui-pr-env-picker [services]="['sapi', 'dapi', 'studio']"></ui-pr-env-picker>
        } @else {
            <toggle-environment />
        }
    }
    <ns-notifications-component />
    @if (hasConnectedUsers()) {
        <current-users />
    }
    <ng-container *permissions="'Comments'">
        <comments-overview [creativeId]="creativeId" />
    </ng-container>
    @if (showUploadButton$ | async) {
        <ui-button
            id="upload-button"
            data-test-id="upload-button"
            [size]="isNewUI() ? 'sm' : 'md'"
            [type]="isNewUI() ? 'primary' : 'discrete'"
            [text]="getButtonDisplayText('UPLOADING')"
            [disabled]="true"></ui-button>
    } @else {
        @let disabled = disabled$ | async;
        @let loading = loading$ | async;
        <ui-button
            [class.save-button]="isNewUI()"
            id="save-design-button"
            data-test-id="save-design-button"
            [size]="isNewUI() ? 'sm' : 'md'"
            type="primary"
            [text]="saveButtonDisplayText"
            [disabled]="!!disabled"
            [loading]="!!loading"
            (click)="!disabled && !loading && onSaveButtonClick()"></ui-button>
    }
    <help-menu />
    @if (isNewUI()) {
        <ui-button
            data-test-id="exit-editor-button"
            uiTooltip="Exit"
            uiTooltipPosition="left"
            (click)="exit()"
            [disabled]="(loading$ | async)!"
            nuiType="plain-primary"
            nuiSvgIcon="close" />
    } @else {
        <ui-svg-icon
            uiTooltip="Exit"
            uiTooltipPosition="left"
            class="exit-icon"
            id="exit-editor-button"
            data-test-id="exit-editor-button"
            icon="close"
            fill="#B5B5B5"
            [ngStyle]="{ pointerEvents: (loading$ | async)! ? 'none' : 'auto' }"
            (click)="exit()"
            >done
        </ui-svg-icon>
    }
</ng-template>
