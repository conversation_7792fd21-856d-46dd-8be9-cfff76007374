import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { UIModule, UINewThemeService, UINotificationService } from '@bannerflow/ui';
import { CreativesetDataService } from '@studio/common';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { lastValueFrom } from 'rxjs';
import { EditorStateService } from '../../services/editor-state.service';

@Component({
    selector: 'creative-preview-access',
    imports: [UIModule, ReactiveFormsModule],
    templateUrl: './creative-preview-access.component.html',
    styleUrl: './creative-preview-access.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CreativePreviewAccessComponent {
    private environmentService = inject(EnvironmentService);
    private uiNewThemeService = inject(UINewThemeService);

    form: FormGroup;

    isNewUI = this.uiNewThemeService.isNewThemeEnabled;

    constructor(
        private fb: FormBuilder,
        private httpClient: HttpClient,
        private uiNotificationService: UINotificationService,
        private creativesetDataService: CreativesetDataService,
        private editorStateService: EditorStateService
    ) {
        this.form = this.fb.group({
            previewEnvironment: [''],
            ignoreCache: [true],
            snapshotId: ['']
        });
    }

    async openDefaultCreativeUrl(): Promise<void> {
        const previewUrl = await this.getBasePreviewUrl();
        this.openCreativePreview(previewUrl);
    }

    async onFormSubmit(): Promise<void> {
        const params = new URLSearchParams();
        const { previewEnvironment, ignoreCache, snapshotId } = this.form.value;
        if (previewEnvironment) {
            params.set('env', previewEnvironment);
            if (previewEnvironment === 'image-generator') {
                params.set('autoplay', 'off');
                params.set('seek', 'preload-frame');
                params.set('fadein', 'off');
            }
        }

        if (ignoreCache) {
            params.set('cache', 'off');
        }

        if (snapshotId) {
            params.set('snapshot', snapshotId);
        }

        const previewUrl = await this.getBasePreviewUrl();
        for (const [key, value] of params.entries()) {
            previewUrl.searchParams.set(key, value);
        }
        this.openCreativePreview(previewUrl);
    }

    private async getBasePreviewUrl(): Promise<URL> {
        const creativesetId = this.creativesetDataService.creativeset.id;
        const creativeId = this.editorStateService.creativeMetadata.id;
        try {
            const { cps } = this.environmentService.origins;
            const result = await lastValueFrom(
                this.httpClient.get<{ previewUrl: string }>(
                    `${cps}/preview-url?creativeset=${creativesetId}&creative=${creativeId}`
                )
            );
            return new URL(result.previewUrl);
        } catch (e: unknown) {
            this.showErrorNotification((e as HttpErrorResponse).status);
            throw e;
        }
    }

    private openCreativePreview(previewUrl: URL): void {
        window.open(previewUrl, '_blank');
    }

    navigateToCache(): void {
        const { acg } = this.environmentService.origins;
        const cacheUrl = `${acg}/cache?debug=kappa`;
        window.open(cacheUrl, '_blank');
    }

    private showErrorNotification(status: number): void {
        this.uiNotificationService.open(`Could not get preview URL (${status}).`, {
            type: 'error',
            placement: 'top',
            autoCloseDelay: 5000
        });
    }
}
