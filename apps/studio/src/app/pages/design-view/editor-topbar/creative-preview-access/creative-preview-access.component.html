<ui-button
    [type]="isNewUI() ? 'solid-secondary' : 'discrete'"
    (click)="openDefaultCreativeUrl()"
    (mouseover)="hoverPopover.open(hoverTarget)"
    ui-popover-target
    #hoverTarget="ui-popover-target"
    [size]="isNewUI() ? 'sm' : 'md'"
    text="Creative Preview">
</ui-button>

<ui-popover
    #hoverPopover="ui-popover"
    [config]="{
        arrowPosition: 'top',
        panelClass: 'no-padding',
        position: 'bottom',
        openOnHover: true,
        minWidth: 300
    }">
    <ng-template ui-popover-template>
        <div class="wrapper">
            <p>Secret employee feature 🤫</p>
            <form
                class="form"
                [formGroup]="form">
                <div class="form-group">
                    <label for="previewEnvironment">Preview Environment</label>
                    <select
                        id="previewEnvironment"
                        formControlName="previewEnvironment"
                        class="form-control">
                        <option
                            value=""
                            selected>
                            Default
                        </option>
                        <option value="image-generator">Image</option>
                        <option value="video-generator">Video</option>
                    </select>
                </div>

                <div class="form-group">
                    <label
                        for="ignoreCache"
                        class="form-check-label"
                        >Ignore Cache</label
                    >
                    <input
                        type="checkbox"
                        id="ignoreCache"
                        formControlName="ignoreCache"
                        class="form-check-input" />
                </div>

                <div class="form-group">
                    <label for="snapshotId">Snapshot ID</label>
                    <ui-input
                        type="text"
                        id="snapshotId"
                        formControlName="snapshotId"
                        class="form-control" />
                </div>

                <div class="form-actions">
                    <ui-button
                        type="discrete"
                        (click)="navigateToCache()"
                        text="Check Cache"></ui-button>
                    <ui-button
                        type="primary"
                        text="Navigate"
                        (click)="onFormSubmit()"></ui-button>
                </div>
            </form>
        </div>
    </ng-template>
</ui-popover>
