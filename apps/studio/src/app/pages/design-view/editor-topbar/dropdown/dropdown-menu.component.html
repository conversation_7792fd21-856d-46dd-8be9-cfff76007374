<ui-dropdown
    #dropdown
    [width]="dropdownWidth"
    [size]="dropdownSize"
    [offset]="isNewUI() ? { x: -1, y: 2 } : { x: -1, y: 0 }"
    type="menu">
    <ui-dropdown-item
        #editorElement
        data-test-id="edit-section-option"
        [uiDropdownTarget]="editSection"
        (dropdownOpened)="openSubmenu(editorElement)"
        (dropdownClosed)="leaveSubmenu(editorElement)">
        Edit
    </ui-dropdown-item>
    <ui-dropdown-divider></ui-dropdown-divider>
    <ui-dropdown-item
        #settingsMenuElement
        [uiDropdownTarget]="settingsMenu"
        (dropdownOpened)="openSubmenu(settingsMenuElement)"
        (dropdownClosed)="leaveSubmenu(settingsMenuElement)">
        Settings
    </ui-dropdown-item>
    <ui-dropdown-item
        class="menu-item"
        (click)="openFontManager()">
        Manage fonts
    </ui-dropdown-item>
    <ui-dropdown-item
        class="menu-item"
        [uiDropdownTarget]="manageWarningsDropdown.dropdown">
        Manage warnings
    </ui-dropdown-item>
    <ui-dropdown-divider></ui-dropdown-divider>
    <ui-dropdown-item (click)="exit()"> Exit </ui-dropdown-item>
</ui-dropdown>

<ui-dropdown
    type="menu"
    [width]="dropdownWidth"
    [size]="dropdownSize"
    #settingsMenu>
    <ui-dropdown-item
        id="interaction-view"
        #viewSectionElement
        [uiDropdownTarget]="viewSection"
        (dropdownOpened)="openSubmenu(viewSectionElement)"
        (dropdownClosed)="leaveSubmenu(viewSectionElement)">
        View
    </ui-dropdown-item>
    <ui-dropdown-item
        id="interaction-settings"
        #settingsSectionElement
        [uiDropdownTarget]="settingsSection"
        (dropdownOpened)="openSubmenu(settingsSectionElement)"
        (dropdownClosed)="leaveSubmenu(settingsSectionElement)">
        Default animation
    </ui-dropdown-item>
</ui-dropdown>

<ui-dropdown
    type="menu"
    [width]="dropdownWidth"
    [size]="dropdownSize"
    #viewSection>
    <ui-dropdown-item
        id="interaction-view-dimoutside"
        [toggle]="(dimOutsideCanvas$ | async)!"
        [preventCloseOnClick]="true"
        (toggleChange)="toggleOverlay($event)">
        Dim outside canvas
    </ui-dropdown-item>
    <ui-dropdown-item
        id="interaction-view-snapping"
        [toggle]="(snapping$ | async)!"
        [preventCloseOnClick]="true"
        (toggleChange)="toggleSnapping($event)">
        Snapping
    </ui-dropdown-item>
    <ui-dropdown-item
        id="interaction-view-showoutlines"
        [toggle]="(outlineVisible$ | async)!"
        [preventCloseOnClick]="true"
        (toggleChange)="toggleOutlines($event)">
        Show outlines
    </ui-dropdown-item>
    <ui-dropdown-item
        id="interaction-view-guidelines"
        #guidelinesSectionElement
        [uiDropdownTarget]="guidelinesSection"
        (dropdownOpened)="openSubmenu(gridSectionElement)"
        (dropdownClosed)="leaveSubmenu(gridSectionElement)">
        Guidelines
    </ui-dropdown-item>
    <ui-dropdown-item
        id="interaction-view-grid"
        #gridSectionElement
        [uiDropdownTarget]="gridSection"
        (dropdownOpened)="openSubmenu(gridSectionElement)"
        (dropdownClosed)="leaveSubmenu(gridSectionElement)">
        Grid
    </ui-dropdown-item>
</ui-dropdown>

<ui-dropdown
    type="menu"
    [width]="dropdownWidth"
    [size]="dropdownSize"
    #settingsSection>
    <ui-dropdown-item
        id="interaction-default-animations-on"
        #viewSectionElement
        [preventCloseOnClick]="true"
        [preventToggleChange]="true"
        [toggle]="(useDefaultAnimations$ | async)!"
        (click)="setDefaultAnimation(true)">
        On
    </ui-dropdown-item>
    <ui-dropdown-item
        id="interaction-default-animations-off"
        #settingsSectionElement
        [preventCloseOnClick]="true"
        [preventToggleChange]="true"
        [toggle]="!(useDefaultAnimations$ | async)!"
        (click)="setDefaultAnimation(false)">
        Off
    </ui-dropdown-item>
</ui-dropdown>

<ui-dropdown
    type="menu"
    [width]="dropdownWidth"
    [size]="dropdownSize"
    #editSection>
    <ui-dropdown-item
        data-test-id="undo-option"
        [label]="keyboardShortcuts.Workspace.Undo"
        [disabled]="disableUndo"
        (click)="undo()">
        Undo
    </ui-dropdown-item>
    <ui-dropdown-item
        data-test-id="redo-option"
        [label]="keyboardShortcuts.Workspace.Redo"
        [disabled]="disableRedo"
        (click)="redo()">
        Redo
    </ui-dropdown-item>
</ui-dropdown>

<ui-dropdown
    type="menu"
    [width]="dropdownWidth"
    [size]="dropdownSize"
    #gridSection
    class="gridSection"
    [maxHeight]="1000"
    [minWidth]="141"
    width="141">
    <ui-dropdown-item
        [preventCloseOnClick]="true"
        [toggle]="(displayGrid$ | async)!"
        (toggleChange)="toggleGrid($event)">
        Show grid
    </ui-dropdown-item>
    <ui-dropdown-divider></ui-dropdown-divider>
    <ui-dropdown-item
        [preventCloseOnClick]="true"
        [preventToggleChange]="true"
        [toggle]="(grid$ | async) === GridSize.Grid10"
        (click)="setGrid(GridSize.Grid10)">
        10px
    </ui-dropdown-item>
    <ui-dropdown-item
        [preventCloseOnClick]="true"
        [preventToggleChange]="true"
        [toggle]="(grid$ | async) === GridSize.Grid20"
        (click)="setGrid(GridSize.Grid20)">
        20px
    </ui-dropdown-item>
    <ui-dropdown-item
        [preventCloseOnClick]="true"
        [preventToggleChange]="true"
        [toggle]="(grid$ | async) === GridSize.Grid50"
        (click)="setGrid(GridSize.Grid50)">
        50px
    </ui-dropdown-item>
    <ui-dropdown-item
        [preventCloseOnClick]="true"
        [preventToggleChange]="true"
        [toggle]="(grid$ | async) === GridSize.Grid100"
        (click)="setGrid(GridSize.Grid100)">
        100px
    </ui-dropdown-item>
    <ui-dropdown-item
        [preventCloseOnClick]="true"
        [preventToggleChange]="true"
        [toggle]="(grid$ | async) === GridSize.GridFacebook"
        (click)="setGrid(GridSize.GridFacebook)">
        Facebook
    </ui-dropdown-item>
    <ui-dropdown-input
        #customGrid
        (valueChange)="setCustomGrid($event)"
        (click)="setCustomGrid(customGrid.value)"
        type="number"
        icon="edit"
        maxLength="4"
        placeholder="Custom..."
        placeholderFocus="Custom size"
        units="px">
    </ui-dropdown-input>

    <ui-dropdown-item
        #gridColorSection
        [uiDropdownTarget]="gridColor"
        (dropdownOpened)="openSubmenu(gridColorSection)"
        (dropdownClosed)="leaveSubmenu(gridColorSection)">
        Color
    </ui-dropdown-item>
</ui-dropdown>

<ui-dropdown
    type="menu"
    [width]="dropdownWidth"
    [size]="dropdownSize"
    #guidelinesSection
    class="guidelinesSection"
    [maxHeight]="1000"
    [minWidth]="141"
    width="141">
    <ui-dropdown-item
        id="interaction-view-showguidelines"
        [toggle]="(guidelineVisible$ | async)!"
        [preventCloseOnClick]="true"
        (toggleChange)="toggleGuidelines($event)">
        Show guidelines
    </ui-dropdown-item>

    <ui-dropdown-divider></ui-dropdown-divider>

    <ui-dropdown-item
        #guidelineColorSection
        [uiDropdownTarget]="guidelineColors">
        Color
    </ui-dropdown-item>
</ui-dropdown>

<ui-dropdown
    type="menu"
    [width]="dropdownWidth"
    [size]="dropdownSize"
    #gridColor>
    <ui-dropdown-item
        [toggle]="(gridColor$ | async) === GridColor.BLACK"
        [preventCloseOnClick]="true"
        (click)="setGridColor(GridColor.BLACK)">
        Black
    </ui-dropdown-item>
    <ui-dropdown-item
        [toggle]="(gridColor$ | async) === GridColor.WHITE"
        [preventCloseOnClick]="true"
        (click)="setGridColor(GridColor.WHITE)">
        White
    </ui-dropdown-item>
    <ui-dropdown-item
        [toggle]="(gridColor$ | async) === GridColor.GREEN"
        [preventCloseOnClick]="true"
        (click)="setGridColor(GridColor.GREEN)">
        Green
    </ui-dropdown-item>
    <ui-dropdown-item
        [toggle]="(gridColor$ | async) === GridColor.PURPLE"
        [preventCloseOnClick]="true"
        (click)="setGridColor(GridColor.PURPLE)">
        Purple
    </ui-dropdown-item>
</ui-dropdown>

<ui-dropdown
    type="menu"
    [width]="dropdownWidth"
    [size]="dropdownSize"
    #guidelineColors>
    <ui-dropdown-item
        [toggle]="(guidelineColor$ | async) === GuidelineColor.Magenta"
        [preventCloseOnClick]="true"
        [preventToggleChange]="true"
        (click)="setGuidelineColor(GuidelineColor.Magenta)">
        Magenta
    </ui-dropdown-item>
    <ui-dropdown-item
        [toggle]="(guidelineColor$ | async) === GuidelineColor.Black"
        [preventCloseOnClick]="true"
        [preventToggleChange]="true"
        (click)="setGuidelineColor(GuidelineColor.Black)">
        Black
    </ui-dropdown-item>
    <ui-dropdown-item
        [toggle]="(guidelineColor$ | async) === GuidelineColor.White"
        [preventCloseOnClick]="true"
        [preventToggleChange]="true"
        (click)="setGuidelineColor(GuidelineColor.White)">
        White
    </ui-dropdown-item>
    <ui-dropdown-item
        [toggle]="(guidelineColor$ | async) === GuidelineColor.Green"
        [preventCloseOnClick]="true"
        [preventToggleChange]="true"
        (click)="setGuidelineColor(GuidelineColor.Green)">
        Green
    </ui-dropdown-item>
    <ui-dropdown-item
        [toggle]="(guidelineColor$ | async) === GuidelineColor.Purple"
        [preventCloseOnClick]="true"
        [preventToggleChange]="true"
        (click)="setGuidelineColor(GuidelineColor.Purple)">
        Purple
    </ui-dropdown-item>
</ui-dropdown>

<manage-warnings-dropdown
    #manageWarningsDropdown
    [isManageView]="false"></manage-warnings-dropdown>
