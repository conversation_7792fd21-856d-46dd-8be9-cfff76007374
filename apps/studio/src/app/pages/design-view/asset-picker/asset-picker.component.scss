@use 'variables' as *;
@use '../properties-panel/common';

:host {
    &.with-replace-button {
        .preview {
            pointer-events: none;
        }

        .details {
            padding: 0;
        }
    }
}

.details-preview-wrapper {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
}

asset-picker-dropdown {
    display: none;
}

.details {
    justify-content: space-between;
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 5px 0;

    ui-button {
        font-size: 10px;
        width: 90px;
    }

    &__label {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow-x: hidden;
        min-width: 100%;
        width: 1px;
    }

    &__name {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow-x: hidden;
        min-width: 100%;
        width: 1px;
        color: var(--studio-color-text-second);

        &.isFeed {
            color: var(--studio-color-primary);
            cursor: pointer;
        }
    }
}

.preview {
    display: flex;
    padding-right: 10px;
}

.type {
    color: var(--studio-color-grey);
    align-self: flex-end;
    padding: 0 5px 5px 0;
}

.image-wrapper {
    width: 44px;
    height: 44px;
    padding: 2px;
    cursor: pointer;
    background-color: var(--studio-color-surface);
    border: 1px solid var(--studio-color-border-second);
    border-radius: 2px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    &::after {
        z-index: 0;
        content: '';
        display: block;
        background-size: 8px 8px;
        position: absolute;
        top: 3px;
        left: 3px;
        width: calc(100% - 6px);
        height: calc(100% - 6px);
        background-position: center;
        background-repeat: repeat;
        background-image: $chessBackgroundUrl;
    }

    .image,
    .no-image {
        width: 100%;
        height: 100%;
        border-radius: 1px;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        z-index: 1;
    }

    .image {
        box-shadow: 0 0 0 1px inset var(--studio-color-transparent-black-20);
    }

    .no-image {
        color: var(--studio-color-grey-semidark);
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: var(--studio-color-grey-86);

        &.mixed {
            ui-svg-icon {
                --font-size: 4rem;
            }
        }
    }
}

.replace-image-button {
    color: var(--studio-color-primary);
    cursor: pointer;
    font-size: 10px;
}
