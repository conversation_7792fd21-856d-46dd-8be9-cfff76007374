<ui-dropdown
    #dropdown
    [positions]="positions"
    [offset]="offset"
    type="menu">
    <ui-dropdown-item
        (click)="openAssetPickerDialog()"
        data-test-id="add-from-brand-library"
        svgIcon="folder-multiple">
        From brand library
    </ui-dropdown-item>
    @if (allowFeed) {
        <ui-dropdown-item
            (click)="openFeedPickerDialog()"
            svgIcon="feed">
            From feed
        </ui-dropdown-item>
    }
    @if (allowUpload) {
        <ui-dropdown-item
            (click)="handleFromDevice(fileInput)"
            [svgIcon]="'upload-file'"
            style="cursor: pointer">
            From your device
            <input
                id="upload-file"
                style="display: none"
                type="file"
                multiple
                [accept]="fileTypes"
                #fileInput
                (change)="uploadFiles(fileInput.files)" />
        </ui-dropdown-item>
    }

    @if (allowRemove) {
        <ui-dropdown-item
            (click)="removeAsset()"
            svgIcon="delete">
            Remove {{ assetType }}
        </ui-dropdown-item>
    }
</ui-dropdown>
