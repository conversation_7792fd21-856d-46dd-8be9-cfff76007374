import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UIConfirmDialogService, UIDialogRef, UIDialogService, UIModule } from '@bannerflow/ui';
import { VideoLibraryAsset } from '@domain/brand/brand-library';
import { ElementKind } from '@domain/elements';
import { createBrandLibraryElementMock, createVideoLibraryAssetMock } from '@mocks/brand-library.mock';
import { createMockCreativesetDataService } from '@mocks/creativeset.mock';
import { CreativesetDataService } from '@studio/common';
import { ApolloTestingModule } from 'apollo-angular/testing';
import { of } from 'rxjs';
import { BrandLibraryDataService } from '../../../../shared/media-library/brand-library.data.service';
import { defineMatchMedia } from '../../../../shared/mocks/matchMedia.mock';
import { HeavyVideoService } from '../../../../shared/services/heavy-video.service/heavy-video.service';
import { PropertiesService } from '../../properties-panel/properties.service';
import { EditorEventService } from '../../services/editor-event/editor-event.service';
import { EditorStateService } from '../../services/editor-state.service';
import { ElementSelectionService } from '../../services/element-selection.service';
import { AssetPickerUploadService } from '../asset-picker-upload.service';
import { AssetPickerService } from '../asset-picker.service';
import { AssetPickerDropdownComponent } from './asset-picker-dropdown.component';

describe('AssetPickerDropdownComponent', () => {
    let component: AssetPickerDropdownComponent;
    let fixture: ComponentFixture<AssetPickerDropdownComponent>;
    let heavyVideoService: HeavyVideoService;

    beforeAll(() => {
        defineMatchMedia();
    });

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ApolloTestingModule, UIModule],
            providers: [
                { provide: UIConfirmDialogService, useValue: {} },
                {
                    provide: AssetPickerService,
                    useValue: {
                        selectAsset: jest.fn()
                    }
                },
                {
                    provide: AssetPickerUploadService,
                    useValue: {
                        assetUploadComplete$: of(null)
                    }
                },
                { provide: ElementSelectionService, useValue: {} },
                { provide: UIDialogService, useValue: {} },
                { provide: CreativesetDataService, useValue: createMockCreativesetDataService() },
                { provide: EditorEventService, useValue: {} },
                { provide: EditorStateService, useValue: {} },
                { provide: PropertiesService, useValue: {} },
                {
                    provide: HeavyVideoService,
                    useValue: {
                        promptHeavyVideo: (): boolean => true,
                        isHeavyVideo: (): boolean => false
                    }
                },
                {
                    provide: UIDialogRef,
                    useValue: {
                        close: (): void => {}
                    }
                },
                {
                    provide: BrandLibraryDataService,
                    useValue: {
                        getAssetByElement: (): VideoLibraryAsset =>
                            createVideoLibraryAssetMock({
                                fileSize: 2050000
                            })
                    }
                }
            ]
        }).compileComponents();
        fixture = TestBed.createComponent(AssetPickerDropdownComponent);
        fixture.detectChanges();
        component = fixture.componentInstance;
        component['dialogRef'] = TestBed.inject(UIDialogRef);
        heavyVideoService = TestBed.inject(HeavyVideoService);
    });

    it('should create component', () => {
        expect(component).toBeTruthy();
    });

    it('should not call heavy video popup for an image replace', async () => {
        const spyOn = jest.spyOn(heavyVideoService, 'promptHeavyVideo');
        const isHeavyVideoSpy = jest.spyOn(heavyVideoService, 'isHeavyVideo');
        isHeavyVideoSpy.mockReturnValue(false);

        await component.useSelection(createBrandLibraryElementMock(ElementKind.Image), false);
        expect(spyOn).not.toHaveBeenCalled();
    });

    it('should call heavy video popup for a video replace', async () => {
        const mockVideoElement = createBrandLibraryElementMock(ElementKind.Video);
        const isHeavyVideoSpy = jest.spyOn(heavyVideoService, 'isHeavyVideo');
        isHeavyVideoSpy.mockReturnValue(true);
        const spyOn = jest.spyOn(heavyVideoService, 'promptHeavyVideo');

        await component.useSelection(mockVideoElement, false);
        expect(spyOn).toHaveBeenCalled();
    });
});
