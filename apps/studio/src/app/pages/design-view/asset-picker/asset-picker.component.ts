import {
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnChanges,
    Output,
    SimpleChanges,
    ViewChild
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { OneOfLibraryAssets, PartialLibraryAsset } from '@domain/brand/brand-library';
import { ElementKind } from '@domain/elements';
import { IBfFeed, IFeed } from '@domain/feed';
import { ImageSizeMode } from '@domain/image';
import { IFeedFieldListItem } from '@domain/media-library';
import {
    AssetPropertyContext,
    ElementWithAssetProperty
} from '../properties-panel/asset-property/asset-property';
import { PropertiesService } from '../properties-panel/properties.service';
import { AssetPickerService } from './asset-picker.service';

@Component({
    selector: 'asset-picker',
    templateUrl: 'asset-picker.component.html',
    styleUrls: ['asset-picker.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    host: {
        '[class.with-replace-button]': 'showReplaceButton'
    },
    standalone: false
})
export class AssetPickerComponent implements OnChanges {
    @Input() icon?: 'feed' | 'folder-multiple';
    @Input() asset?: PartialLibraryAsset;
    @Input() feed?: IFeed;
    @Input() elements?: ElementWithAssetProperty[];
    @Input() context: AssetPropertyContext;
    @Input() allowFeed = false;
    @Input() allowUpload = true;
    @Input() allowRemove = true;
    @Input() isFeed = false;
    @Input() name?: string;
    @Input() label: string;
    @Input() isMixed = false;
    @Input() assetType: ElementKind.Image | ElementKind.Video = ElementKind.Image;
    @Input() showReplaceButton = false;
    @Input() sizeMode: ImageSizeMode | 'mixed';
    @Input() isSizeModeEnabled = true;

    @Output() assetSelected = new EventEmitter<IAssetSelectionEvent>();
    @Output() assetRemoved = new EventEmitter<void>();
    @Output() feedSelected = new EventEmitter<{ selection: IFeedItemSelection; replaceAll: boolean }>();
    @Output() openFeedSettings = new EventEmitter<ElementRef>();
    @Output() updateImageSizeMode = new EventEmitter<ImageSizeMode>();
    @ViewChild('popoverTarget') popoverTarget: ElementRef;

    thumbnailUrl = '';

    constructor(
        private assetPickerService: AssetPickerService,
        public propertiesService: PropertiesService
    ) {
        this.assetPickerService.feedSelected$.pipe(takeUntilDestroyed()).subscribe(feed => {
            this.onFeedItemSelected(feed);
        });
        this.assetPickerService.assetSelected$.pipe(takeUntilDestroyed()).subscribe(asset => {
            this.onAssetSelected(asset);
        });
        this.assetPickerService.assetRemoved$.pipe(takeUntilDestroyed()).subscribe(() => {
            this.removeAsset();
        });
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.asset) {
            this.setHasThumbnail();
        }
    }

    removeAsset(): void {
        this.assetRemoved.emit();
    }

    onAssetSelected(selection: IAssetSelectionEvent): void {
        this.assetSelected.emit(selection);
    }

    onFeedItemSelected($event: { selection: IFeedItemSelection; replaceAll: boolean }): void {
        this.feedSelected.emit($event);
    }

    toggleFeedSettings(): void {
        this.openFeedSettings.emit(this.popoverTarget);
    }

    onUpdateImageSizeMode(mode: ImageSizeMode): void {
        this.updateImageSizeMode.emit(mode);
    }

    private setHasThumbnail(): void {
        if (this.isMixed) {
            return;
        }
        const thumbnailUrl = this.asset?.thumbnail.url;
        // Widget image assets has url value 'undefined' until the value has been changed
        this.thumbnailUrl =
            thumbnailUrl && thumbnailUrl !== 'undefined'
                ? `url(${thumbnailUrl.replace(/\s/g, '%20')})`
                : '';
    }
}

export interface IAssetSelectionEvent<T extends OneOfLibraryAssets = OneOfLibraryAssets> {
    asset: T;
    replaceAll: boolean;
}

export interface IFeedItemSelection {
    selectedFeed: IBfFeed;
    selection: IFeedFieldListItem;
}
