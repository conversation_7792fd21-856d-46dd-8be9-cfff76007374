import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { createMockCreativesetDataService } from '@mocks/creativeset.mock';
import { createMockBrandLibraryDataService } from '@mocks/services/brand-library-data-service.mock';
import { createDisplayCampaignServiceMock } from '@mocks/services/display-campaign-service.mock';
import { createUserServiceMock } from '@mocks/services/user-service.mock';
import { BrandService } from '@studio/common/brand';
import { CreativesetDataService } from '@studio/common/creativeSet/creativeset.data.service';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { UserService } from '@studio/common/user/user.service';
import { ApolloTestingModule } from 'apollo-angular/testing';
import { of } from 'rxjs';
import { DisplayCampaignService } from '../../../shared/display-campaign/state/display-campaign.service';
import { BrandLibraryDataService } from '../../../shared/media-library/brand-library.data.service';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../../shared/mocks/store.mock';
import { HeavyVideoService } from '../../../shared/services/heavy-video.service/heavy-video.service';
import { AssetUploadService } from '../services/asset-upload.service';
import { EditorStateService } from '../services/editor-state.service';
import { AssetPickerUploadService } from './asset-picker-upload.service';

describe('AssetPickerUploadService', () => {
    let assetPickerUploadService: AssetPickerUploadService;
    let heavyVideoService: HeavyVideoService;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [ApolloTestingModule],
            providers: [
                provideHttpClient(),
                provideHttpClientTesting(),
                AssetPickerUploadService,
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER,
                {
                    provide: BrandService,
                    useValue: {}
                },
                {
                    provide: BrandLibraryDataService,
                    useValue: createMockBrandLibraryDataService()
                },
                {
                    provide: DisplayCampaignService,
                    useValue: createDisplayCampaignServiceMock()
                },
                {
                    provide: EnvironmentService,
                    useValue: {}
                },
                {
                    provide: CreativesetDataService,
                    useValue: createMockCreativesetDataService()
                },
                {
                    provide: UserService,
                    useValue: createUserServiceMock()
                },
                {
                    provide: AssetUploadService,
                    useValue: {
                        uploadProgress$: of({}),
                        uploadAssets: (): string => 'id'
                    }
                },
                {
                    provide: EditorStateService,
                    useValue: {}
                }
            ]
        });
        assetPickerUploadService = TestBed.inject(AssetPickerUploadService);
        heavyVideoService = TestBed.inject(HeavyVideoService);
    });

    it('should create service', () => {
        expect(assetPickerUploadService).toBeTruthy();
    });

    describe('uploadAssets', () => {
        const size = 1024 * 1000 * 2 + 1;
        // Create a blob with the specific size
        const buffer = new ArrayBuffer(size);
        const blob = new Blob([buffer], { type: 'video/mp4' });
        const file = new File([blob], 'filename.mp4', { type: 'video/mp4' });

        it('should set an asset upload process id if heavy video is allowed', async () => {
            jest.spyOn(heavyVideoService, 'promptHeavyVideo').mockResolvedValue(true);
            jest.spyOn(heavyVideoService, 'isHeavyVideo').mockReturnValue(false);

            await assetPickerUploadService.uploadAssets([file]);
            expect(assetPickerUploadService.assetUploadProcessId).toBe('id');
        });

        it('should not set an asset upload process id if heavy video is not allowed', async () => {
            jest.spyOn(heavyVideoService, 'promptHeavyVideo').mockResolvedValue(false);
            jest.spyOn(heavyVideoService, 'isHeavyVideo').mockReturnValue(true);

            await assetPickerUploadService.uploadAssets([file]);
            expect(assetPickerUploadService.assetUploadProcessId).toBeUndefined();
        });
    });
});
