@if (assetType === 'image' && !isFeed) {
    <div class="details-preview-wrapper">
        <div class="preview">
            <div
                class="image-wrapper"
                [uiDropdownTarget]="dropdown.dropdown">
                @if (isMixed) {
                    <div class="no-image mixed">
                        <ui-svg-icon icon="progress"></ui-svg-icon>
                    </div>
                } @else {
                    @if (thumbnailUrl) {
                        <div
                            class="image"
                            [style.backgroundImage]="thumbnailUrl"
                            [class.empty-state]="
                                propertiesService.stateValueIsUndefined('imageAsset') ||
                                propertiesService.stateValueIsUndefined('videoAsset')
                            "></div>
                    } @else {
                        <div class="no-image">
                            <ui-svg-icon icon="plus"></ui-svg-icon>
                        </div>
                    }
                }
            </div>
        </div>

        <div class="details">
            @if (!showReplaceButton) {
                <div class="details__label">
                    {{ label }}
                </div>
            }
            @if (isSizeModeEnabled && context !== 'widget-image') {
                <size-mode-options
                    [sizeMode]="sizeMode"
                    (sizeModeChanged)="onUpdateImageSizeMode($event)"></size-mode-options>
            }

            @if (showReplaceButton) {
                <div
                    class="replace-image-button"
                    data-test-id="replace-image-button"
                    [uiDropdownTarget]="dropdown.dropdown">
                    {{ 'Replace ' + assetType }}
                </div>
            }
        </div>
    </div>
}

@let isFeededImage = assetType === 'image' && isFeed;
@if (assetType === 'video' || isFeededImage) {
    <div class="details-preview-wrapper">
        <div class="details">
            @if (!showReplaceButton) {
                <div class="details__label">
                    {{ label }}
                </div>
            }
            <div
                #popoverTarget
                [uiTooltip]="name"
                [uiTooltipDisabled]="!name"
                class="details__name"
                [class.isFeed]="isFeed"
                (click)="toggleFeedSettings()">
                {{ isMixed ? 'Mixed' : name || 'No image selected' }}
            </div>

            @if (showReplaceButton) {
                <ui-button
                    type="primary"
                    [capitalize]="true"
                    [uiDropdownTarget]="dropdown.dropdown"
                    [text]="'Replace ' + assetType"></ui-button>
            }
        </div>

        <div class="preview">
            <div class="type">
                @if (icon) {
                    <ui-svg-icon [icon]="icon"></ui-svg-icon>
                }
            </div>
            <div
                class="image-wrapper"
                [uiDropdownTarget]="dropdown.dropdown">
                @if (isMixed) {
                    <div class="no-image mixed">
                        <ui-svg-icon icon="progress"></ui-svg-icon>
                    </div>
                } @else {
                    @if (thumbnailUrl) {
                        <div
                            class="image"
                            [style.backgroundImage]="thumbnailUrl"
                            [class.empty-state]="
                                propertiesService.stateValueIsUndefined('imageAsset') ||
                                propertiesService.stateValueIsUndefined('videoAsset')
                            "></div>
                    } @else {
                        <div class="no-image">
                            <ui-svg-icon icon="plus"></ui-svg-icon>
                        </div>
                    }
                }
            </div>
        </div>
    </div>

    @if (isFeededImage && isSizeModeEnabled && context !== 'widget-image') {
        <size-mode-options
            [sizeMode]="sizeMode"
            (sizeModeChanged)="onUpdateImageSizeMode($event)"></size-mode-options>
    }
}

<asset-picker-dropdown
    #dropdown
    [offset]="{ x: 15, y: -20 }"
    [assetType]="assetType"
    [context]="context"
    [allowUpload]="allowUpload"
    [allowRemove]="allowRemove && asset !== undefined"
    [allowFeed]="allowFeed">
</asset-picker-dropdown>
