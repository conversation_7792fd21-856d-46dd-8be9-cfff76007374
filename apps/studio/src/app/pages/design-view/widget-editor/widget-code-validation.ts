import { IWidgetCustomProperty } from '@domain/widget';

/** Strictly checks that the string contains http//, https// or www. */
const urlRegex =
    /((http(s)?:\/\/))|((www\.))+([a-zA-Z0-9@:%._+~#=]{2,256})\.[a-z]{2,6}\b([-a-zA-Z0-9@:%_+.~#?&//=]*)/;

export function validateHtml(model: monaco.editor.ITextModel): monaco.editor.IMarkerData[] {
    const markers: monaco.editor.IMarkerData[] = [];
    for (let i = 1; i < model.getLineCount() + 1; i++) {
        const range = {
            startLineNumber: i,
            startColumn: 1,
            endLineNumber: i,
            endColumn: model.getLineLength(i) + 1
        };
        const content = model.getValueInRange(range).trim();
        const isUrl = content.match(urlRegex);

        if (isUrl) {
            markers.push({
                message:
                    'Url detected. Please note that the resource may not be fully loaded before the creative is initalized and starts playing unless it is a <script> tag. It is recomended to preload external resources through javascript by utilizing `Widget.isLoadedPromise`.',
                severity: monaco.MarkerSeverity.Info,
                code: {
                    target: monaco.Uri.parse(
                        'https://support.bannerflow.com/en/articles/3313404-custom-widgets'
                    ),
                    value: 'Read more here'
                },
                startLineNumber: range.startLineNumber,
                startColumn: range.startColumn,
                endLineNumber: range.endLineNumber,
                endColumn: range.endColumn
            });
        }
    }

    return markers;
}

export function validateCss(model: monaco.editor.ITextModel): monaco.editor.IMarkerData[] {
    const markers: monaco.editor.IMarkerData[] = [];

    // lines start at 1
    for (let i = 1; i < model.getLineCount() + 1; i++) {
        const range = {
            startLineNumber: i,
            startColumn: 1,
            endLineNumber: i,
            endColumn: model.getLineLength(i) + 1
        };
        const content = model.getValueInRange(range).trim();
        // Very optimistic check if the line is commented out
        const isCommented = content.startsWith('/*');
        const isUrl = content.match(urlRegex);

        if (isUrl && !isCommented) {
            const severity = monaco.MarkerSeverity.Info;

            markers.push({
                message:
                    'Url detected. Please note that the resource may not be fully loaded before the creative is initalized and starts playing. It is recomended to preload external resources through javascript by utilizing `Widget.isLoadedPromise`.',
                severity,
                code: {
                    target: monaco.Uri.parse(
                        'https://support.bannerflow.com/en/articles/3313404-custom-widgets'
                    ),
                    value: 'Read more here'
                },
                startLineNumber: range.startLineNumber,
                startColumn: range.startColumn,
                endLineNumber: range.endLineNumber,
                endColumn: range.endColumn
            });
        }
    }

    return markers;
}

export function validateTs(
    model: monaco.editor.ITextModel,
    customProperties: IWidgetCustomProperty[],
    isLoadedPromiseExists: boolean
): monaco.editor.IMarkerData[] {
    const markers: monaco.editor.IMarkerData[] = [];
    const informationMessage =
        '`Widget.isLoadedPromise` seem to exist but please ensure that the resource is properly loaded before the isLoadedPromise is resolved.';
    const errorMessage =
        '`Widget.isLoadedPromise` does not seem to exist. Please ensure `Widget.isLoadedPromise` is utilized and that the resource is properly loaded before the isLoadedPromise is resolved.';

    // lines start at 1
    for (let i = 1; i < model.getLineCount() + 1; i++) {
        const range = {
            startLineNumber: i,
            startColumn: 1,
            endLineNumber: i,
            endColumn: model.getLineLength(i) + 1
        };
        const content = model.getValueInRange(range).trim();
        // Very optimistic check if the line is commented out
        const isCommented = content.startsWith('//') || content.startsWith('/*');

        if (isCommented) {
            continue;
        }

        const isUrl = content.match(urlRegex);
        const customPropertyWithExternalResource = customProperties.find(({ unit, name, value }) => {
            const nameRegExp = new RegExp(`\\.${name}`);

            if (!content.match(nameRegExp)) {
                return false;
            }

            return (
                unit === 'image' ||
                (unit === 'text' && typeof value === 'string' && value.match(urlRegex))
            );
        });
        const severity = isLoadedPromiseExists
            ? monaco.MarkerSeverity.Info
            : monaco.MarkerSeverity.Error;

        const markerData: Omit<monaco.editor.IMarkerData, 'message'> = {
            severity,
            code: {
                target: monaco.Uri.parse(
                    'https://support.bannerflow.com/en/articles/3313404-custom-widgets'
                ),
                value: 'Read more here'
            },
            startLineNumber: range.startLineNumber,
            startColumn: range.startColumn,
            endLineNumber: range.endLineNumber,
            endColumn: range.endColumn
        };

        if (isUrl) {
            const message =
                severity === monaco.MarkerSeverity.Info
                    ? `Url detected. ${informationMessage}`
                    : `Url detected. ${errorMessage}`;

            markers.push({
                message,
                ...markerData
            });
        } else if (customPropertyWithExternalResource?.unit === 'image') {
            const message =
                severity === monaco.MarkerSeverity.Info
                    ? `Image properties are not preloaded automatically. ${informationMessage}`
                    : `Image properties are not preloaded automatically and ${errorMessage}`;

            markers.push({
                message,
                ...markerData
            });
        } else if (customPropertyWithExternalResource?.unit === 'text') {
            const message =
                severity === monaco.MarkerSeverity.Info
                    ? `This text property appears to reference an external resource which are not preloaded automatically. ${informationMessage}`
                    : `This text property appears to reference an external resource which are not preloaded automatically and ${errorMessage}`;

            markers.push({
                message,
                ...markerData
            });
        }
    }

    return markers;
}
