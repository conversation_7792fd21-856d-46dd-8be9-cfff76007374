// Fork from https://github.com/xieranmaya/infinite-loop-detector

export const infiniteLoopDetectorCode = `const infiniteLoopDetector = (() => {

    function InfiniteLoopError(msg, type) {
        this.message = msg;
        this.type = type;
    }

    function infiniteLoopDetector(id) {
        if (Date.now() - id > 2000) {
            throw new Error('Loop running too long!', 'InfiniteLoopError');
        }
    }

    infiniteLoopDetector.wrap = function (codeStr) {
        if (typeof codeStr !== 'string') {
            throw new Error('Can only wrap code represented by string, not any other thing at the time! If you want to wrap a function, convert it to string first.');
        }

        return codeStr.replace(/for *\\(.*{|while *\\(.*\\{|do *\\{/g, (loopHead) => {
            return 'var id = Date.now();infiniteLoopDetector(id);' + loopHead + 'infiniteLoopDetector(id);';
        });
    };

    infiniteLoopDetector.unwrap = function (codeStr) {
        return codeStr.replace(/infiniteLoopDetector\\([0-9]*?\\);/g, '');
    }

    return infiniteLoopDetector;
})();`.replace(/ {12}/g, '' /* Normalize indenting */);
