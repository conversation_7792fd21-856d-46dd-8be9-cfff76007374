<div
    class="code-editor"
    #editor>
    @if ((editorLoading$ | async)!) {
        <ui-loader></ui-loader>
    } @else {
        <div
            id="widget-details-toggle"
            class="arrow-toggle toggleDetails"
            (click)="toggleDetails()">
            @if (detailsShowing) {
                <ui-svg-icon icon="arrow-left"></ui-svg-icon>
            }
            @if (!detailsShowing) {
                <ui-svg-icon icon="arrow-right"></ui-svg-icon>
            }
        </div>
        <div
            id="widget-preview-toggle"
            class="arrow-toggle togglePreview"
            (click)="togglePreview()"
            [class.previewHidden]="!previewEnabled">
            @if (previewEnabled) {
                <ui-svg-icon icon="arrow-right"></ui-svg-icon>
            }
            @if (!previewEnabled) {
                <span>PREVIEW</span>
            }
        </div>
    }
</div>

<div
    class="code-preview"
    #codePreview
    [style.display]="previewEnabled ? 'block' : 'none'">
    @if (previewLoading$ | async) {
        <ng-container>
            <ui-loader></ui-loader>
        </ng-container>
    }
    <div
        class="resizer resize-0"
        (mousedown)="onStartResizePreview($event)"></div>

    @if (creativeDataNode) {
        <studio-workspace
            #workspace
            [transparent]="true"
            [hotkeysExclusions]="hotkeysExclusions"></studio-workspace>
    }
</div>

<div
    class="preview-properties"
    [style.display]="previewEnabled ? 'block' : 'none'">
    @if (creativeDataNode) {
        <properties-panel
            ui-theme="tiny"
            [hideDefaultProperties]="true"></properties-panel>
    }
</div>
