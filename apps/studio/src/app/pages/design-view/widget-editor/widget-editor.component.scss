:host {
    width: 100%;
    display: flex;
    height: 100%;
    background: var(--studio-color-background-second);
    --properties-width: 220px;
    --code-preview-width: 299px;
}

.code-editor {
    flex: 1;
    min-width: 30%;
    position: relative;
    padding-top: 15px;
    background: var(--studio-color-static-black-off-semi);
    z-index: 102;

    ::ng-deep .monaco-editor {
        * {
            font-size: 16px;
        }
    }
}

.resizer {
    width: 0.3rem;
    height: 100%;
    position: absolute;
    background: transparent;
    cursor: w-resize;
    left: -0.5px;
    z-index: 1001;
}

.code-preview {
    color: var(--studio-color-background-second);
    width: var(--code-preview-width);
    flex-shrink: 0;
    position: relative;
    min-width: var(--code-preview-width);
    max-width: calc(70% - var(--properties-width));
    background-image: linear-gradient(
            to right,
            var(--studio-color-transparent-white-95),
            var(--studio-color-transparent-white-95)
        ),
        linear-gradient(to right, black 50%, white 50%),
        linear-gradient(to bottom, black 50%, white 50%);
    background-blend-mode: normal, difference, normal;
    background-size: 2em 2em;

    .preview-iframe {
        width: 100%;
        height: 100%;
        border: 0;
    }
}

.preview-properties {
    width: var(--properties-width);
}

.arrow-toggle {
    color: var(--studio-color-surface);
    border: 1px solid var(--studio-color-border-second);
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    z-index: 1;
    cursor: pointer;

    &.toggleDetails {
        left: 0;
        bottom: 15px;
        border-left: 0;
    }

    &.togglePreview {
        right: 0;
        top: 15px;
        background: var(--studio-color-static-black-off-semi);
        border-right: 0;

        &.previewHidden {
            right: 30px;
            top: 15px;
            width: 108px;
            border-right: 1px solid var(--studio-color-border-second);
        }
    }
}
