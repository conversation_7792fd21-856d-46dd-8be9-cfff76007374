.upload-wrapper {
    padding: 1.2rem 0 1.6rem 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hidden {
    display: none;
}

.upload {
    color: var(--studio-color-text-second);
    height: 8.4rem;
    width: 8.4rem;
    background: var(--studio-color-background);
    border-radius: 2px;
    border: 1px solid var(--studio-color-grey-92);
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    overflow: hidden;
    position: relative;
}

.image {
    background-size: cover !important;
    transition:
        transform 0.08s cubic-bezier(0.54, 1.44, 0.72, 1),
        width 0.08s cubic-bezier(0.54, 1.44, 0.72, 1),
        height 0.08s cubic-bezier(0.54, 1.44, 0.72, 1);
    background-position: center center !important;
    background-repeat: no-repeat !important;
    width: 100%;
    height: 100%;

    &.uploading {
        transform: scale(0.5);
    }
}

.icon {
    text-align: center;
}

.text {
    height: 1.2rem;
    font-size: 1rem;
    text-align: center;
    letter-spacing: 0;
}

.progress {
    width: 0;
    position: absolute;
    height: 2px;
    background: var(--studio-color-primary);
    z-index: 1;
    bottom: 0;
    left: 0;
    transition: width 0.2s ease-in-out;
    transition-delay: 0.1s;
}
