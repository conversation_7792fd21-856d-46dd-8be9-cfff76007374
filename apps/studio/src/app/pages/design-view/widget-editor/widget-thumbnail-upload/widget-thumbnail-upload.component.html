<div class="upload-wrapper">
    <input
        id="upload"
        type="file"
        class="hidden"
        accept=".jpg, .jpeg, .png, .svg, .gif"
        #fileInput
        (change)="upload(fileInput.files)" />
    <label
        class="upload"
        for="upload">
        @if (imageURL) {
            <div
                class="image"
                [ngClass]="{ uploading: isUploading }"
                [ngStyle]="{ background: 'url(' + imageURL + ')' }"></div>
        }
        @if (isUploading) {
            <div
                [style.width.%]="progress"
                class="progress"></div>
        }
        @if (!imageURL) {
            <div class="upload-content">
                <div class="icon">
                    <ui-svg-icon [icon]="'add'"></ui-svg-icon>
                </div>
                <div class="text">Add thumbnail</div>
            </div>
        }
    </label>
</div>
