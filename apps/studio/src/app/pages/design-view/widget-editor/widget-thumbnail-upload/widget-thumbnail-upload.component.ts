import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    HostListener,
    Input,
    On<PERSON><PERSON>roy,
    OnInit,
    ViewChild
} from '@angular/core';
import { IBrandLibraryElement, INewBrandLibraryElement } from '@domain/brand/brand-library';
import { AssetUploadService } from '../../services/asset-upload.service';
import { Subject, takeUntil } from 'rxjs';
import { BrandLibraryDataService } from '../../../../shared/media-library/brand-library.data.service';

@Component({
    selector: 'widget-thumbnail-upload',
    templateUrl: 'widget-thumbnail-upload.component.html',
    styleUrls: ['widget-thumbnail-upload.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class WidgetThumbnailUploadComponent implements OnInit, OnDestroy {
    @Input() element: IBrandLibraryElement | INewBrandLibraryElement;

    @ViewChild('fileInput') fileInputVariable: ElementRef;

    @HostListener('contextmenu', ['$event'])
    onContextMenu(event: MouseEvent): void {
        event.stopPropagation();
    }
    imageURL: string;
    progress = 0;
    isUploading = false;
    private unsubscribe$ = new Subject<void>();

    constructor(
        private changeDetector: ChangeDetectorRef,
        private brandLibraryDataService: BrandLibraryDataService,
        private assetUploadService: AssetUploadService
    ) {
        this.assetUploadService.uploadProgress$
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe(uploadState => {
                if (uploadState.status === 'IN_PROGRESS') {
                    const percentDone = uploadState.progress;
                    this.progress = percentDone;
                    this.changeDetector.detectChanges();
                    return;
                }

                if (uploadState.status === 'AFTER_PROGRESS') {
                    this.imageURL = uploadState.newAsset.url;
                    this.isUploading = false;
                    this.changeDetector.detectChanges();
                }
            });
    }

    async ngOnInit(): Promise<void> {
        await this.brandLibraryDataService.brandLibraryLoaded;

        const widgetAsset = this.brandLibraryDataService.getWidgetAssetByElement(this.element);
        if (widgetAsset && widgetAsset.thumbnail) {
            this.imageURL = widgetAsset.thumbnail;
            this.changeDetector.detectChanges();
        }
    }

    ngOnDestroy(): void {
        this.unsubscribe$.complete();
        this.unsubscribe$.unsubscribe();
    }

    upload(files: FileList | null): void {
        if (!files) {
            return;
        }

        const file = files[0];
        if (!file) {
            return;
        }

        this.renderImagePreviewLocallyAndUpload(file);
        this.fileInputVariable.nativeElement.value = '';
        this.fileInputVariable.nativeElement.files = new DataTransfer().files;
    }

    private renderImagePreviewLocallyAndUpload(file: File): void {
        this.progress = 0;
        this.isUploading = true;
        this.changeDetector.detectChanges();

        this.assetUploadService.uploadAssets({
            files: [file]
        });
    }
}
