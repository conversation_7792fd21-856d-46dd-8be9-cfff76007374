:where(:root:not([data-uinew])) :host {
    display: block;
    width: 22rem;

    .align-bar {
        height: 2.5rem;
        display: flex;
        padding: 0 0.4rem;

        &.disabled {
            pointer-events: none;
        }

        .button {
            flex: 1;
            text-align: center;
            line-height: 2.7rem;
            font-size: 1.4rem;
            cursor: pointer;
            color: var(--studio-color-grey-84);

            &:hover {
                color: var(--studio-color-black);
            }

            &.active {
                color: var(--studio-color-primary);
            }

            &.disabled {
                pointer-events: none;
                cursor: not-allowed;
                opacity: 0.5;
            }
        }
    }
}
