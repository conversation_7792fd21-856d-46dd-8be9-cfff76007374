import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    DestroyRef,
    forwardRef,
    inject,
    Input,
    OnInit
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { UIButtonGroupOption } from '@bannerflow/ui';
import { isEllipseNode, isState, isWidgetNode } from '@creative/nodes/helpers';
import { isFormulaValue } from '@creative/rendering/formula.utils';
import { getResolvedStatePropertyValue, getValueFromKeyframe } from '@creative/rendering/states.utils';
import { ISetConstraint } from '@domain/dimension';
import { OneOfElementDataNodes, OneOfElementPropertyKeys } from '@domain/nodes';
import { IState, StateProperties } from '@domain/state';
import { IRadius, RadiusType } from '@domain/style';
import { AnimatableProperty } from '@domain/transition';
import { IDesignViewAnimationSettings, UserSettingsService } from '@studio/common/user-settings';
import { isNumber, toFixedDecimal, toRadians } from '@studio/utils/utils';
import { filter, merge, Observable } from 'rxjs';
import { DesignViewComponent } from '../../design-view.component';
import { EditorEventService, ElementChangeType } from '../../services/editor-event';
import { EditorStateService } from '../../services/editor-state.service';
import { HistoryService } from '../../services/history.service';
import { MutatorService } from '../../services/mutator.service';
import { GizmoDrawSchedulerService } from '../../workspace/gizmo-draw-scheduler';
import { createMixedProperty } from '../mixed-properties';
import { PropertiesService } from '../properties.service';
import { layoutPropertiesInputValidation } from './layout-properties.component.constants';

interface IStateProperty {
    value?: string | number;
    resolvedValue?: number;
    isFormula: boolean;
}

const DEFAULT_MIXED_STATE_VALUE: IStateProperty = {
    value: 'mixed',
    resolvedValue: undefined,
    isFormula: false
};

@Component({
    selector: 'layout-properties',
    templateUrl: './layout-properties.component.html',
    styleUrls: ['./layout-properties.scss', '../common.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class LayoutPropertiesComponent implements OnInit {
    private destroyRef = inject(DestroyRef);
    private editor = inject(forwardRef(() => DesignViewComponent));
    private gizmoDrawScheduler = inject(GizmoDrawSchedulerService);
    private editorEvent = inject(EditorEventService);
    private changeDetectorRef = inject(ChangeDetectorRef);
    private historyService = inject(HistoryService);
    private editorStateService = inject(EditorStateService);
    public propertiesService = inject(PropertiesService);
    private mutatorService = inject(MutatorService);
    private userSettingsService = inject(UserSettingsService);

    @Input() elements$: Observable<OneOfElementDataNodes[]>;
    @Input() isPropertiesHidden: boolean;
    @Input() readonly = false;

    isFormulaValue = isFormulaValue;
    elements: OneOfElementDataNodes[] = [];

    inputValidation = layoutPropertiesInputValidation;
    x: IStateProperty = {
        value: 0,
        resolvedValue: 0,
        isFormula: false
    };
    y: IStateProperty = {
        value: 0,
        resolvedValue: 0,
        isFormula: false
    };
    ratio?: number;
    width?: number;
    height?: number;
    constraint: ISetConstraint;
    rotationX: IStateProperty = {
        value: 0,
        resolvedValue: 0,
        isFormula: false
    };
    rotationY: IStateProperty = {
        value: 0,
        resolvedValue: 0,
        isFormula: false
    };
    rotationZ: IStateProperty = {
        value: 0,
        resolvedValue: 0,
        isFormula: false
    };
    fullRadius: number | undefined = 0;
    radiusType: RadiusType = this.fullRadius === 0 ? RadiusType.Joint : RadiusType.Separate;
    RadiusTypeEnum = RadiusType;
    radiusOptions: UIButtonGroupOption[] = [
        { id: this.RadiusTypeEnum.Joint, svgIcon: 'border-joint', value: this.RadiusTypeEnum.Joint },
        {
            id: this.RadiusTypeEnum.Separate,
            svgIcon: 'corner-separate',
            value: this.RadiusTypeEnum.Separate
        }
    ];
    radius = createMixedProperty<IRadius | undefined>({
        type: RadiusType.Joint,
        topLeft: 0,
        topRight: 0,
        bottomRight: 0,
        bottomLeft: 0
    });

    opacity = createMixedProperty<number | undefined>(0);
    ElementChangeType = ElementChangeType;
    elementIsLocked = false;
    stateOrElement?: IState | OneOfElementDataNodes;
    usePixelValues = false;
    scaleX?: number;
    scaleY?: number;
    targetWidth?: number;
    targetHeight?: number;
    showRadiusProperty = true;
    isWidgetEditor = false;
    currentAnimationSettings?: IDesignViewAnimationSettings;
    inStateView = false;

    get state(): IState | undefined {
        return this.propertiesService.inStateView ? this.propertiesService.stateData : undefined;
    }

    ngOnInit(): void {
        this.gizmoDrawScheduler.gizmoDrawer = this.editor.workspace.gizmoDrawer;
        this.isWidgetEditor = this.mutatorService.preview;

        merge(this.historyService.onChange$, this.editorEvent.elements.immediateChange$)
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(() => this.updateProperties());

        this.propertiesService
            .observeDataElementOrStateChange()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(({ element, state }) => {
                this.inStateView = this.propertiesService.inStateView;
                this.isWidgetEditor = this.mutatorService.preview;
                this.shouldShowRadiusProperty();
                this.stateOrElement = state || element;
                this.updateProperties();
            });

        this.elements$
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                filter(elements => !!elements.length)
            )
            .subscribe(elements => {
                this.elements = elements;
                const elementId = this.elements[0].id;
                this.usePixelValues = this.currentAnimationSettings?.usePixelValues[elementId] ?? false;
                this.shouldShowRadiusProperty();
                this.updateProperties();
            });

        this.userSettingsService.animation$
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(animation => {
                this.currentAnimationSettings = animation;
                const elementId = this.elements[0].id;
                this.usePixelValues = animation.usePixelValues[elementId];
            });
    }

    undo(): void {
        this.historyService.undo$.next();
    }

    redo(): void {
        this.historyService.redo$.next();
    }

    toggleRatioLock(): void {
        if (this.inStateView) {
            const element = this.elements[0];
            let ratio = this.stateOrElement?.ratio;

            if (typeof ratio !== 'undefined') {
                ratio = undefined;
            } else {
                ratio = (this.scaleX || element.scaleX || 1) / (this.scaleY || element.scaleY || 1);
            }
            this.mutatorService.setElementPropertyValue(element, 'ratio', ratio);
        } else {
            for (const element of this.elements) {
                let ratio: number | undefined;
                const isRatioLockEnabled =
                    this.ratio === undefined && this.width !== undefined && this.height !== undefined;

                if (isRatioLockEnabled) {
                    ratio = (this.width || element.width) / (this.height || element.height);
                } else {
                    ratio = undefined;
                }
                this.mutatorService.setElementPropertyValue(element, 'ratio', ratio);
            }
        }
    }

    resetRatioLock(): void {
        for (const element of this.elements) {
            this.mutatorService.setElementPropertyValue(element, 'ratio', undefined);
        }
    }

    setRotationX(newValue?: number): void {
        this.setRotation('rotationX', newValue);
    }

    setRotationY(newValue?: number): void {
        this.setRotation('rotationY', newValue);
    }

    setRotationZ(newValue?: number): void {
        this.setRotation('rotationZ', newValue);
    }

    private setRotation(rotation: 'rotationX' | 'rotationY' | 'rotationZ', newValue?: number): void {
        const value = newValue || this[rotation].resolvedValue || 0;

        const rotationValue = toRadians(value);

        for (const element of this.elements) {
            this.mutatorService.setElementPropertyValue(
                element,
                rotation,
                rotationValue,
                ElementChangeType.Burst
            );
        }
    }

    setPosition(newValue: number, type: 'x' | 'y'): void {
        for (const element of this.elements) {
            const prevXValue = this.inStateView ? this.state?.x : element.x;
            const prevYValue = this.inStateView ? this.state?.y : element.y;
            const x = type !== 'x' && isNumber(prevXValue) ? prevXValue : newValue;
            const y = type !== 'y' && isNumber(prevYValue) ? prevYValue : newValue;

            this.mutatorService.setPosition(
                element,
                {
                    x,
                    y
                },
                ElementChangeType.Burst
            );
        }
    }

    setSize(newValue: number, type: 'width' | 'height'): void {
        const isAnyRatioLockDisabled = this.elements.some(({ ratio }) => ratio === undefined);
        const uniqueHeightValues = new Set(this.elements.map(element => element.height));
        const isHeightMixed = uniqueHeightValues.size > 1;
        const uniqueWidthValues = new Set(this.elements.map(element => element.width));
        const isWidthMixed = uniqueWidthValues.size > 1;

        if (isHeightMixed || isWidthMixed || isAnyRatioLockDisabled) {
            this.resetRatioLock();
        }

        for (const element of this.elements) {
            let height = type === 'height' ? newValue : element.height;
            let width = type === 'width' ? newValue : element.width;

            if (this.ratio) {
                if (type === 'width' && isNumber(width)) {
                    height = Math.round(width / this.ratio);
                } else if (type === 'height' && isNumber(height)) {
                    width = Math.round(height * this.ratio);
                }
            }

            this.mutatorService.setSize(element, { width, height }, false, ElementChangeType.Burst);
        }
    }

    resizeEnd(): void {
        this.mutatorService.resizeEnd(ElementChangeType.Skip);
    }

    rotateEnd(): void {
        if (this.elements.length === 1) {
            this.mutatorService.rotateEnd(ElementChangeType.Skip);
        }
    }

    setUsePixelValues(usePixels: boolean): void {
        const selectedElement = this.propertiesService.selectedElement;
        if (!selectedElement) {
            return;
        }

        this.usePixelValues = usePixels;

        const width = selectedElement.width;
        const height = selectedElement.height;

        this.userSettingsService.setAnimationSetting('usePixelValues', {
            ...this.currentAnimationSettings?.usePixelValues,
            [selectedElement.id]: usePixels
        });

        if (usePixels) {
            this.targetWidth = this.scaleX ? width * (this.scaleX / 100) : undefined;
            this.targetHeight = this.scaleY ? height * (this.scaleY / 100) : undefined;
        } else {
            this.scaleX = this.targetWidth ? this.targetWidth / (width / 100) : undefined;
            this.scaleY = this.targetHeight ? this.targetHeight / (height / 100) : undefined;
        }
    }

    setScale(unit: 'scaleX' | 'scaleY'): void {
        if (!this.elements.length) {
            return;
        }

        this.setScaleFromRatio(unit);

        const scaleX = this.scaleX !== undefined ? this.scaleX / 100 : undefined;
        const scaleY = this.scaleY !== undefined ? this.scaleY / 100 : undefined;

        const scale = { scaleX, scaleY };
        const element = this.elements[0];

        this.mutatorService.setScale(element, scale, ElementChangeType.Burst);
    }

    setScaleWithPixelValues(value: number, unit: 'scaleX' | 'scaleY'): void {
        const width = this.propertiesService.selectedElement?.width ?? 0;
        const height = this.propertiesService.selectedElement?.height ?? 0;

        if (unit === 'scaleX' && width) {
            this.targetWidth = value;
            this.scaleX = (value / width) * 100;
        } else if (unit === 'scaleY' && height) {
            this.targetHeight = value;
            this.scaleY = (value / height) * 100;
        }

        this.setTargetSizeFromRatio(unit);
        this.setScale(unit);
    }

    setFullRadius(value = 0, eventType?: ElementChangeType): void {
        for (const element of this.elements) {
            if (isEllipseNode(element)) {
                continue;
            }
            const newFullRadius: IRadius = {
                type: this.radiusType,
                topLeft: value,
                topRight: value,
                bottomRight: value,
                bottomLeft: value
            };
            this.mutatorService.setElementPropertyValue(element, 'radius', newFullRadius, eventType);
            this.fullRadius = value;
            this.radius.value = newFullRadius;
        }
    }

    setRadius(value: number, eventType?: ElementChangeType, cornerToUpdate?: string): void {
        value ??= 0;
        for (const element of this.elements) {
            if (isEllipseNode(element)) {
                continue;
            }
            const newRadius: IRadius = {
                ...((this.stateOrElement || element).radius as IRadius)
            };
            if (cornerToUpdate) {
                newRadius[cornerToUpdate] = value;
            }
            this.mutatorService.setElementPropertyValue(element, 'radius', newRadius, eventType);
            this.radius.value = newRadius;
        }
    }

    setRadiusType(value: RadiusType): void {
        this.radiusType = value;
        if (this.radius.value) {
            this.radius.value.type = this.radiusType;
            for (const element of this.elements) {
                const data = this.stateOrElement?.radius || element.radius;
                const newRadius: IRadius = {
                    topLeft: data.topLeft,
                    topRight: data.topRight,
                    bottomRight: data.bottomRight,
                    bottomLeft: data.bottomLeft,
                    type: this.radiusType
                };

                this.mutatorService.setElementPropertyValue(element, 'radius', newRadius);
            }
        }
    }

    setOpacity(value: number | undefined, eventType?: ElementChangeType): void {
        this.opacity.value = value;
        this.opacity.isMixed = false;
        let opacityValue = value;
        if (!(this.inStateView && !isNumber(this.opacity.value))) {
            opacityValue = (value ?? 100) / 100;
        }
        for (const element of this.elements) {
            this.mutatorService.setElementPropertyValue(element, 'opacity', opacityValue, eventType);
        }
    }

    isStateValueUndefined(propertyName: OneOfElementPropertyKeys): boolean {
        return this.propertiesService.stateValueIsUndefined(propertyName);
    }

    private setTargetSizeFromRatio(unit: 'scaleX' | 'scaleY'): void {
        if (this.stateOrElement?.ratio) {
            if (this.targetWidth === 0 || this.targetHeight === 0) {
                this.targetWidth = this.targetHeight = 0;
            } else if (unit === 'scaleX' && isNumber(this.targetWidth)) {
                this.targetHeight = this.targetWidth / this.stateOrElement.ratio;
            } else if (unit === 'scaleY' && isNumber(this.targetHeight)) {
                this.targetWidth = this.targetHeight * this.stateOrElement.ratio;
            }

            if (this.targetWidth === undefined || this.targetHeight === undefined) {
                this.targetWidth = this.targetHeight = undefined;
            }
        }
    }

    private setScaleFromRatio(unit: 'scaleX' | 'scaleY'): void {
        if (this.stateOrElement?.ratio) {
            if (this.scaleX === 0 || this.scaleY === 0) {
                this.scaleX = this.scaleY = 0;
            } else if (unit === 'scaleX' && isNumber(this.scaleX)) {
                this.scaleY = toFixedDecimal(this.scaleX / this.stateOrElement.ratio, 3);
            } else if (unit === 'scaleY' && isNumber(this.scaleY)) {
                this.scaleX = toFixedDecimal(this.scaleY * this.stateOrElement.ratio, 3);
            }

            if (this.scaleX === undefined || this.scaleY === undefined) {
                this.scaleX = this.scaleY = undefined;
            }
        }
    }

    private updateProperties = (): void => {
        const element = this.elements[0];
        if (element) {
            this.updateOpacityProperty();
            this.updateRadiusProperty();
            this.updateRotationProperties();
            this.updatePositionProperties();
            this.updateSizeProperties();
        }
        if (!this.stateOrElement || !element) {
            return;
        }

        this.elementIsLocked = element.locked;

        if (this.inStateView) {
            const { scaleX, scaleY } = this.stateOrElement;

            this.targetWidth = isNumber(scaleX) ? Math.round(scaleX * element.width) : element.width;
            this.targetHeight = isNumber(scaleY) ? Math.round(scaleY * element.height) : element.height;

            this.scaleX = isNumber(scaleX) ? toFixedDecimal(scaleX * 100, 3) : 100;
            this.scaleY = isNumber(scaleY) ? toFixedDecimal(scaleY * 100, 3) : 100;
        }

        this.detectChanges();
    };

    private updateOpacityProperty(): void {
        const opacityFromSelection = this.getValueFromSelection('opacity');
        this.opacity = {
            isMixed: opacityFromSelection === 'mixed',
            value:
                opacityFromSelection === 'mixed'
                    ? undefined
                    : Math.round(((opacityFromSelection as number) || 0) * 100)
        };
    }

    private isRadiusValueMixed(values: IRadius): boolean {
        const cornerValues = Object.values(values).filter(
            value => value !== RadiusType.Joint && value !== RadiusType.Separate
        );

        return !Object.values(cornerValues).every(value => value === values.topLeft);
    }

    private updateRadiusProperty(): void {
        const radiusFromSelection = this.getValueFromSelection('radius') as IRadius;
        const isMixed = this.isRadiusValueMixed(radiusFromSelection);
        this.radius = {
            isMixed: isMixed,
            value: radiusFromSelection
        };
        this.fullRadius = isMixed ? undefined : radiusFromSelection.topLeft;
        this.radiusType = radiusFromSelection.type;
    }

    private updateRotationProperties(): void {
        if (!this.elements.length) {
            return;
        }

        const rotationX = this.getValueFromSelection('rotationX');
        this.rotationX =
            rotationX === 'mixed'
                ? { ...DEFAULT_MIXED_STATE_VALUE }
                : {
                      value: rotationX as number,
                      resolvedValue: rotationX as number,
                      isFormula: isFormulaValue(rotationX?.toString())
                  };

        const rotationY = this.getValueFromSelection('rotationY');
        this.rotationY =
            rotationY === 'mixed'
                ? { ...DEFAULT_MIXED_STATE_VALUE }
                : {
                      value: rotationY as number,
                      resolvedValue: rotationY as number,
                      isFormula: isFormulaValue(rotationY?.toString())
                  };
        const rotationZ = this.getValueFromSelection('rotationZ');
        this.rotationZ =
            rotationZ === 'mixed'
                ? { ...DEFAULT_MIXED_STATE_VALUE }
                : {
                      value: rotationZ as number,
                      resolvedValue: rotationZ as number,
                      isFormula: isFormulaValue(rotationZ?.toString())
                  };
    }

    private updatePositionProperties(): void {
        if (!this.elements.length) {
            return;
        }
        const x = this.getValueFromSelection('x');

        this.x =
            x === 'mixed'
                ? { ...DEFAULT_MIXED_STATE_VALUE }
                : {
                      value: x as number,
                      resolvedValue: x as number,
                      isFormula: isFormulaValue(x?.toString())
                  };

        const y = this.getValueFromSelection('y');
        this.y =
            y === 'mixed'
                ? { ...DEFAULT_MIXED_STATE_VALUE }
                : {
                      value: y as number,
                      resolvedValue: y as number,
                      isFormula: isFormulaValue(y?.toString())
                  };
    }

    private updateSizeProperties(): void {
        if (!this.elements.length) {
            return;
        }

        if (this.inStateView) {
            this.width = undefined;
            this.height = undefined;
            this.ratio = this.stateOrElement?.ratio;
        } else {
            const isAnyRatioLockDisabled = this.elements.some(element => element.ratio === undefined);

            const uniqueHeightValues = new Set(this.elements.map(element => element.height));
            const isHeightMixed = uniqueHeightValues.size > 1;
            this.height = isHeightMixed ? undefined : Array.from(uniqueHeightValues)[0];

            const uniqueWidthValues = new Set(this.elements.map(element => element.width));
            const isWidthMixed = uniqueWidthValues.size > 1;
            this.width = isWidthMixed ? undefined : Array.from(uniqueWidthValues)[0];

            this.ratio =
                !isHeightMixed && !isWidthMixed && !isAnyRatioLockDisabled
                    ? this.elements[0]?.ratio
                    : undefined;
        }
    }

    private getValue(element: OneOfElementDataNodes, property: AnimatableProperty): StateProperties {
        const stateData = isState(this.stateOrElement) ? this.stateOrElement : undefined;

        let value: StateProperties;

        if (stateData) {
            for (const animation of element.animations) {
                for (const keyframe of animation.keyframes) {
                    if (keyframe.stateId === stateData.id && property in stateData) {
                        const canvasSize = this.editorStateService.size;
                        value = getValueFromKeyframe(
                            property,
                            keyframe,
                            animation,
                            element,
                            canvasSize
                        );
                        break;
                    }
                }
            }
        }

        if (!value) {
            value = stateData ? stateData[property] : element[property];
        }

        return getResolvedStatePropertyValue(value, property, element);
    }

    private getValueFromSelection(property: AnimatableProperty): StateProperties | 'mixed' {
        if (this.inStateView || this.elements.length === 1) {
            return this.getValue(this.elements[0], property);
        }
        const values = this.elements.map(e => e[property]);
        const propertiesSet = new Set<StateProperties>(values);
        if (propertiesSet.size === 0) {
            throw new Error(`"${property}" not found in elements`);
        }

        if (propertiesSet.size > 1) {
            if (property === 'radius') {
                return this.getMixedRadius(values);
            }
            return 'mixed';
        }
        const value = propertiesSet.values().next().value as StateProperties;

        return getResolvedStatePropertyValue(value, property);
    }

    private getMixedRadius(values: IRadius[]): IRadius {
        const areTypesEqual = Object.values(values).every(value => value.type === values[0].type);
        const areTopLeftEqual = this.areRadiusValuesEqual(values, 'topLeft');
        const areTopRightEqual = this.areRadiusValuesEqual(values, 'topRight');
        const areBottomRightEqual = this.areRadiusValuesEqual(values, 'bottomRight');
        const areBottomLeftEqual = this.areRadiusValuesEqual(values, 'bottomLeft');
        const areAllEqual =
            areTopLeftEqual && areTopRightEqual && areBottomRightEqual && areBottomLeftEqual;
        if (areAllEqual) {
            return values[0];
        }
        return {
            type: areTypesEqual ? values[0].type : undefined,
            topLeft: areTopLeftEqual ? values[0].topLeft : undefined,
            topRight: areTopRightEqual ? values[0].topRight : undefined,
            bottomLeft: areBottomLeftEqual ? values[0].bottomLeft : undefined,
            bottomRight: areBottomRightEqual ? values[0].bottomRight : undefined
        } as IRadius;
    }

    private areRadiusValuesEqual(values: IRadius[], corner: string): boolean {
        return Object.values(values).every(value => value[corner] === values[0][corner]);
    }

    private shouldShowRadiusProperty(): void {
        this.showRadiusProperty =
            !this.elements.some(isEllipseNode) && !this.elements.some(isWidgetNode);
    }

    private detectChanges(): void {
        if (!this.changeDetectorRef['destroyed']) {
            this.changeDetectorRef.detectChanges();
        }
    }
}
