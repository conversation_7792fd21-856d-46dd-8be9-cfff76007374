<div
    class="bar height"
    *ngIf="
        (!constraint.top || !constraint.bottom) && (!constraint.left || !constraint.right || !ratioLock)
    "
    [ngClass]="{ active: constraint.height }"
    (click)="toggleConstraint('height')"></div>
<div
    class="bar width"
    *ngIf="
        (!constraint.left || !constraint.right) && (!constraint.top || !constraint.bottom || !ratioLock)
    "
    [ngClass]="{ active: constraint.width }"
    (click)="toggleConstraint('width')"></div>
