// import { Component, Input, EventEmitter, Output } from '@angular/core';
// import { ISetConstraint } from '@core/domain/dimension';
// import { Mutator } from '@app/design-view/mutator.service';

/**
 * CURRENTLY NOT IN USE
 */
// @Component({
//     selector: 'position-constraint',
//     // templateUrl: './position-constraint.component.html',
//     template: '',
//     styleUrls: ['./position-constraint.component.scss'],
// })
// export class PositionConstraintComponent {
//     @Input() constraint: ISetConstraint;
//     @Input() ratioLock: boolean;
//     @Output() constraintChange = new EventEmitter<any>();
//     @Input() mutator: Mutator;

//     toggleConstraint(direction: string): void {
//         this.constraint[direction] = !this.constraint[direction];

//         // If ratio lock is set on object and all size constraints are set
//         // unset the opposite constraint, because all cannot be set when ratio locked
//         if (this.ratioLock) {
//             if (this.constraint.left && this.constraint.top
//                 && this.constraint.right && this.constraint.bottom)
//             {
//                 switch (direction) {
//                     case 'left':
//                         this.constraint.right = false;
//                         break;
//                     case 'right':
//                         this.constraint.left = false;
//                         break;
//                     case 'top':
//                         this.constraint.bottom = false;
//                         break;
//                     case 'bottom':
//                         this.constraint.top = false;
//                         break;
//                 }
//             }

//             // Prevent both size and position constraint in the
//             // same direction when ratio lock is on
//             if ((this.constraint.left && this.constraint.right)
//                 || (this.constraint.top && this.constraint.bottom))
//             {
//                 this.constraint.width = false;
//                 this.constraint.height = false;
//             }
//         }
//         this.constraintChange.emit(this.constraint);
//     }
// }
