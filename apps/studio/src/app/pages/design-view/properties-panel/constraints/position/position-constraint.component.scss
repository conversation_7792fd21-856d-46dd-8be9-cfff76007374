:host {
    display: block;
    width: 5.4rem;
    height: 5.4rem;
    background: var(--studio-color-background-second);
    margin-left: 1rem;
    margin-right: 1rem;
    border-radius: 0.3rem;
    border: 1px solid var(--studio-color-border-second);
    position: relative;
}

.center {
    width: 1.8rem;
    height: 1.8rem;
    border-radius: 0.3rem;
    border: 0.1rem solid var(--studio-color-border-second);
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -0.9rem;
    margin-top: -0.9rem;
}

.bar {
    position: absolute;
    background: var(--studio-color-border-second);
    cursor: pointer;

    &:before {
        content: '';
        position: absolute;
        left: -0.3rem;
        right: -0.3rem;
        top: -0.3rem;
        bottom: -0.3rem;
    }

    &.active {
        background: var(--studio-color-primary);
    }
}

.left {
    height: 0.2rem;
    width: 1.2rem;
    left: 0.2rem;
    top: 50%;
    margin-top: -0.1rem;
}

.right {
    height: 0.2rem;
    width: 1.2rem;
    right: 0.2rem;
    top: 50%;
    margin-top: -0.1rem;
}

.top {
    width: 0.2rem;
    height: 1.2rem;
    top: 0.2rem;
    left: 50%;
    margin-left: -0.1rem;
}

.bottom {
    width: 0.2rem;
    height: 1.2rem;
    bottom: 0.2rem;
    left: 50%;
    margin-left: -0.1rem;
}
