// import { Component, Input, EventEmitter, Output } from '@angular/core';
// import { ISetConstraint } from '@core/domain/dimension';
// import { Mutator } from '@app/design-view/mutator.service';

// /**
//  * CURRENTLY NOT IN USE
//  */
// @Component({
//     selector: 'size-constraint',
//     // templateUrl: './size-constraint.component.html',
//     template: '',
//     styleUrls: ['./size-constraint.component.scss'],
// })
// export class SizeConstraintComponent {
//     @Input() mutator: Mutator;
//     @Input() constraint: ISetConstraint;
//     @Output() constraintChange = new EventEmitter<ISetConstraint>();
//     @Input() ratioLock: boolean;

//     toggleConstraint(direction: string): void {
//         this.mutator.emit('burstchange');
//         this.constraint[direction] = !this.constraint[direction];

//         if (this.ratioLock) {
//             if (direction === 'width') {
//                 this.constraint.height = false;
//             }

//             if (direction === 'height') {
//                 this.constraint.width = false;
//             }
//         }

//         this.constraintChange.emit(this.constraint);
//     }
// }
