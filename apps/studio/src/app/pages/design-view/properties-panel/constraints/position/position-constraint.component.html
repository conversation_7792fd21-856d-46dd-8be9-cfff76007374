<div
    class="bar left"
    [ngClass]="{ active: constraint.left }"
    (click)="toggleConstraint('left')"></div>
<div
    class="bar right"
    [ngClass]="{ active: constraint.right }"
    (click)="toggleConstraint('right')"></div>
<div
    class="bar top"
    [ngClass]="{ active: constraint.top }"
    (click)="toggleConstraint('top')"></div>
<div
    class="bar bottom"
    [ngClass]="{ active: constraint.bottom }"
    (click)="toggleConstraint('bottom')"></div>
<div class="center"></div>
