:host {
    display: block;
    width: 5.4rem;
    height: 5.4rem;
    background: var(--studio-color-background-second);
    margin-left: 1rem;
    margin-right: 1rem;
    border-radius: 0.3rem;
    border: 1px solid var(--studio-color-border-second);
    position: relative;
}

.bar {
    position: absolute;
    background: var(--studio-color-border-second);
    cursor: pointer;

    &.active {
        background: var(--studio-color-primary);
        z-index: 1;
    }

    &:before {
        content: '';
        position: absolute;
        left: -0.3rem;
        right: -0.3rem;
        top: -0.3rem;
        bottom: -0.3rem;
    }
}

.width {
    height: 0.2rem;
    left: 0.6rem;
    right: 0.6rem;
    top: 50%;
    margin-top: -0.1rem;
}

.height {
    width: 0.2rem;
    top: 0.6rem;
    bottom: 0.6rem;
    left: 50%;
    margin-right: -0.1rem;
}
