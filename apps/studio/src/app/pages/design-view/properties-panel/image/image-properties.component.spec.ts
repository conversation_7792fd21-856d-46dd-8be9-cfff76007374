import { provideHttpClient } from '@angular/common/http';
import { createMockCreativesetDataService } from '@mocks/creativeset.mock';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { ComponentRef } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { InMemoryCache } from '@apollo/client/cache';
import { UIModule } from '@bannerflow/ui';
import { ElementKind } from '@domain/elements';
import { createImageElementAssetMock } from '@mocks/brand-library.mock';
import { createDataNodeMock } from '@mocks/element.mock';
import { createMockBrandLibraryDataService } from '@mocks/services/brand-library-data-service.mock';
import { createEditorEventServiceMock } from '@mocks/services/editor-event-service.mock';
import { createMockPropertiesService } from '@mocks/services/properties-service.mock';
import { CreativesetDataService } from '@studio/common';
import { provideEnvironment } from '@studio/common/environment/environment.provider';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { EventLoggerService } from '@studio/monitoring/events';
import { provideApollo } from 'apollo-angular';
import { EMPTY, of } from 'rxjs';
import { environment } from '../../../../../environments/environment.test';
import { AIStudioDialogService } from '../../../../shared/ai-studio/ai-studio-dialog.service';
import { AIStudioService } from '../../../../shared/ai-studio/ai-studio.service';
import { StudioUISectionComponent } from '../../../../shared/components/section/studio-ui-section/studio-ui-section.component';
import { CreativeSetShowcaseService } from '../../../../shared/creativeset-showcase/state/creativeset-showcase.service';
import { PermissionsDirective } from '../../../../shared/directives/permissions.directive';
import { BrandLibraryDataService } from '../../../../shared/media-library/brand-library.data.service';
import { defineMatchMedia } from '../../../../shared/mocks/matchMedia.mock';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../../../shared/mocks/store.mock';
import { AssetPickerDropdownComponent } from '../../asset-picker/asset-picker-dropdown/asset-picker-dropdown.component';
import { AssetPickerUploadService } from '../../asset-picker/asset-picker-upload.service';
import { AssetPickerComponent } from '../../asset-picker/asset-picker.component';
import { AssetPickerService } from '../../asset-picker/asset-picker.service';
import { DesignViewComponent } from '../../design-view.component';
import { BrandLibraryElementService } from '../../media-library/brandlibrary-element.service';
import { EditorEventService } from '../../services/editor-event/editor-event.service';
import { EditorSaveStateService } from '../../services/editor-save-state.service';
import { EditorStateService } from '../../services/editor-state.service';
import { ElementReplaceService } from '../../services/element-replace.service';
import { ElementSelectionService } from '../../services/element-selection.service';
import { MutatorService } from '../../services/mutator.service';
import { AssetPropertyComponent } from '../asset-property/asset-property.component';
import { PropertiesService } from '../properties.service';
import { QualityOptionsComponent } from '../quality-options/quality-options.component';
import { SizeModeOptionsComponent } from './size-mode/size-mode-options.component';
import { ImageOptimizationComponent } from './image-optimization/image-optimization.component';
import { ImagePropertiesComponent } from './image-properties.component';

describe('ImagePropertiesComponent', () => {
    let component: ImagePropertiesComponent;
    let componentRef: ComponentRef<ImagePropertiesComponent>;
    let fixture: ComponentFixture<ImagePropertiesComponent>;

    const svgImageElement = createDataNodeMock({
        kind: ElementKind.Image,
        imageAsset: createImageElementAssetMock({ url: 'image.svg' })
    });
    const pngImageElement = createDataNodeMock({
        kind: ElementKind.Image,
        imageAsset: createImageElementAssetMock({ url: 'image.png' })
    });
    const gifImageElement = createDataNodeMock({
        kind: ElementKind.Image,
        imageAsset: createImageElementAssetMock({ url: 'image.gif' })
    });

    beforeEach(() => {
        defineMatchMedia();
        TestBed.configureTestingModule({
            declarations: [
                AssetPickerComponent,
                AssetPropertyComponent,
                SizeModeOptionsComponent,
                ImageOptimizationComponent,
                ImagePropertiesComponent,
                QualityOptionsComponent
            ],
            imports: [
                AssetPickerDropdownComponent,
                PermissionsDirective,
                StudioUISectionComponent,
                UIModule
            ],
            providers: [
                provideEnvironment(environment),
                EnvironmentService,
                provideApollo(() => ({ cache: new InMemoryCache() })),
                provideHttpClient(),
                provideHttpClientTesting(),
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER,
                { provide: AIStudioDialogService, useValue: {} },
                { provide: AIStudioService, useValue: {} },
                { provide: AssetPickerService, useValue: { assetSelected$: of(undefined) } },
                { provide: AssetPickerUploadService, useValue: { assetSelected$: of(undefined) } },
                { provide: BrandLibraryDataService, useValue: createMockBrandLibraryDataService() },
                { provide: BrandLibraryElementService, useValue: {} },
                {
                    provide: CreativesetDataService,
                    useValue: createMockCreativesetDataService()
                },
                { provide: EditorEventService, useValue: createEditorEventServiceMock() },
                { provide: CreativeSetShowcaseService, useValue: {} },
                { provide: EditorSaveStateService, useValue: {} },
                { provide: EditorStateService, useValue: {} },
                { provide: ElementReplaceService, useValue: {} },
                { provide: ElementSelectionService, useValue: { change$: EMPTY } },
                { provide: EventLoggerService, useValue: {} },
                { provide: MutatorService, useValue: {} },
                { provide: PropertiesService, useValue: createMockPropertiesService() },
                { provide: DesignViewComponent, useValue: {} }
            ]
        });

        fixture = TestBed.createComponent(ImagePropertiesComponent);
        fixture.autoDetectChanges();
        component = fixture.componentInstance;
        componentRef = fixture.componentRef;
    });

    it('should disable image optimization and size mode for SVG files', () => {
        componentRef.setInput('images', [svgImageElement]);

        expect(component.isImageOptimizationEnabled).toBe(false);
        expect(component.isSizeModeEnabled).toBe(false);
    });

    it('should enable image optimization and size mode for PNG files', () => {
        componentRef.setInput('images', [pngImageElement]);
        fixture.detectChanges();

        expect(component.isImageOptimizationEnabled).toBe(true);
        expect(component.isSizeModeEnabled).toBe(true);
    });

    it('should enable image optimization and size mode for GIF files', () => {
        componentRef.setInput('images', [gifImageElement]);
        fixture.detectChanges();

        expect(component.isImageOptimizationEnabled).toBe(true);
        expect(component.isSizeModeEnabled).toBe(true);
    });

    it('should disable image optimization and size mode if at least one of the files are SVG', () => {
        componentRef.setInput('images', [gifImageElement, svgImageElement, pngImageElement]);

        expect(component.isImageOptimizationEnabled).toBe(false);
        expect(component.isSizeModeEnabled).toBe(false);
    });
});
