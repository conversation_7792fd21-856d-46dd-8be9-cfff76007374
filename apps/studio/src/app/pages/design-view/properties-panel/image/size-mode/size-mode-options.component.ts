import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    DestroyRef,
    EventEmitter,
    inject,
    Input,
    OnInit,
    Output
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { isImageNode } from '@creative/nodes/helpers';
import { ImageSizeMode } from '@domain/image';
import { IImageElementDataNode } from '@domain/nodes';
import { isFilledImage } from '@studio/utils/media';
import { filter } from 'rxjs';
import { ElementChangeType } from '../../../services/editor-event/element-change';
import { EditorStateService } from '../../../services/editor-state.service';
import { MutatorService } from '../../../services/mutator.service';
import { PropertiesService } from '../../properties.service';

@Component({
    selector: 'size-mode-options',
    templateUrl: './size-mode-options.component.html',
    styleUrls: ['size-mode-options.component.scss', '../../common.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class SizeModeOptionsComponent implements OnInit {
    private changeDetector = inject(ChangeDetectorRef);
    private propertiesService = inject(PropertiesService);
    private editorStateService = inject(EditorStateService);
    private mutatorService = inject(MutatorService);
    private destroyRef = inject(DestroyRef);

    @Input() sizeMode: ImageSizeMode | 'mixed';
    @Output() sizeModeChanged = new EventEmitter<ImageSizeMode>();

    classEmptyState = false;
    ImageSizeMode = ImageSizeMode;

    constructor() {
        this.propertiesService.dataElementChange$
            .pipe(takeUntilDestroyed(), filter(isImageNode))
            .subscribe(imageElement => {
                if (isFilledImage(imageElement)) {
                    this.centerFilledDynamicImage(imageElement);
                }
            });
    }

    ngOnInit(): void {
        this.propertiesService
            .observeDataElementOrStateChange()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(({ element }) => {
                if (isImageNode(element)) {
                    this.sizeMode = element.imageSettings.sizeMode;
                    this.changeDetector.detectChanges();
                }
                this.classEmptyState = this.propertiesService.stateValueIsUndefined('imageSettings');
            });
    }

    setSizeMode(option: ImageSizeMode): void {
        this.sizeModeChanged.emit(option);
    }

    private centerFilledDynamicImage(element: IImageElementDataNode): void {
        const isDynamic = this.editorStateService.isDynamicElement(element);
        if (isDynamic) {
            this.mutatorService.setImageSettings(
                element,
                {
                    x: 0.5,
                    y: 0.5
                },
                ElementChangeType.Skip
            );
        }
    }
}
