import { ElementRef, NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UIModule } from '@bannerflow/ui';
import { UserService } from '@studio/common';
import { EMPTY, of } from 'rxjs';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../../../../shared/mocks/store.mock';
import { PropertiesService } from '../../properties.service';
import { QualityOptionsComponent } from '../../quality-options/quality-options.component';
import { ImageOptimizationComponent } from './image-optimization.component';

class MockElementRef extends ElementRef {
    constructor() {
        super(null);
    }
}

describe('ImageOptimization', () => {
    let component: ImageOptimizationComponent;
    let fixture: ComponentFixture<ImageOptimizationComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [ImageOptimizationComponent, QualityOptionsComponent],
            imports: [UIModule],
            providers: [
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER,
                { provide: ElementRef, useValue: new MockElementRef() },
                {
                    provide: PropertiesService,
                    useValue: {
                        observeDataElementOrStateChange: (): any => EMPTY,
                        getPlaceholderValue: (): any => 0.85
                    }
                },
                {
                    provide: UserService,
                    useValue: {
                        isEmployee$: of(true)
                    }
                }
            ],
            schemas: [NO_ERRORS_SCHEMA]
        }).compileComponents();
        fixture = TestBed.createComponent(ImageOptimizationComponent);
        fixture.detectChanges();
        component = fixture.componentInstance;
    });

    it('should create component', () => {
        component.elements = [];
        expect(component).toBeTruthy();
    });

    it('should show the hint text when IO deactivated', () => {
        component.isImageOptimized = false;
        component.mixedIO = false;
        fixture.detectChanges();
        expect(fixture.nativeElement.querySelector('.hint-text')).toBeTruthy();
    });

    it('should show the highDpi option', () => {
        component.isImageOptimized = true;
        component.mixedIO = false;
        fixture.nativeElement.querySelector('#interaction-image-optimization-toggle').click();
        fixture.detectChanges();
        expect(fixture.nativeElement.querySelector('.setting-row')).toBeTruthy();
    });
});
