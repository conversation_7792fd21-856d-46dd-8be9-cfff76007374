import {
    ChangeDetectionStrategy,
    Component,
    DestroyRef,
    EventEmitter,
    Input,
    OnChanges,
    OnInit,
    Output,
    SimpleChanges,
    inject
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Logger } from '@bannerflow/sentinel-logger';
import { DEFAULT_IMAGE_QUALITY } from '@domain/image';
import { IImageElementDataNode } from '@domain/nodes';
import { UserService } from '@studio/common';
import { BehaviorSubject } from 'rxjs';
import { PropertiesService } from '../../properties.service';

type ImageQuality = number | undefined;
@Component({
    selector: 'image-optimization',
    templateUrl: './image-optimization.component.html',
    styleUrls: ['./image-optimization.component.scss', '../../common.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class ImageOptimizationComponent implements OnInit, OnChanges {
    @Input() elements: IImageElementDataNode[] = [];

    @Output() highDpiChange = new EventEmitter<boolean>();
    @Output() qualityChange = new EventEmitter<ImageQuality>();

    isImageOptimized = true; // show the section if one element at least is optimized
    highDpi = false;
    mixedQualities = false;
    mixedIO = false;
    mixedHighDpi = false;

    private logger = new Logger('ImageOptimizationComponent');
    private propertiesService = inject(PropertiesService);
    private userService = inject(UserService);
    private destroyRef = inject(DestroyRef);

    private _imageQuality$: BehaviorSubject<ImageQuality> = new BehaviorSubject<ImageQuality>(
        undefined
    );
    imageQuality$ = this._imageQuality$.asObservable();
    isEmployee$ = this.userService.isEmployee$;

    ngOnInit(): void {
        this.propertiesService
            .observeDataElementOrStateChange<IImageElementDataNode>()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(({ element }) => {
                const imageSettings = element.imageSettings;
                if (imageSettings) {
                    const { quality, highDpi } = imageSettings;
                    this._imageQuality$.next(quality);
                    this.isImageOptimized = quality !== undefined;
                    this.highDpi = highDpi || false;
                }
            });
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes.elements && this.elements.length) {
            const quality = this.elements[0].imageSettings.quality;
            const highDPI = this.elements[0].imageSettings.highDpi;
            this.mixedQualities = this.elements.some(
                element => element.imageSettings.quality !== quality
            );
            this.mixedHighDpi = this.elements.some(
                element => element.imageSettings.highDpi !== highDPI
            );
            this.highDpi = this.elements.every(element => element.imageSettings.highDpi);
            if (!this.mixedQualities) {
                this._imageQuality$.next(quality);
                this.isImageOptimized = quality !== undefined;
            } else {
                const hasQuality = this.elements.every(element => element.imageSettings.quality);
                this.mixedIO = !hasQuality;
            }
        }
    }

    setImageOptimization(optimized: boolean): void {
        this.isImageOptimized = optimized;
        this.logger.verbose(`Set image optimization - ${this.isImageOptimized}`);

        let quality: ImageQuality;

        if (this.isImageOptimized) {
            quality = DEFAULT_IMAGE_QUALITY; // Defaulting quality to 85
        } else if (this.highDpi) {
            this.setHighDpi(false);
        }

        this.setImageQuality(quality);
    }

    setHighDpi(highDPI: boolean): void {
        this.highDpi = highDPI;
        this.highDpiChange.emit(this.highDpi);
    }

    setImageQuality(quality: ImageQuality): void {
        this._imageQuality$.next(quality);
        this.qualityChange.emit(quality);
    }
}
