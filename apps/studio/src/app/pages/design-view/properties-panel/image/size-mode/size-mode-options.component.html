<div class="setting-row alternate">
    <div class="select-label">Size mode</div>
    <div class="setting-value select col-1 wide">
        <ui-select
            class="size-mode-options"
            [(selected)]="sizeMode"
            (selectedChange)="setSizeMode($event)"
            [class.empty-state]="classEmptyState"
            [useTargetWidth]="true">
            @if (sizeMode === 'mixed') {
                <ui-option value="mixed">Mixed</ui-option>
                <div class="option-divider"></div>
            }
            <ui-option
                id="size-mode-fill"
                [value]="ImageSizeMode.Fill">
                Fill
            </ui-option>
            <ui-option
                id="size-mode-fit"
                [value]="ImageSizeMode.Fit">
                Fit
            </ui-option>
            <ui-option
                id="size-mode-stretch"
                [value]="ImageSizeMode.Stretch">
                Stretch
            </ui-option>
        </ui-select>
    </div>
</div>
