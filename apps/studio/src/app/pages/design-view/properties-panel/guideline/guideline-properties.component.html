<studio-ui-section headline="Guideline">
    <div class="setting padding-top">
        <!-- Position -->
        <div class="setting-row">
            <div class="setting-label">Position</div>
            <div class="setting-value col-2">
                <div class="property-input">
                    @if (guideline.type === 'vertical') {
                        <ui-number-input
                            [(value)]="x"
                            (valueChange)="updatePosition()"
                            [allowEmpty]="false"
                            [disableUndo]="true"
                            (submit)="updatePosition()"
                            unitLabel="X"></ui-number-input>
                    }
                </div>
                <div class="property-input">
                    @if (guideline.type === 'horizontal') {
                        <ui-number-input
                            [(value)]="y"
                            (valueChange)="updatePosition()"
                            [allowEmpty]="false"
                            [disableUndo]="true"
                            (submit)="updatePosition()"
                            unitLabel="Y"></ui-number-input>
                    }
                </div>
            </div>
        </div>
    </div>
</studio-ui-section>
