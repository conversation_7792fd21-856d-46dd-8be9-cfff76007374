:host {
    display: block;
    width: inherit;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    background: var(--studio-color-grey-98);
    transition: opacity 0.1s ease;
    padding-bottom: 8rem;

    &.disabled {
        opacity: 0.5;
        pointer-events: none;
    }
}

.state-tabs-offset {
    padding-top: 2.9rem;
}

:host ::ng-deep .toggle-button {
    position: relative;
    height: 1.6rem;
    width: 4.5rem;
    text-align: right;
    font-size: 0.8rem;
    margin: 0.4rem 0;
    border-radius: 10rem;
    line-height: 1.6rem;
    padding: 0 0.5rem 0 0;
    color: var(--studio-color-background-second);
    cursor: pointer;

    &.on {
        background: var(--studio-color-primary);
    }

    &.off {
        background: var(--studio-color-second);
        width: 5.7rem;
        text-align: left;
        padding: 0 0 0 0.5rem;

        &:after {
            left: unset;
            right: 0.2rem;
        }
    }
}

:host ::ng-deep .color-picker-outlet {
    margin: var(--section-padding) calc(-1 * var(--section-padding)) calc(-1 * var(--section-padding))
        calc(-1 * var(--section-padding));
    display: block;

    &:not(:last-child) {
        margin-bottom: var(--section-padding);
    }

    &:empty {
        display: none;
    }
}

.settings {
    width: 22rem;
}

:where(:root[data-uinew]) :host .settings {
    width: 27rem;
}
