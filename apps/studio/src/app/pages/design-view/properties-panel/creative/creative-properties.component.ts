import { Component, Input, OnInit, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MIN_ELEMENT_DURATION } from '@creative/animation.utils';
import { Color } from '@creative/color';
import { isVideoNode } from '@creative/nodes';
import { IAnimator } from '@domain/creative/animator.header';
import { IGifExport, IPreloadImage } from '@domain/creativeset/creative';
import { ICreativeDataNode } from '@domain/nodes';
import { ISocialPlacement } from '@domain/social';
import { UserSettingsService } from '@studio/common/user-settings';
import { ISocialGuide, ISocialNetwork } from '@studio/domain/social';
import { Observable, combineLatest, merge } from 'rxjs';
import { filter, switchMap } from 'rxjs/operators';
import { SocialGuideService } from '../../../../shared/services/social-guide.service';
import { ColorService } from '../../color-section/color.service';
import { DesignViewComponent } from '../../design-view.component';
import { EditorEventService, ElementChangeType } from '../../services/editor-event';
import { EditorStateService } from '../../services/editor-state.service';
import { HistoryService } from '../../services/history.service';
import { MutatorService } from '../../services/mutator.service';
import { MAX_TIMELINE_DURATION } from '../../timeline/timeline.constants';

@Component({
    selector: 'creative-properties',
    templateUrl: './creative-properties.component.html',
    styleUrls: ['./creative-properties.component.scss', '../common.scss'],
    standalone: false
})
export class CreativePropertiesComponent implements OnInit {
    private historyService = inject(HistoryService);
    private socialGuideService = inject(SocialGuideService);
    private colorService = inject(ColorService);
    private userSettingsService = inject(UserSettingsService);
    private mutatorService = inject(MutatorService);
    private editorEvent = inject(EditorEventService);
    private editorStateService = inject(EditorStateService);
    private designViewComponent = inject(DesignViewComponent);

    @Input() animator: IAnimator;

    fill = new Color();
    duration = 0;
    minTimelineDuration = MIN_ELEMENT_DURATION;
    maxTimelineDuration = MAX_TIMELINE_DURATION;
    loops = 0;
    startTime: number;
    stopTime: number | undefined;
    audio = false;
    preloadImage: IPreloadImage;
    gifExport: IGifExport;
    creativeDocument: ICreativeDataNode;
    ElementChangeType = ElementChangeType;
    isAnyVideoElement = false;

    isEmployee$: Observable<boolean>;
    socialNetworks$: Observable<ISocialNetwork[]>;
    selectedNetwork$: Observable<ISocialNetwork>;
    selectedPlacement$: Observable<ISocialPlacement>;
    showOverlay$: Observable<boolean>;
    showGuidelines$: Observable<boolean>;
    isSocialCreative$: Observable<boolean>;
    guidelineVisible$: Observable<boolean>;

    constructor() {
        this.creativeDocument = this.editorStateService.creativeDataNode;
        this.isSocialCreative$ = this.socialGuideService.isSocialCreative$;
        this.socialNetworks$ = this.socialGuideService.socialNetworks$;
        this.selectedNetwork$ = this.socialGuideService.selectedNetwork$;
        this.selectedPlacement$ = this.socialGuideService.selectedPlacement$;
        this.showOverlay$ = this.socialGuideService.showOverlay$;
        this.showGuidelines$ = this.socialGuideService.showGuidelines$;
        this.guidelineVisible$ = this.userSettingsService.guidelineVisible$;

        merge(
            this.historyService.snapshotApply$,
            this.editorEvent.creative.change$,
            this.editorStateService.renderer$
        )
            .pipe(takeUntilDestroyed())
            .subscribe(() => {
                this.creativeDocument = this.mutatorService.creativeDocument;
                this.updateProperties();
            });

        this.isSocialCreative$
            .pipe(
                takeUntilDestroyed(),
                filter(isSocial => isSocial),
                switchMap(() =>
                    combineLatest({
                        network: this.selectedNetwork$,
                        placement: this.selectedPlacement$,
                        guidelines: this.showGuidelines$,
                        overlay: this.showOverlay$
                    })
                )
            )
            .subscribe((socialGuide: ISocialGuide) => {
                this.setSocialGuide(socialGuide);
            });
    }

    ngOnInit(): void {
        this.updateProperties();
        this.socialGuideService.initSocialCreative(this.creativeDocument);
    }

    toggleColorPicker(): void {
        this.colorService.toggleColorPicker('creativeFill');
    }

    setFill(fill: Color, eventType?: ElementChangeType): void {
        this.fill = fill;
        this.updateFill(eventType);
    }

    setFillAlpha(alpha: number, eventType?: ElementChangeType): void {
        const newColor = new Color(this.fill);
        newColor.alpha = alpha;
        this.setFill(newColor, eventType);
    }

    updateFill(eventType?: ElementChangeType): void {
        this.mutatorService.setCreativeAppearance('fill', this.fill, eventType);
    }

    previewFill(color: Color): void {
        this.mutatorService.setCreativeAppearance('fill', color);
    }

    previewStop(color: Color): void {
        this.fill = color;
        this.updateFill();
    }

    setLoops(): void {
        this.mutatorService.setCreativeLoops(this.loops);

        if (this.loops === 0) {
            this.stopTime = undefined;
        } else {
            this.stopTime = this.creativeDocument.getStopTime_m();
        }

        this.setStopTime(false);
    }

    showManualGifFrames(show: boolean): void {
        this.mutatorService.showManualGifFrames(show);
    }

    setStopTime(emitChanges = true): void {
        this.stopTime = Math.max(0, this.stopTime ?? 0);
        this.mutatorService.setCreativeStopTime(this.stopTime, emitChanges);
    }

    setAudio(enabled: boolean): void {
        this.audio = enabled;
        this.mutatorService.setCreativeAudio(enabled);
    }

    setStartTime(emitChanges = true): void {
        this.mutatorService.setAnimationStartTime(this.startTime, emitChanges);
        this.startTime = Math.max(0, this.startTime);
    }

    setDuration(): void {
        this.mutatorService.setCreativeDuration(this.duration);
    }

    setPreloadImage(): void {
        this.mutatorService.setPreloadImage(this.preloadImage);
    }

    undo(): void {
        this.historyService.undo$.next();
    }

    redo(): void {
        this.historyService.redo$.next();
    }

    setNetwork(network: ISocialNetwork): void {
        this.socialGuideService.selectNetwork(network);
    }

    setPlacement(placement: ISocialPlacement): void {
        this.socialGuideService.selectPlacement(placement);
    }

    showSocialOverlay(show: boolean): void {
        this.socialGuideService.setShowOverlay(show);
    }

    showSocialGuidelines(show: boolean): void {
        this.socialGuideService.setShowGuidelines(show);
    }

    private getIsAnyVideoElement(): boolean {
        return this.editorStateService.creativeDataNode.elements.some(isVideoNode);
    }

    private updateProperties(): void {
        const { audio, fill, loops, preloadImage, gifExport, startTime } = this.creativeDocument;
        this.isAnyVideoElement = this.getIsAnyVideoElement();

        if (!this.isAnyVideoElement && this.audio) {
            this.setAudio(false);
        }

        this.fill = fill;
        this.loops = loops;
        this.audio = audio ?? false;
        this.stopTime = this.creativeDocument.getStopTime_m();
        this.startTime = startTime ?? 0;
        this.preloadImage = { ...preloadImage };
        this.gifExport = gifExport;

        if (!this.preloadImage.frames || this.preloadImage.frames.length === 0) {
            this.preloadImage.frames = [this.creativeDocument.getFirstPreloadImageFrame()];
        }

        if (this.animator) {
            this.duration = this.creativeDocument.duration;
        }
    }

    private setSocialGuide(socialGuide: ISocialGuide): void {
        this.mutatorService.setCreativeSocialGuide(socialGuide);
        this.designViewComponent.workspace.gizmoDrawer.draw();
    }
}
