ui-select {
    ::ng-deep {
        .button {
            min-width: 100px;
            max-width: 100px;

            .text {
                .text-wrapper {
                    overflow: unset;
                }
            }
        }
    }
}

.setting-row {
    &.auto {
        max-height: 22px;
        height: 22px;
    }

    &.wide-text {
        grid-gap: unset;
    }

    .time,
    .duration {
        justify-self: end;
        max-width: 100px;
    }

    .stop {
        justify-self: end;
    }

    &.network,
    &.placement {
        ui-svg-icon {
            --color: var(--studio-color-text-third);
            cursor: pointer;
            transition: color 0.2s ease;

            &:hover {
                color: var(--studio-color-text-second);
            }
        }

        .setting-value {
            display: inline-flex;
            justify-self: flex-end;
            grid-gap: 3px;
        }

        .property-input {
            .social-guide-placement {
                ::ng-deep {
                    .button {
                        width: 117px;
                    }
                }
            }

            ui-select {
                ::ng-deep {
                    .button {
                        max-width: 120px;
                    }
                }
            }
        }
    }

    &.placement {
        .setting-value {
            min-width: 117px;
        }
    }
}
