import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UIModule } from '@bannerflow/ui';
import { ElementKind } from '@domain/elements';
import { createCreativeDataNodeFixture } from '@fixtures/data-node.fixture';
import { createMockEditorStateService } from '@mocks/editor.mock';
import { createDataNodeMock } from '@mocks/element.mock';
import { createEditorEventServiceMock } from '@mocks/services/editor-event-service.mock';
import { createMutatorServiceMock } from '@mocks/services/mutator-service.mock';
import { provideMock } from '@studio/testing/utils/provide-mock';
import { of } from 'rxjs';
import { ColorButtonComponent } from '../../../../shared/components/color/color-button/color-button.component';
import { StudioUISectionComponent } from '../../../../shared/components/section/studio-ui-section/studio-ui-section.component';
import { defineMatchMedia } from '../../../../shared/mocks/matchMedia.mock';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../../../shared/mocks/store.mock';
import { SocialGuideService } from '../../../../shared/services/social-guide.service';
import { ColorPickerPipe } from '../../color-section/color-picker.pipe';
import { ColorService } from '../../color-section/color.service';
import { DesignViewComponent } from '../../design-view.component';
import { EditorEventService } from '../../services/editor-event/editor-event.service';
import { EditorStateService } from '../../services/editor-state.service';
import { HistoryService } from '../../services/history.service';
import { MutatorService } from '../../services/mutator.service';
import { CreativePropertiesComponent } from './creative-properties.component';

describe('CreativePropertiesComponent', () => {
    let component: CreativePropertiesComponent;
    let fixture: ComponentFixture<CreativePropertiesComponent>;

    let editorStateServiceMock: EditorStateService;
    let mutatorServiceMock: Partial<MutatorService>;

    beforeAll(() => {
        defineMatchMedia();
    });

    beforeEach(() => {
        const node = createDataNodeMock();
        const creativeDocument = createCreativeDataNodeFixture(undefined, [node]);
        editorStateServiceMock = createMockEditorStateService({ creativeDataNode: creativeDocument });
        mutatorServiceMock = createMutatorServiceMock({
            setCreativeAudio: (audio: boolean) => {
                editorStateServiceMock.creativeDataNode.audio = audio;
            }
        });

        TestBed.configureTestingModule({
            declarations: [CreativePropertiesComponent],
            imports: [UIModule, StudioUISectionComponent, ColorButtonComponent, ColorPickerPipe],
            providers: [
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER,
                provideMock(HistoryService),
                provideMock(SocialGuideService, {
                    isSocialCreative$: of(false),
                    initSocialCreative: jest.fn()
                }),
                provideMock(ColorService, { isColorPickerOpen: jest.fn() }),
                provideMock(MutatorService, mutatorServiceMock),
                provideMock(EditorEventService, createEditorEventServiceMock()),
                provideMock(EditorStateService, editorStateServiceMock),
                provideMock(DesignViewComponent)
            ]
        }).compileComponents();

        fixture = TestBed.createComponent(CreativePropertiesComponent);
        component = fixture.componentInstance;
    });

    it('should show opt-in audio toggle when video element is present', () => {
        const videoElement = createDataNodeMock({ kind: ElementKind.Video });
        editorStateServiceMock.creativeDataNode.setNodes_m([videoElement]);

        fixture.detectChanges();

        const audioToggleElement = fixture.nativeElement.querySelector(
            '#interaction-creative-audio-setting'
        );
        expect(audioToggleElement).toBeTruthy();
    });

    it('should hide opt-in audio toggle when no video element is present', () => {
        const rectangleElement = createDataNodeMock();
        editorStateServiceMock.creativeDataNode.setNodes_m([rectangleElement]);

        fixture.detectChanges();

        const audioToggleElement = fixture.nativeElement.querySelector(
            '#interaction-creative-audio-setting'
        );
        expect(audioToggleElement).toBeFalsy();
    });

    it('should hide opt-in audio & disable "audio" property on the creative when no video element is present', () => {
        const setAudioSpy = jest.spyOn(mutatorServiceMock, 'setCreativeAudio');
        editorStateServiceMock.creativeDataNode.audio = true;
        component.audio = true;
        editorStateServiceMock.creativeDataNode.setNodes_m([]);

        fixture.detectChanges();

        const audioToggleElement = fixture.nativeElement.querySelector(
            '#interaction-creative-audio-setting'
        );
        expect(audioToggleElement).toBeFalsy();
        expect(setAudioSpy).toHaveBeenCalledWith(false);
    });
});
