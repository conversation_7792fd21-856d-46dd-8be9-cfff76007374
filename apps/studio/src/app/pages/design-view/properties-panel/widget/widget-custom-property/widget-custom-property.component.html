<div class="setting">
    <div
        class="setting-row auto"
        [class.full-width]="property.unit === 'image'"
        [class.input-width]="property.unit === 'feed'">
        @if (property.unit !== 'feed' && property.unit !== 'image') {
            <div
                class="setting-label"
                [uiTooltip]="property.label || ''">
                {{ property.label }}
            </div>
        }
        @if (feedPicker && property.unit === 'feed') {
            <div
                class="setting-label"
                [uiTooltip]="
                    feedPicker && feedPicker.selectedFeed
                        ? feedPicker.selectedFeed.name
                        : 'No feed selected'
                ">
                {{ property.label }}
            </div>
        }
        <div class="setting-value property">
            <div
                class="property-input"
                [attr.data-test-id]="property.label">
                @switch (property.unit) {
                    @case ('text') {
                        <ui-textarea
                            [placeholder]="property.label || ''"
                            [value]="$any(getVersionedPropertyValue(property)) || ''"
                            (valueChange)="onTextPropertyChange($event)"
                            [blurOnSubmit]="
                                property.label
                                    ? property.label.toLowerCase().indexOf('url') > -1
                                    : false
                            "
                            [maxRows]="
                                property.label
                                    ? property.label.toLowerCase().indexOf('url') > -1
                                        ? 1
                                        : 0
                                    : 0
                            "
                            [maxCharacters]="3000"
                            (undo)="undo()"
                            (redo)="redo()"></ui-textarea>
                    }

                    @case ('number') {
                        <ui-number-input
                            [allowEmpty]="true"
                            [value]="$any(property.value) ?? 0"
                            [step]="1"
                            [min]="-9999999"
                            [max]="9999999"
                            (valueChange)="setCustomPropertyValue($event)"
                            (undo)="undo()"
                            (redo)="redo()"
                            [disableUndo]="true">
                        </ui-number-input>
                    }
                    @case ('boolean') {
                        <ui-toggle-switch
                            (undo)="undo()"
                            (redo)="redo()"
                            (selectedChange)="setCustomPropertyValue($event)"
                            [selected]="$any(property.value)"
                            ui-theme="tiny">
                        </ui-toggle-switch>
                    }

                    @case ('color') {
                        <color-button
                            [id]="'widget-color-button-' + index"
                            [color]="$any(property.value)"
                            (click)="toggleColorPicker(index)"
                            data-test-id="color-button">
                        </color-button>
                    }

                    @case ('select') {
                        <ui-select
                            (undo)="undo()"
                            (redo)="redo()"
                            [useTargetWidth]="true"
                            [selected]="getSelectedOption()">
                            @for (option of $any(property.value); track $index) {
                                <ui-option
                                    [value]="option.value"
                                    (select)="onSelectionChange($event)">
                                    <div
                                        [uiTooltip]="option.value"
                                        [uiTooltipPosition]="'bottom'"
                                        [uiTooltipHideArrow]="true">
                                        {{ option.value }}
                                    </div>
                                </ui-option>
                            }
                        </ui-select>
                    }

                    @case ('font') {
                        <font-picker
                            [selectedFontFamilyId]="$any(property.value).fontFamilyId"
                            [selectedFontStyleId]="$any(property.value).id"
                            [labels]="false"
                            (selectedFontChange)="selectedFontChanged($event)"
                            (previewFontChange)="previewFontChanged($event, property)"
                            (onPreviewStop)="onPreviewStop()">
                        </font-picker>
                    }

                    @case ('image') {
                        <asset-property
                            [context]="AssetPropertyContext.Widget"
                            [allowRemove]="true"
                            [label]="property.label || ''"
                            [asset]="getImageAssetById($any(property.value).id, property)"
                            (assetSelected)="onImageChanged($event)"
                            (assetRemoved)="onImageChanged(undefined)">
                        </asset-property>
                    }

                    @case ('feed') {
                        <feed-picker
                            #feedPicker
                            [value]="$any(getVersionedPropertyValue(property))"
                            (feedSelectionChanged)="onFeedSelectionChanged($event)">
                        </feed-picker>
                    }
                }
            </div>
        </div>
        @if (isDynamicTextSettingVisible(property)) {
            <div class="setting-label">
                Dynamic field
                @if (isFirstTextProperty) {
                    <ui-svg-icon
                        id="interaction-toggle-dynamic-field-text"
                        class="info"
                        icon="question-mark"
                        (mouseover)="hoverPopoverRoot.open(hoverTarget)"
                        ui-popover-target
                        #hoverTarget="ui-popover-target"></ui-svg-icon>
                }
            </div>
            <div class="setting-value property">
                <div class="property-input">
                    <ui-toggle-switch
                        [selected]="isDynamicTextEnabled(property)"
                        (selectedChange)="
                            onDynamicTextToggleChange($event, property)
                        "></ui-toggle-switch>
                </div>
            </div>
        }
    </div>
</div>

@if (isDynamicTextSettingVisible(property)) {
    <div class="body divider"></div>
}

@if (property.unit === 'color' && ('widgetColor' + index | picker | async)) {
    <div class="color-picker widget">
        <section-expand
            arrowPosition="187px"
            [showBackground]="true"
            [removeContentWhenCollapsed]="false"
            [expanded]="true">
            <color-section
                [name]="'widgetColor' + index"
                [preventCloseElements]="['#widget-properties']"
                [color]="$any(property.value)"
                (onColorChanged)="setCustomPropertyValue($event)">
            </color-section>
        </section-expand>
    </div>
}

<ui-popover
    #hoverPopoverRoot="ui-popover"
    [config]="{
        arrowPosition: 'bottom',
        position: 'top',
        width: 165,
        panelClass: 'no-padding',
        openOnHover: true
    }">
    <ng-template ui-popover-template>
        <div class="dynamic-text-popover-content">
            Learn more about working with dynamic content
            <a
                href="https://support.bannerflow.com/en/articles/11035801-dynamic-advertising-create-your-sda-catalogue#h_ff4bba45b7"
                rel="noopener"
                target="_blank"
                >here</a
            >
        </div>
    </ng-template>
</ui-popover>
