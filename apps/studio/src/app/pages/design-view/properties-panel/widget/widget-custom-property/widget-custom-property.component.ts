import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    DestroyRef,
    inject,
    Input,
    OnInit,
    ViewChild
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { createFeed, isVersionedFeed } from '@creative/elements/feed/feeds.utils';
import { isVersionedWidgetText } from '@creative/elements/rich-text/utils';
import { getPartialImageAssetByProperty } from '@creative/elements/widget/utils';
import { getElementPropertyByName } from '@creative/nodes';
import { ImageLibraryAsset, PartialLibraryAsset } from '@domain/brand/brand-library';
import { IVersion, IVersionProperty } from '@domain/creativeset/version';
import { IBfFeed } from '@domain/feed';
import { IFontStyle, ISelectedFont } from '@domain/font';
import {
    IWidgetCustomProperty,
    IWidgetElementDataNode,
    IWidgetImage,
    IWidgetSelectOption,
    OneOfCustomPropertyValue
} from '@domain/widget';
import { cloneDeep } from '@studio/utils/clone';
import { deepEqual } from '@studio/utils/utils';
import { Observable } from 'rxjs';
import { FeedPickerComponent } from '../../../../../shared/components';
import { BrandLibraryDataService } from '../../../../../shared/media-library/brand-library.data.service';
import { WidgetService } from '../../../../../shared/services/widget.service';
import { VersionsService } from '../../../../../shared/versions/state/versions.service';
import { IAssetSelectionEvent } from '../../../asset-picker/asset-picker.component';
import { ColorService } from '../../../color-section/color.service';
import { ElementChangeType } from '../../../services/editor-event/element-change';
import { HistoryService } from '../../../services/history.service';
import { MutatorService } from '../../../services/mutator.service';
import { AssetPropertyContext } from '../../asset-property/asset-property';

@Component({
    selector: 'widget-custom-property',
    templateUrl: 'widget-custom-property.component.html',
    styleUrls: ['../../common.scss', 'widget-custom-property.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class WidgetCustomPropertyComponent implements OnInit {
    private historyService = inject(HistoryService);
    private versionsService = inject(VersionsService);
    private colorService = inject(ColorService);
    private mutatorService = inject(MutatorService);
    private brandLibraryDataService = inject(BrandLibraryDataService);
    private destroyRef = inject(DestroyRef);
    private changeDetector = inject(ChangeDetectorRef);
    private widgetService = inject(WidgetService);

    @Input() property: IWidgetCustomProperty;
    @Input() index: number;
    @Input() customProperties: IWidgetCustomProperty[];
    @Input() isSDAEnabled: boolean;
    @Input() isFirstTextProperty: boolean;
    @Input() element: IWidgetElementDataNode;

    @ViewChild('feedPicker') feedPicker: FeedPickerComponent;

    AssetPropertyContext = AssetPropertyContext;
    selectedOptions: Record<string, IWidgetSelectOption> = {};
    versionedTextValue$: Observable<string | undefined>;
    private selectedVersion: IVersion;

    ngOnInit(): void {
        this.versionsService.selectedVersion$
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(selectedVersion => {
                this.selectedVersion = selectedVersion;
            });
    }

    onDynamicTextToggleChange(value: boolean, property: IWidgetCustomProperty): void {
        const elementProperty = getElementPropertyByName(this.element, property.name);

        if (elementProperty && value !== elementProperty.hasDynamicContent) {
            elementProperty.hasDynamicContent = value;
            const otherProperties = this.element.globalElement.properties.filter(
                ({ name }) => name !== elementProperty.name
            );
            const properties = [...otherProperties, elementProperty];

            this.mutatorService.setElementPropertyValue(
                this.element,
                'globalElement',
                { ...this.element.globalElement, properties },
                ElementChangeType.Instant
            );
        }
    }

    isDynamicTextEnabled(property: IWidgetCustomProperty): boolean {
        const elementProperty = getElementPropertyByName(this.element, property.name);
        if (!elementProperty) {
            throw new Error(`Property '${property.name}' not found in global element`);
        }

        return !!elementProperty.hasDynamicContent;
    }

    isDynamicTextSettingVisible(property: IWidgetCustomProperty): boolean {
        const isTextProperty = property.unit === 'text';
        const isSDAEnabled = this.isSDAEnabled;
        const isNotPreviewMode = !this.mutatorService.preview;

        return isTextProperty && isSDAEnabled && isNotPreviewMode;
    }

    onSelectionChange(value: string): void {
        const options = cloneDeep(this.property.value) as IWidgetSelectOption[];
        for (const option of options) {
            option.selected = option.value === value;
        }

        this.setCustomPropertyValue(options);
    }

    getSelectedOption(): string | undefined {
        if (this.property.unit === 'select') {
            const value = this.property.value as IWidgetSelectOption[];
            return value.find(option => option.selected)?.value;
        }
    }

    selectedFontChanged({ fontFamily, fontStyle }: ISelectedFont): void {
        const font: IFontStyle = {
            id: fontStyle.id,
            src: fontStyle.fontUrl,
            style: fontStyle.italic ? 'italic' : 'normal',
            weight: fontStyle.weight || 400,
            fontFamilyId: fontFamily.id
        };
        this.setCustomPropertyValue(font);
    }

    onTextPropertyChange(value: string): void {
        if (!isVersionedWidgetText(this.property)) {
            return;
        }

        if (this.property.value.text === value) {
            return;
        }

        let newValue = this.property.value;
        if (this.property.versionPropertyId) {
            newValue = {
                text: value
            };

            const updatedVersionProperty: IVersionProperty = {
                id: this.property.versionPropertyId,
                name: 'widgetText',
                value: newValue
            };

            this.versionsService.upsertVersionProperty(this.selectedVersion.id, updatedVersionProperty);
        } else {
            // redundant case?
            newValue.text = value;
        }

        this.setCustomPropertyValue(newValue);
    }

    onImageChanged(event: IAssetSelectionEvent | undefined): void {
        const imageAsset = event?.asset;

        // Empty fields means that the image is removed
        const widgetImage: IWidgetImage = {
            id: imageAsset?.id ?? '',
            src: imageAsset?.url ?? ''
        };

        this.setCustomPropertyValue(widgetImage);
    }

    previewFontChanged(
        { fontFamily, fontStyle }: Partial<ISelectedFont>,
        property: IWidgetCustomProperty
    ): void {
        const font: IFontStyle = {
            id: fontStyle?.id ?? '',
            src: fontStyle?.fontUrl ?? '',
            style: fontStyle?.italic ? 'italic' : 'normal',
            weight: fontStyle?.weight ?? 300,
            fontFamilyId: fontFamily?.id ?? ''
        };
        const previewedProperties = this.customProperties.map(prop => {
            if (prop.name === property.name) {
                return {
                    ...prop,
                    value: font
                };
            }
            return prop;
        });
        this.mutatorService.setElementPropertyValue(
            this.element,
            'customProperties',
            previewedProperties,
            ElementChangeType.Skip
        );
    }

    onPreviewStop(): void {
        this.mutatorService.setElementPropertyValue(
            this.element,
            'customProperties',
            this.customProperties,
            ElementChangeType.Skip
        );
    }

    toggleColorPicker(index: number): void {
        this.colorService.toggleColorPicker(`widgetColor${index}`);
    }

    getImageAssetById(
        id: string,
        property: IWidgetCustomProperty
    ): PartialLibraryAsset<ImageLibraryAsset> | undefined {
        const libraryAsset = this.brandLibraryDataService.getImageAssetById(id);

        if (libraryAsset) {
            return libraryAsset;
        }

        return getPartialImageAssetByProperty(property);
    }

    setCustomPropertyValue(value: OneOfCustomPropertyValue): void {
        if (deepEqual(this.property.value, value)) {
            return;
        }

        this.property.value = value;

        const newProperties = this.element.customProperties.map(property => {
            if (property.name === this.property.name) {
                return { ...property, value };
            }
            return property;
        });

        this.mutatorService.setElementPropertyValue(this.element, 'customProperties', newProperties);

        this.changeDetector.markForCheck();
    }

    onFeedSelectionChanged(feed: IBfFeed): void {
        const feedProperty = this.property;

        if (!feedProperty || !isVersionedFeed(feedProperty)) {
            throw new Error('Property not found in element');
        }

        const versionProperty = this.selectedVersion.properties.find(
            ({ id }) => id === feedProperty.versionPropertyId
        );

        if (isVersionedFeed(feedProperty)) {
            if (versionProperty) {
                this.versionsService.updateVersionPropertyFeed(
                    this.selectedVersion.id,
                    versionProperty.id,
                    feed.id
                );
            } else {
                this.versionsService.addVersionProperty(this.selectedVersion.id, {
                    name: 'feed',
                    id: feedProperty.versionPropertyId!,
                    value: createFeed(feed.id)
                });
            }
        }

        feedProperty.value.id = feed.id;
        this.setCustomPropertyValue(feedProperty.value);
    }

    /**
     * Retrieves the versioned value of a custom property.
     * The `true` parameter ensures that the method fetches the value considering the current version context.
     */
    getVersionedPropertyValue(
        property: IWidgetCustomProperty
    ): string | OneOfCustomPropertyValue | undefined {
        return this.widgetService.getVersionedCustomPropertyValue(property, true);
    }

    undo(): void {
        this.historyService.undo$.next();
    }

    redo(): void {
        this.historyService.redo$.next();
    }
}
