@if ((elements$ | async) && (selectedVersionProperties$ | async) && widgetProperties.length) {
    <studio-ui-section
        [headline]="isEffectWidget ? 'Effect properties' : 'Widget properties'"
        id="widget-properties"
        [class.no-padding]="true"
        [dropdownTarget]="dropdown">
        <ui-dropdown
            #dropdown
            width="195"
            [offset]="{ x: 18, y: 0 }"
            type="menu">
            <ui-dropdown-item
                *ngIf="!isBannerflowLibraryWidget"
                svgIcon="export"
                (click)="updateLibraryElementProperties()">
                Update properties in library
            </ui-dropdown-item>
            <ui-dropdown-item
                svgIcon="sync"
                (click)="resetProperties()">
                Reset to default
            </ui-dropdown-item>
            <ng-container *ngIf="isBannerflowLibraryWidget">
                <ui-dropdown-divider></ui-dropdown-divider>
                <ui-dropdown-item
                    svgIcon="question-mark-s"
                    (click)="openWidgetInfo()">
                    How it works</ui-dropdown-item
                >
            </ng-container>
        </ui-dropdown>
        @for (property of widgetProperties; track $index; let i = $index) {
            <widget-custom-property
                [property]="property"
                [element]="element"
                [customProperties]="widgetProperties"
                [isSDAEnabled]="isSDAEnabled"
                [isFirstTextProperty]="isFirstTextProperty(property)"
                [index]="i">
            </widget-custom-property>
        }
    </studio-ui-section>
}

<ui-dropdown
    #dropdown
    width="195"
    [offset]="{ x: 18, y: 0 }"
    type="menu">
    @if (!isBannerflowLibraryWidget) {
        <ui-dropdown-item
            svgIcon="export"
            (click)="updateLibraryElementProperties()">
            Update properties in library
        </ui-dropdown-item>
    }
    <ui-dropdown-item
        svgIcon="sync"
        (click)="resetProperties()">
        Reset to default
    </ui-dropdown-item>
    @if (isBannerflowLibraryWidget) {
        <ui-dropdown-divider></ui-dropdown-divider>
        <ui-dropdown-item
            svgIcon="question-mark-s"
            (click)="openWidgetInfo()">
            How it works</ui-dropdown-item
        >
    }
</ui-dropdown>

<ui-popover
    #hoverPopoverRoot="ui-popover"
    [config]="{
        arrowPosition: 'bottom',
        position: 'top',
        width: 165,
        panelClass: 'no-padding',
        openOnHover: true
    }">
    <ng-template ui-popover-template>
        <div class="dynamic-text-popover-content">
            Learn more about working with dynamic content
            <a
                href="https://support.bannerflow.com/en/articles/11035801-dynamic-advertising-create-your-sda-catalogue#h_ff4bba45b7"
                rel="noopener"
                target="_blank"
                >here</a
            >
        </div>
    </ng-template>
</ui-popover>
