@if (elements$ | async) {
    <studio-ui-section
        class="no-padding"
        headline="Dynamic content">
        <div class="setting">
            <div class="setting-row auto">
                <div class="setting-label">Dynamic field</div>
                <div class="setting-value end">
                    <ui-toggle-switch
                        [interim]="enabled.isMixed"
                        [selected]="enabled.value ?? false"
                        (selectedChange)="onEnabledChange($event)" />
                </div>
            </div>
            @if (enabled.value) {
                <div class="setting-row auto">
                    <div class="setting-label">Label</div>
                    <div class="setting-value end">
                        <ui-input
                            type="text"
                            [value]="label.isMixed ? undefined : label.value"
                            (valueChange)="onLabelChange($event)"
                            [placeholder]="label.isMixed ? 'Mixed' : '-'">
                        </ui-input>
                    </div>
                </div>
            }
        </div>
    </studio-ui-section>
}
