import { ChangeDetectionStrategy, Component, DestroyRef, Input, OnInit, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { IElementProperty } from '@domain/creativeset';
import { IElementDataNode, OneOfElementDataNodes } from '@domain/nodes';
import { createElementProperty } from '@studio/utils/element.utils';
import { generateUniqueName } from '@studio/utils/utils';
import { Observable } from 'rxjs';
import { EditorEventService } from '../../services/editor-event/editor-event.service';
import { ElementChangeType } from '../../services/editor-event/element-change';
import { EditorStateService } from '../../services/editor-state.service';
import { createMixedProperty } from '../mixed-properties';
import { getDynamicPropertyOfElement } from '@creative/nodes';

@Component({
    selector: 'dynamic-content-properties',
    templateUrl: './dynamic-content-properties.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    styleUrls: ['../common.scss'],
    standalone: false
})
export class DynamicContentPropertiesComponent implements OnInit {
    @Input() elements$: Observable<OneOfElementDataNodes[]>;

    elements: OneOfElementDataNodes[] = [];
    label = createMixedProperty<string>();
    enabled = createMixedProperty<boolean>();

    private editorStateService = inject(EditorStateService);
    private editorEventService = inject(EditorEventService);
    private destroyRef = inject(DestroyRef);

    ngOnInit(): void {
        this.elements$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(elements => {
            this.elements = elements;
            this.intializeState();
        });
    }

    onEnabledChange(enabled: boolean): void {
        this.enabled.value = enabled;

        // Label will always be mixed as we'll generate unique names for each element
        if (enabled && this.elements.length > 1) {
            this.label.isMixed = true;
        }

        const names: { name: string }[] = [];

        for (const node of this.elements) {
            const element = node.globalElement;
            const dynamicContent = getDynamicPropertyOfElement(element);

            if (enabled && !dynamicContent) {
                const uniqueName = generateUniqueName(node.name, names);
                names.push({ name: uniqueName });

                this.label.value = uniqueName;
                element.properties.push(
                    createElementProperty({
                        unit: 'object',
                        name: 'dynamicContent',
                        label: uniqueName,
                        value: 'dynamicContent' // No value to add atm, just a placeholder as BE validation enforces it
                    })
                );
            } else if (!enabled) {
                element.properties = element.properties.filter(prop => prop.name !== 'dynamicContent');
            }
        }

        const elements = this.editorStateService.creativeDataNode.elements.filter(node =>
            this.elements.some(({ id }) => id === node.id)
        );

        for (const element of elements) {
            this.editorEventService.elements.change(element, {}, ElementChangeType.Force);
        }
    }

    onLabelChange(value: string): void {
        if (!value || value === this.label.value) {
            return;
        }

        this.label.value = value;

        for (const node of this.elements) {
            const element = node.globalElement;
            const dynamicContent = getDynamicPropertyOfElement(element);

            if (dynamicContent) {
                dynamicContent.label = this.label.value;
            }
        }

        const elements = this.editorStateService.creativeDataNode.elements.filter(node =>
            this.elements.some(({ id }) => id === node.id)
        );

        this.editorEventService.creative.change('elements', elements);
    }

    private getDynamicProperty(node: IElementDataNode): IElementProperty | undefined {
        const element = node.globalElement;
        const dynamicContent = getDynamicPropertyOfElement(element);

        return dynamicContent;
    }

    private intializeState(): void {
        if (this.elements.length === 0) {
            return;
        }

        const dynamicProperties = this.elements.map(node => this.getDynamicProperty(node));

        this.label.isMixed = !dynamicProperties.every(
            prop => prop?.label === dynamicProperties[0]?.label
        );
        this.label.value = this.label.isMixed ? undefined : dynamicProperties[0]?.label;

        const firstEnableState = !!this.getDynamicProperty(this.elements[0]);
        this.enabled.value = this.elements.every(e => this.getDynamicProperty(e));
        this.enabled.isMixed = this.elements.some(
            node => !!this.getDynamicProperty(node) !== firstEnableState
        );
    }
}
