import { TestBed } from '@angular/core/testing';
import { IState } from '@domain/state';
import { DataElementFixture } from '@fixtures/element.fixture';
import { provideMock } from '@studio/testing/utils/provide-mock';
import { EditorEventService } from '../services/editor-event/editor-event.service';
import { EditorStateService } from '../services/editor-state.service';
import { PropertiesService } from './properties.service';

const stateFixture: IState = {
    id: '123'
};

describe('PropertiesService', () => {
    let propertiesService: PropertiesService;

    beforeEach(() => {
        TestBed.configureTestingModule({
            declarations: [],
            imports: [],
            providers: [PropertiesService, EditorEventService, provideMock(EditorStateService)]
        }).compileComponents();

        propertiesService = TestBed.inject(PropertiesService);
        // Dummy observer to trigger some side-effects
        propertiesService.observeDataElementOrStateChange().subscribe();
    });

    it('should have selectedElement', () => {
        propertiesService.dataElementChange$.next(DataElementFixture);
        expect(propertiesService.selectedElement).toBe(DataElementFixture);
    });

    it('should not have selectedElement', () => {
        propertiesService.dataElementChange$.next(DataElementFixture);
        propertiesService.dataElementChange$.next(undefined);
        expect(propertiesService.selectedElement).toBe(undefined);
    });

    it('should have stateData', () => {
        propertiesService.selectedStateChange$.next(stateFixture);
        expect(propertiesService.stateData).toBe(stateFixture);
    });

    it('should not have stateData', () => {
        propertiesService.selectedStateChange$.next(stateFixture);
        propertiesService.selectedStateChange$.next(undefined);
        expect(propertiesService.stateData).toBe(undefined);
    });

    it('should set inStateView to true when selectedState has value', () => {
        propertiesService.dataElementChange$.next(DataElementFixture);
        propertiesService.selectedStateChange$.next(stateFixture);
        expect(propertiesService.inStateView).toBe(true);
    });

    it('should set inStateView to false when element is changed', () => {
        const DataElementFixture2 = { ...DataElementFixture };
        DataElementFixture.id = '2';
        propertiesService.dataElementChange$.next(DataElementFixture);
        propertiesService.selectedStateChange$.next(stateFixture);
        propertiesService.dataElementChange$.next(DataElementFixture2);
        expect(propertiesService.inStateView).toBe(false);
    });

    it('should get data-element from observable', done => {
        propertiesService.observeDataElementOrStateChange().subscribe(({ element, state }) => {
            expect(element).toBe(DataElementFixture);
            expect(state).toBe(undefined);
            done();
        });
        propertiesService.dataElementChange$.next(DataElementFixture);
    });

    it('should get state from observable', done => {
        propertiesService.dataElementChange$.next(DataElementFixture);
        let assert = false;
        propertiesService.observeDataElementOrStateChange().subscribe(({ element, state }) => {
            if (assert) {
                expect(element).toBe(DataElementFixture);
                expect(state).toBe(stateFixture);
                done();
            }
        });
        assert = true;
        propertiesService.selectedStateChange$.next(stateFixture);
    });

    it('should get data-element from observable when element is changed', done => {
        let assert = false;
        const DataElementFixture2 = { ...DataElementFixture };
        DataElementFixture2.id = '2';
        propertiesService.observeDataElementOrStateChange().subscribe(({ element, state }) => {
            if (assert) {
                expect(element).toEqual(DataElementFixture2);
                expect(state).toEqual(undefined);
                done();
            }
        });
        propertiesService.dataElementChange$.next(DataElementFixture);
        propertiesService.selectedStateChange$.next(stateFixture);
        assert = true;
        propertiesService.dataElementChange$.next(DataElementFixture2);
    });

    it('should have undefined state when element is set to undefined and not required', done => {
        let assert = false;
        propertiesService.observeDataElementOrStateChange(false).subscribe(({ element, state }) => {
            if (assert) {
                expect(element).toBe(undefined);
                expect(state).toBe(undefined);
                done();
            }
        });
        propertiesService.dataElementChange$.next(DataElementFixture);
        assert = true;
        propertiesService.dataElementChange$.next(undefined);
    });
});
