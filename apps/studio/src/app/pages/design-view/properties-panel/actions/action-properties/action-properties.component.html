<studio-ui-section
    id="add-action-header"
    headline="Actions"
    [clickableHeader]="true"
    (headerClick)="triggerButton($event)"
    [empty]="!actions.length"
    class="no-padding"
    [customAction]="addActionTemplate">
    <div class="actions">
        @for (actionType of sortedActionKeys; track actionType) {
            <div>
                @if (sortedActions[actionType] && sortedActions[actionType]!.length > 0) {
                    <div class="setting">On {{ actionName(actionType) | lowercase }}</div>
                    @for (
                        elementAction of sortedActions[actionType];
                        track elementAction;
                        let i = $index
                    ) {
                        <action-property
                            #actionElement
                            [actionId]="elementAction.id"
                            [actionSettingsOpened]="newActionId === elementAction.id"
                            (editAction)="onEditAction($event)"
                            (actionDeleted)="onDeleteAction($event)"></action-property>
                    }
                }
            </div>
        }
    </div>
</studio-ui-section>
<ui-dropdown
    #dropdown
    id="action-add-dropdown"
    [offset]="placeDropdown()"
    [positions]="[
        { originX: 'start', originY: 'bottom', overlayX: 'end', overlayY: 'top' },
        { originX: 'start', originY: 'top', overlayX: 'end', overlayY: 'bottom' }
    ]"
    type="menu">
    @for (trigger of actionTriggers; track trigger.name) {
        <ui-dropdown-item
            [id]="'interaction-action-' + trigger.value"
            (click)="addAction(trigger)">
            On {{ trigger.name }}
        </ui-dropdown-item>
    }
</ui-dropdown>

<ng-template #addActionTemplate>
    <ng-container>
        <div
            #customDropdown
            class="action"
            [uiDropdownTarget]="dropdown">
            <ui-svg-icon
                [style.width]="addActionIconWidth + 'px'"
                class="icon"
                icon="plus-small">
            </ui-svg-icon>
        </div>
    </ng-container>
</ng-template>
