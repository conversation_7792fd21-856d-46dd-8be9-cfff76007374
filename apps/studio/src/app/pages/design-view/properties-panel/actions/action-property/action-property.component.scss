:host {
    display: block;

    &.open:last-child {
        border-bottom: none;
    }

    &.disabled {
        color: var(--studio-color-text-disabled);

        .action-label .action-name {
            cursor: default;
            color: var(--studio-color-text-disabled);
        }
    }

    .action-row {
        display: grid;
        grid-template-columns: 1fr auto;
        grid-column-gap: 0.7rem;
        align-content: center;

        .icon.is-highlighted,
        .icon.menu-button:hover {
            color: var(--studio-color-text);
        }

        &.collapsed {
            .action-label {
                &.action-state {
                    color: var(--studio-color-text-second);
                }
            }
        }
    }

    .action-label {
        &.action-state {
            display: flex;
            flex-wrap: nowrap;
            max-width: 100%;
            overflow: hidden;
            margin-left: -2px;
        }

        .action-name {
            white-space: nowrap;
            color: var(--studio-color-blue);
            cursor: pointer;
        }

        .direction-right {
            color: var(--studio-color-text-second);
        }

        .state-name {
            color: var(--studio-color-text-second);
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }
    }

    .action-settings {
        margin-bottom: -1rem;

        .setting-row {
            height: 22px;
        }

        .divider {
            margin-bottom: 1rem;
        }
    }

    .setting-edit {
        display: flex;
        justify-content: center;
        color: var(--studio-color-text-second);
        cursor: pointer;

        &:hover {
            color: var(--studio-color-text);
        }
    }

    .property-input {
        min-width: 0;
    }

    ui-svg-icon[icon='question-mark-s'] {
        color: var(--studio-color-grey-84);
        margin-top: -1px;
        margin-left: 5px;
    }
}
