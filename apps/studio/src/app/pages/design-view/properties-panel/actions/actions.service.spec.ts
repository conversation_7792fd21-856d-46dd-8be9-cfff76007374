import { TestBed } from '@angular/core/testing';
import { createAction } from '@creative/actions/actions.utils';
import { OneOfElementDataNodes } from '@domain/nodes';
import { createCreativeDataNodeFixture } from '@fixtures/data-node.fixture';
import { DataElementFixture } from '@fixtures/element.fixture';
import { createRendererFixture } from '@fixtures/renderer.fixture';
import { createMockEditorStateService } from '@mocks/editor.mock';
import { createMutatorServiceMock } from '@mocks/services/mutator-service.mock';
import { CreativesetDataService } from '@studio/common/creativeSet/creativeset.data.service';
import { provideMock } from '@studio/testing/utils/provide-mock';
import { cloneDeep } from '@studio/utils/clone';
import { BehaviorSubject } from 'rxjs';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../../../shared/mocks/store.mock';
import { VersionsService } from '../../../../shared/versions/state/versions.service';
import { EditorStateService } from '../../services/editor-state.service';
import { MutatorService } from '../../services/mutator.service';
import { PropertiesService } from '../properties.service';
import { ActionsService } from './actions.service';

describe('ActionsService', () => {
    let actionsService: ActionsService;

    beforeEach(async () => {
        const dataElementFixture = cloneDeep(DataElementFixture);
        dataElementFixture.actions = [];
        const rendererFixture = createRendererFixture(300, 250, [dataElementFixture]);

        const editorStateServiceMock = createMockEditorStateService({
            creativeDataNode: createCreativeDataNodeFixture(undefined, [dataElementFixture]),
            renderer: rendererFixture
        });

        await TestBed.configureTestingModule({
            declarations: [],
            imports: [],
            providers: [
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER,
                ActionsService,
                CreativesetDataService,
                provideMock(VersionsService),
                provideMock(PropertiesService, {
                    dataElementChange$: new BehaviorSubject<OneOfElementDataNodes | undefined>(
                        dataElementFixture
                    )
                }),
                provideMock(EditorStateService, editorStateServiceMock),
                provideMock(MutatorService, {
                    editorStateService: editorStateServiceMock,
                    ...createMutatorServiceMock({
                        renderer: rendererFixture
                    })
                })
            ]
        }).compileComponents();
        actionsService = TestBed.inject(ActionsService);
    });

    it('should add an action', () => {
        const action = createAction();
        actionsService.addAction(action);
        expect(actionsService.dataElement!.actions.length).toBe(1);
    });

    it('should delete the action', () => {
        const action = createAction();
        actionsService.addAction(action);
        expect(actionsService.dataElement!.actions.length).toBe(1);
        actionsService.deleteAction(actionsService.dataElement!.actions[0]);
        expect(actionsService.dataElement!.actions.length).toBe(0);
    });
});
