import { TestBed } from '@angular/core/testing';
import { UIModule } from '@bannerflow/ui';
import { createAction } from '@creative/actions/actions.utils';
import { createCreativeDataNodeMock } from '@creative/nodes/__tests__/mocks/data-node.mock';
import { ActionOperationMethod, ActionTrigger } from '@domain/action';
import { IRectangleElementDataNode } from '@domain/nodes';
import { DataElementFixtures } from '@fixtures/element.fixture';
import { createMockEditorStateService } from '@mocks/editor.mock';
import { cloneDeep } from '@studio/utils/clone';
import { BehaviorSubject, of } from 'rxjs';
import { VersionsService } from '../../../../../shared/versions/state/versions.service';
import { EditorEventService } from '../../../services/editor-event/editor-event.service';
import { EditorStateService } from '../../../services/editor-state.service';
import { PropertiesService } from '../../properties.service';
import { IMappedAction } from '../action';
import { ActionPropertiesComponent } from '../action-properties/action-properties.component';
import { ActionsService } from '../actions.service';
import { ActionPropertyComponent } from './action-property.component';

jest.useFakeTimers();

describe('ActionPropertyComponent', () => {
    let component: ActionPropertyComponent;
    let editorStateService: EditorStateService;
    let elementFixtures: IRectangleElementDataNode[];
    let elementFixture: IRectangleElementDataNode;

    beforeEach(() => {
        elementFixtures = cloneDeep(DataElementFixtures);
        elementFixture = elementFixtures[0];

        const editorStateServiceMock = createMockEditorStateService({
            creativeDataNode: createCreativeDataNodeMock({
                elements: elementFixtures
            })
        });

        TestBed.configureTestingModule({
            declarations: [ActionPropertyComponent],
            imports: [UIModule],
            providers: [
                {
                    provide: EditorStateService,
                    useValue: editorStateServiceMock
                },
                {
                    provide: PropertiesService,
                    useValue: {
                        dataElementChange$: of(),
                        selectedStateChange$: new BehaviorSubject<void>(void 0),
                        selectedElement: elementFixture
                    }
                },
                {
                    provide: ActionPropertiesComponent,
                    useValue: { actions: [] }
                },
                {
                    provide: ActionsService,
                    useValue: {
                        dataElement: elementFixture,
                        deleteAction: (): void => {
                            elementFixture.actions = [];
                        }
                    }
                },
                VersionsService,
                EditorEventService
            ]
        }).compileComponents();

        const fixture = TestBed.createComponent(ActionPropertyComponent);
        component = fixture.componentInstance;
        component.actionId = 'action-1';
        editorStateService = TestBed.inject(EditorStateService);

        fixture.detectChanges();
        jest.runAllTimers();
    });

    it('should be created', () => {
        expect(component).toBeTruthy();
    });

    it('should have actionData when created', () => {
        expect(component.actionData).toBeTruthy();
    });

    it('should change to a valid action based on the trigger', () => {
        component.selectedTrigger = {
            name: 'Mouse over',
            value: ActionTrigger.MouseEnter
        };
        component.onTriggerChange();
        expect(component.selectedAction).toEqual({
            name: 'Set state',
            value: ActionOperationMethod.SetState
        });
    });

    it('should only have valid defaultActions based on Mouse over trigger', () => {
        component.selectedTrigger = {
            name: 'Mouse over',
            value: ActionTrigger.MouseEnter
        };
        component.onTriggerChange();
        expect(component.selectableActions).toEqual([
            {
                name: 'Set state',
                value: ActionOperationMethod.SetState
            },
            {
                name: 'Remove state',
                value: ActionOperationMethod.RemoveState
            },
            {
                name: 'Clear all states',
                value: ActionOperationMethod.ClearStates
            }
        ] as IMappedAction[]);
    });

    it('should only have valid defaultActions based on Click trigger when switching between it and others', () => {
        component.selectedTrigger = {
            name: 'Mouse over',
            value: ActionTrigger.MouseEnter
        };
        component.onTriggerChange();
        component.selectedTrigger = {
            name: 'Mouse down',
            value: ActionTrigger.MouseDown
        };
        component.onTriggerChange();
        expect(component.selectableActions).toEqual([
            {
                name: 'Set state',
                value: ActionOperationMethod.SetState
            },
            {
                name: 'Remove state',
                value: ActionOperationMethod.RemoveState
            },
            {
                name: 'Clear all states',
                value: ActionOperationMethod.ClearStates
            }
        ] as IMappedAction[]);
    });

    it('should set preventClickThrough to `false` when MouseDown is set as trigger', () => {
        component.selectedTrigger = {
            name: 'Mouse over',
            value: ActionTrigger.MouseEnter
        };
        component.onTriggerChange();
        component.selectedTrigger = {
            name: 'Mouse down',
            value: ActionTrigger.MouseDown
        };
        component.onTriggerChange();
        expect(component.actionData.preventClickThrough).toEqual(false);
    });

    it('should set preventClickThrough to `undefined` when *not* MouseDown is set as trigger', () => {
        component.selectedTrigger = {
            name: 'Mouse down',
            value: ActionTrigger.MouseDown
        };
        component.onTriggerChange();
        component.selectedTrigger = {
            name: 'Mouse over',
            value: ActionTrigger.MouseEnter
        };
        component.onTriggerChange();
        expect(component.actionData.preventClickThrough).toBeUndefined();
    });

    it('should set allowClickThrough to true when changing from OpenUrl method to SetState method on new click actions', () => {
        component.dataElement.actions = [
            createAction({
                triggers: [ActionTrigger.Click],
                method: ActionOperationMethod.OpenUrl,
                target: undefined,
                stateId: undefined
            })
        ];
        component.actionId = editorStateService.creativeDataNode.elements[0].actions[0].id;
        component.ngOnInit();
        component.selectedAction = {
            name: 'Go to URL',
            value: ActionOperationMethod.SetState
        };
        component.onActionMethodChange();
        expect(component.actionData.preventClickThrough).toBeUndefined();
        expect(component.allowClickThrough).toBe(true);
    });

    it('should change selectedState to a state related to the selectedTarget', () => {
        component.selectedTarget = editorStateService.creativeDataNode.elements[1];
        component.onStateTargetChange();
        jest.runAllTimers();
        expect(component.selectedState).toEqual(
            editorStateService.creativeDataNode.elements[1].states[0]
        );
    });

    it('should have the same values after re-initializing', () => {
        component.actionData.operations[0].animation = {
            duration: 1.5,
            timingFunction: 'linear'
        };
        const action = { ...component.actionData };
        component.ngOnInit();
        expect(component.actionData).toEqual(action);
    });

    it('should delete the action', () => {
        component.onDeleteAction();
        expect(elementFixture.actions).toEqual([]);
    });

    describe('click action', () => {
        beforeEach(() => {
            const action = elementFixture.actions[0];

            component.dataElement = {
                ...elementFixture,
                actions: [
                    {
                        ...action,
                        triggers: [ActionTrigger.Click],
                        operations: [
                            {
                                ...action.operations[0],
                                method: ActionOperationMethod.OpenUrl,
                                value: ''
                            }
                        ]
                    }
                ]
            };

            component.selectedTrigger = {
                name: 'Click',
                value: ActionTrigger.Click
            };
        });

        it('should not save invalid url', () => {
            component.onUrlChange('abc');
            expect(component.actionData.operations[0].value).toEqual('');
        });

        it('should save last valid url on destroy', () => {
            component.selectedTrigger = {
                name: 'Click',
                value: ActionTrigger.Click
            };
            component.onTriggerChange();
            component.onUrlChange('https://google.com');
            expect(component.actionData.operations[0].value).toEqual('https://google.com');
            component.onUrlChange('https://google');
            expect(component.urlValidation?.value).toEqual('https://google');
            component.ngOnDestroy();
            expect(component.actionData.operations[0].value).toEqual('https://google.com');
        });
    });
});
