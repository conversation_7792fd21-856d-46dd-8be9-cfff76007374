@if (!guideline) {
    @if (selectedElements$ | async; as selectedElements) {
        @if (selectedElements?.length === 1 && !showKeyframePanel && !preview) {
            <state-tabs></state-tabs>
        }
        @if (!preview) {
            <align-bar
                (align)="alignSelection($event)"
                (distribute)="distributeSelection($event)"></align-bar>
        }
        <!-- No node selected -->
        @if (!selectedElements.length) {
            <div class="settings">
                <creative-properties
                    id="interaction-creative-properties"
                    [animator]="animator">
                </creative-properties>
                <ng-container *permissions="'CalculateWeights'">
                    @if (!preview) {
                        <size-breakdown></size-breakdown>
                    }
                </ng-container>
            </div>
        }
        <!-- Node selected -->
        @if (selectedElements.length) {
            <div
                [id]="
                    'interaction-properties-' +
                    (selectedElements.length > 1
                        ? 'mixed'
                        : selectedElements.length === 1
                          ? selectedElements[0].kind
                          : '')
                "
                class="settings">
                <!-- Layout properties -->
                <layout-properties
                    [isPropertiesHidden]="isPropertiesHidden"
                    [readonly]="preview"
                    [elements$]="selectedElements$"></layout-properties>
                <!-- Dynamic content properties -->
                @if (selectedElements$ | async; as selectedElements) {
                    @if (isDynamicContentPropertiesVisible(selectedElements)) {
                        <dynamic-content-properties
                            [elements$]="selectedElements$"></dynamic-content-properties>
                    }
                }
                <!-- Image -->
                @if (selectedImageElements$ | async; as selectedImageElements) {
                    @if (selectedImageElements.length && !inStateView) {
                        <image-properties [images]="selectedImageElements"></image-properties>
                    }
                }
                <!-- Widget -->
                @if (selectedWidgetElements$ | async; as selectedWidgetElements) {
                    @if (selectedWidgetElements.length === 1 && !inStateView) {
                        <widget-properties
                            [elements$]="selectedWidgetElements$"
                            [preview]="preview"></widget-properties>
                    }
                }
                @if (!isPropertiesHidden) {
                    <!-- Text properies -->
                    @if (selectedTextElements$ | async; as selectedTextElements) {
                        @if (selectedTextElements.length) {
                            <text-properties [elements$]="selectedTextElements$"></text-properties>
                        }
                    }
                    <!-- Video properies -->
                    @if (selectedVideoElements$ | async; as selectedVideoElements) {
                        @if (selectedVideoElements.length && !inStateView) {
                            <video-properties [elements$]="selectedVideoElements$"> </video-properties>
                        }
                    }
                    <!-- Default properties -->
                    @if (selectedElements.length >= 1) {
                        @if (!hideDefaultProperties) {
                            <default-properties [elements$]="selectedElements$"></default-properties>
                        }
                        @if (inReservedStateView) {
                            <reserved-state-settings></reserved-state-settings>
                        }
                        @if (!inStateView) {
                            <!-- In animation -->
                            @if (animator) {
                                <animation-properties
                                    type="in"
                                    [animator]="animator"
                                    [elements$]="selectedElements$"></animation-properties>
                            }
                            <!-- Out animation -->
                            @if (animator) {
                                <animation-properties
                                    type="out"
                                    [animator]="animator"
                                    [elements$]="selectedElements$"></animation-properties>
                            }
                            @if (!preview && !isWidgetElement && selectedElements.length === 1) {
                                <action-properties [element]="selectedElements[0]"></action-properties>
                            }
                        }
                    }
                }
            </div>
        }
        @if (showKeyframePanel) {
            <keyframe-properties></keyframe-properties>
        }
    }
}

<!-- Guideline -->
@if (guideline) {
    <div class="settings">
        <guideline-properties [guideline]="guideline"></guideline-properties>
    </div>
}
