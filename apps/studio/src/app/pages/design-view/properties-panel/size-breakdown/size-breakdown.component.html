<studio-ui-section
    headline="Weight breakdown"
    [collapsable]="true">
    <div
        id="interaction-size-breakdown"
        class="breakdown-wrapper">
        <div
            [class.hidden]="!hasWeights"
            class="circle-wrapper">
            <canvas id="size-breakdown-canvas"></canvas>
            <div class="circle-text">
                <span [uiTooltip]="'* This is an estimate. Final weight may vary'"> Total * </span>
                <span class="totalSize">
                    {{ totalWeight | uiFormatBytes: 0 }}
                </span>
                <span> Initial load: {{ initialLoadWeight | uiFormatBytes: 0 }} </span>
            </div>
        </div>
        @if (hasWeights) {
            <div class="breakdown-container">
                <div>
                    <div class="breakdown-text">
                        <div>
                            <span>Initial load</span>
                            <ui-svg-icon
                                [icon]="showInitialList ? 'collapse' : 'expand'"
                                class="toggleIcon"
                                (click)="showInitialList = !showInitialList">
                            </ui-svg-icon>
                        </div>
                        <div>
                            <span class="dot yellow"></span>
                            <span>{{ initialLoadWeight | uiFormatBytes: 0 }}</span>
                        </div>
                    </div>
                    @if (showInitialList) {
                        <div class="breakdown-list">
                            @for (asset of initialAssets; track asset.url) {
                                <div>
                                    <span [uiTooltip]="asset.name">{{ asset.name }}</span>
                                    <span>{{ asset.weight | uiFormatBytes: 0 }}</span>
                                </div>
                            }
                        </div>
                    }
                </div>
                <div class="breakdown-text">
                    <span>HTML, Script, Widgets</span>
                    <div>
                        <span class="dot red"></span>
                        <span>{{ textWeight | uiFormatBytes: 0 }}</span>
                    </div>
                </div>
                <div>
                    <div class="breakdown-text">
                        <div>
                            <span>Images</span>
                            @if (showImageToggle) {
                                <ui-svg-icon
                                    [icon]="showImageList ? 'collapse' : 'expand'"
                                    class="toggleIcon"
                                    (click)="showImageList = !showImageList">
                                </ui-svg-icon>
                            }
                        </div>
                        <div>
                            <span class="dot green"></span>
                            <span>{{ imageWeight | uiFormatBytes: 0 }}</span>
                        </div>
                    </div>
                    @if (showImageList) {
                        <div class="breakdown-list">
                            @for (asset of imageAssets; track asset.url) {
                                <div>
                                    <span [uiTooltip]="asset.name">{{ asset.name }}</span>
                                    <span>{{ asset.weight | uiFormatBytes: 0 }}</span>
                                </div>
                            }
                        </div>
                    }
                </div>
                <div>
                    <div class="breakdown-text">
                        <div>
                            <span>Fonts</span>
                            @if (showFontToggle) {
                                <ui-svg-icon
                                    [icon]="showFontList ? 'collapse' : 'expand'"
                                    class="toggleIcon"
                                    (click)="showFontList = !showFontList">
                                </ui-svg-icon>
                            }
                        </div>
                        <div>
                            <span class="dot blue"></span>
                            <span>{{ fontWeight | uiFormatBytes: 0 }}</span>
                        </div>
                    </div>
                    @if (showFontList) {
                        <div class="breakdown-list">
                            @for (asset of fontAssets; track asset.url) {
                                <div>
                                    <span [uiTooltip]="asset.name">{{ asset.name }}</span>
                                    <span>{{ asset.weight | uiFormatBytes: 0 }}</span>
                                </div>
                            }
                        </div>
                    }
                </div>
                @if (showVideo) {
                    <div>
                        <div class="breakdown-text">
                            <div>
                                <span>Video</span>
                                @if (showVideoToggle) {
                                    <ui-svg-icon
                                        [icon]="showVideoList ? 'collapse' : 'expand'"
                                        class="toggleIcon"
                                        (click)="showVideoList = !showVideoList">
                                    </ui-svg-icon>
                                }
                            </div>
                            <div>
                                <ui-svg-icon
                                    icon="question-mark"
                                    [uiTooltipPosition]="'bottom'"
                                    [uiTooltipHideArrow]="true"
                                    [uiTooltipWidth]="290"
                                    [uiTooltip]="
                                        'Videos are streamed to the end-users to ensure fast and efficient delivery. This weight indication shows the non-streamed, total file-size of the video.'
                                    "></ui-svg-icon>
                                <span class="dot purple"></span>
                                <span>{{ videoWeight | uiFormatBytes: 0 }}</span>
                            </div>
                        </div>
                        @if (showVideoList) {
                            <div class="breakdown-list">
                                @for (asset of videoAssets; track asset.url) {
                                    <div>
                                        <span [uiTooltip]="asset.name">{{ asset.name }}</span>
                                        <span>{{ asset.weight | uiFormatBytes: 0 }}</span>
                                    </div>
                                }
                            </div>
                        }
                    </div>
                }
                @if (url && isEmployee) {
                    <div>
                        <ui-button
                            (click)="previewClick()"
                            text="Preview"></ui-button>
                    </div>
                }
            </div>
        }
        <div>
            <div>
                @if (showCalcButton) {
                    <ui-button
                        type="discrete"
                        [text]="calcButtonDisplayText"
                        [icon]="buttonIcon"
                        id="interaction-calc-weight-btn"
                        (click)="calculateButtonClick()"
                        [loading]="loading">
                    </ui-button>
                }
            </div>
            @if (showInfoSaveText) {
                <div>
                    @if (showUpdateText) {
                        <div class="saveText">
                            Save your creative to be able to update weight calculation
                        </div>
                    }
                    @if (showSaveText) {
                        <div class="saveText">Save your creative to enable weight calculation</div>
                    }
                </div>
            }
        </div>
    </div>
</studio-ui-section>
