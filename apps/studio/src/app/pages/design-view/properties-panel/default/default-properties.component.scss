:host ::ng-deep .setting-value {
    .ui-select {
        & ::ng-deep {
            .button {
                min-width: 77px;
                max-width: 77px;

                .text {
                    .text-wrapper {
                        overflow: unset;
                    }
                }
            }
        }
    }
}

.setting-value {
    &.col-4 {
        &.shadow {
            grid-gap: unset;
            grid-template-columns: 32px 31px 31px 32px;
            justify-items: center;

            &.hide-spread {
                grid-template-columns: 42px 41px 42px 0px;
            }
        }

        &.shadow-container {
            grid-gap: unset;
        }
    }
}

.add-filter {
    display: grid;
    align-items: center;

    .icon {
        text-align: right;
        color: var(--studio-color-text-second);
    }
}

.placeholder-text {
    color: var(--studio-color-text-second);
}
