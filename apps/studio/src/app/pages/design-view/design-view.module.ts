import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { ThreadsDirective } from '@bannerflow/comments';
import { NSNotificationsComponent } from '@bannerflow/notification';
import { UiCardDirective, UIPillComponent, UIPREnvPickerComponent } from '@bannerflow/ui';
import { DevtoolsComponent } from '../../core/devtools/devtools.component';
import { AiImageGenerationPopoverComponent } from '../../shared/ai-studio/ai-image-generation-popover/ai-image-generation-popover.component';
import { CreativePreviewComponent } from '../../shared/components/creative-preview/creative-preview.component';
import { HeavyVideoWarningDialogComponent } from '../../shared/components/heavy-video-warning-dialog/heavy-video-warning-dialog.component';
import { HelpMenuComponent } from '../../shared/components/help-menu/help-menu.component';
import { TruncateSpanComponent } from '../../shared/directives/truncate-span.directive';
import { SharedModule } from '../../shared/shared.module';
import { AssetPickerDropdownComponent } from './asset-picker/asset-picker-dropdown/asset-picker-dropdown.component';
import { AssetPickerComponent } from './asset-picker/asset-picker.component';
import { ColorPickerPipe } from './color-section/color-picker.pipe';
import { ColorSectionComponent } from './color-section/color-section.component';
import { DesignViewComponent } from './design-view.component';
import { DraggableContainerDirective } from './draggable-container.directive';
import {
    EditElementComponent,
    EditElementPreviewComponent,
    EditElementTopbarComponent
} from './edit-element';
import { DropdownMenuComponent, EditorTopbarComponent } from './editor-topbar';
import { CreativePreviewAccessComponent } from './editor-topbar/creative-preview-access/creative-preview-access.component';
import { DraggableElementComponent } from './media-library/draggable-element/draggable-element.component';
import { FeedFieldComponent } from './media-library/feed-field/feed-field.component';
import { LibraryElementComponent } from './media-library/library-element.component';
import { LibraryFolderComponent } from './media-library/library-folder.component';
import { MediaLibraryComponent } from './media-library/media-library.component';
import { UpdateWidgetDialogComponent } from './media-library/update-widget-dialog/update-widget-dialog.component';
import { IsElementsPipe } from './pipes/is-element.pipe';
import { IsGroupPipe } from './pipes/is-group.pipe';
import { ActionPropertiesComponent } from './properties-panel/actions/action-properties/action-properties.component';
import { ActionPropertyComponent } from './properties-panel/actions/action-property/action-property.component';
import { AlignBarComponent } from './properties-panel/align-bar.component';
import { AnimationPropertiesComponent } from './properties-panel/animation/animation-properties.component';
import { AssetPropertyComponent } from './properties-panel/asset-property/asset-property.component';
import { CreativePropertiesComponent } from './properties-panel/creative/creative-properties.component';
import { DefaultPropertiesComponent } from './properties-panel/default/default-properties.component';
import { DynamicContentPropertiesComponent } from './properties-panel/dynamic-content/dynamic-content-properties.component';
import { GuidelinePropertiesComponent } from './properties-panel/guideline/guideline-properties.component';
import { ImageOptimizationComponent } from './properties-panel/image/image-optimization/image-optimization.component';
import { ImagePropertiesComponent } from './properties-panel/image/image-properties.component';
import { SizeModeOptionsComponent } from './properties-panel/image/size-mode/size-mode-options.component';
import { KeyframePropertiesComponent } from './properties-panel/keyframe/keyframe-properties.component';
import { LayoutPropertiesComponent } from './properties-panel/layout/layout-properties.component';
import { PropertiesPanelComponent } from './properties-panel/properties-panel.component';
import { QualityOptionsComponent } from './properties-panel/quality-options/quality-options.component';
import { SizeBreakdownComponent } from './properties-panel/size-breakdown/size-breakdown.component';
import { ReservedStateSettingsComponent } from './properties-panel/state-tabs/reserved-state-settings.component';
import { StateTabsComponent } from './properties-panel/state-tabs/state-tabs.component';
import { TextPropertiesComponent } from './properties-panel/text/text-properties.component';
import { VideoPropertiesComponent } from './properties-panel/video/video-properties.component';
import { WidgetCustomPropertyComponent } from './properties-panel/widget/widget-custom-property/widget-custom-property.component';
import { WidgetPropertiesComponent } from './properties-panel/widget/widget-properties.component';
import {
    GifFramesComponent,
    NewStudioTimelineComponent,
    StudioTimelineComponent,
    TimelineAnimationComponent,
    TimelineElementComponent,
    TimeRulerComponent
} from './timeline';
import { MaskingSvgsComponent } from './timeline/studio-timeline/masking-svgs.component';
import { StudioToolbarComponent } from './toolbar';
import { WidgetEditorComponent, WidgetThumbnailUploadComponent } from './widget-editor';
import { WorkspaceModule } from './workspace/workspace.module';
import { ZoomControlModule } from './workspace/zoom-control/zoom-control.module';

const routes: Routes = [
    {
        path: '',
        component: DesignViewComponent
    }
];

@NgModule({
    imports: [
        AssetPickerDropdownComponent,
        ThreadsDirective,
        CommonModule,
        CreativePreviewAccessComponent,
        DevtoolsComponent,
        FormsModule,
        HelpMenuComponent,
        NSNotificationsComponent,
        ReactiveFormsModule,
        RouterModule.forChild(routes),
        SharedModule,
        TruncateSpanComponent,
        WorkspaceModule,
        ZoomControlModule,
        AiImageGenerationPopoverComponent,
        ColorPickerPipe,
        CreativePreviewComponent,
        UiCardDirective,
        UIPillComponent,
        UIPREnvPickerComponent
    ],
    exports: [RouterModule],
    declarations: [
        ActionPropertiesComponent,
        ActionPropertyComponent,
        AlignBarComponent,
        AnimationPropertiesComponent,
        AssetPickerComponent,
        AssetPropertyComponent,
        CreativePropertiesComponent,
        DefaultPropertiesComponent,
        DesignViewComponent,
        DraggableContainerDirective,
        DraggableElementComponent,
        DropdownMenuComponent,
        DynamicContentPropertiesComponent,
        EditElementComponent,
        EditElementPreviewComponent,
        EditElementTopbarComponent,
        EditorTopbarComponent,
        FeedFieldComponent,
        SizeModeOptionsComponent,
        GifFramesComponent,
        GuidelinePropertiesComponent,
        HeavyVideoWarningDialogComponent,
        ImageOptimizationComponent,
        ImagePropertiesComponent,
        IsElementsPipe,
        IsGroupPipe,
        KeyframePropertiesComponent,
        LayoutPropertiesComponent,
        LibraryElementComponent,
        LibraryFolderComponent,
        MaskingSvgsComponent,
        MediaLibraryComponent,
        PropertiesPanelComponent,
        QualityOptionsComponent,
        ReservedStateSettingsComponent,
        SizeBreakdownComponent,
        StateTabsComponent,
        StudioTimelineComponent,
        NewStudioTimelineComponent,
        StudioToolbarComponent,
        TextPropertiesComponent,
        TimeRulerComponent,
        TimelineAnimationComponent,
        TimelineElementComponent,
        UpdateWidgetDialogComponent,
        VideoPropertiesComponent,
        WidgetEditorComponent,
        WidgetPropertiesComponent,
        WidgetCustomPropertyComponent,
        WidgetThumbnailUploadComponent,
        ColorSectionComponent
    ]
})
export class DesignViewModule {}
