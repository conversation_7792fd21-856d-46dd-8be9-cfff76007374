$toolbarWidth: 5rem;
$toolbarItemHeight: 4.5rem;

:where(:root:not([data-uinew])) :host {
    z-index: 2;

    .toolbar {
        display: flex;
        box-shadow: 1px 0 0 0 var(--studio-color-border-second);
        background-color: var(--studio-color-white-off-light);
        height: 100%;
        width: $toolbarWidth;
        flex-direction: column;
        justify-content: flex-start;
        align-content: flex-start;
        border-top: 1px solid var(--studio-color-border-second);

        .hidden {
            display: none;
        }
    }

    .toolbar-item {
        cursor: pointer;
        color: var(--studio-color-grey-dark);
        height: $toolbarItemHeight;
        margin: -2px 0;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
            color: var(--studio-color-black);
            background: var(--studio-color-grey-93);

            .tooltip {
                opacity: 1;
                transition: opacity 0.1s ease 0.25s;
            }
        }

        [icon='arrow-right'] {
            transition: all 0.25s;
        }

        &.active {
            color: var(--studio-color-black);
            background: var(--studio-color-grey-93);

            [icon='arrow-right'] {
                transform: rotateZ(180deg);
            }
        }

        .tooltip {
            transition: opacity 0.2s ease;
            pointer-events: none;
            opacity: 0;
            background: var(--studio-color-text-discrete);
            color: var(--studio-color-background-second);
            position: absolute;
            left: 58px;
            top: 50%;
            padding: 3px 8px;
            transform: translateY(-50%);
            width: auto;
            display: block;
            white-space: nowrap;
            border-radius: 2px;
        }

        .button {
            display: flex;
            flex: 1;
            border-radius: 0.2rem;
            text-align: center;
            font-size: 14px;
            background-color: transparent;
            transition:
                background-color 0.2s ease,
                color 0.2s ease;
            align-items: center;
            justify-content: center;
            float: left;

            &.zoom {
                position: absolute;
                bottom: 50%;
                left: 0;
                line-height: 4rem;
                width: 4rem;
                display: inline-block;
                min-height: 5rem;

                .text {
                    font-size: 0.9rem;
                    color: var(--studio-color-black);
                    position: absolute;
                    bottom: -1rem;
                    width: 100%;
                }
            }

            &.library {
                position: absolute;
                bottom: -4.5rem;
                transform: rotate(-90deg);
                transform-origin: left top 0;
                text-transform: uppercase;
                margin: 0.5rem;
                left: 0;
                line-height: 4rem;
                padding: 0 1rem;
                font-size: 1.4rem;
                display: inline-block;
                font-weight: var(--default-font-weight-bold);
                cursor: pointer;
                width: 12rem;

                i {
                    margin-right: 0.5rem;
                }
            }
        }
    }

    .divider {
        width: 100%;
        height: 1px;
        margin: 2px 0;
        background: var(--studio-color-border-second);
        pointer-events: none;
    }
}
