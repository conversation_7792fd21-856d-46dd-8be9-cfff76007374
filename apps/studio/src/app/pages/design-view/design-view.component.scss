$barSize: 50px;
$libraryWidth: 200px;
$propertiesWidth: 220px;

:where(:root:not([data-uinew])) :host {
    width: 100%;
    height: 100%;
    position: fixed;

    & > ui-header {
        z-index: 3;
        padding: 0;
        box-shadow: 0 1px 0px 0 var(--studio-color-border-second);

        ::ng-deep {
            .content {
                max-width: none;
                margin: 0;
            }
        }
    }

    &:before {
        content: '';
        background: var(--studio-color-background-second);
        width: 100%;
        height: $barSize;
        position: absolute;
        top: 0;
        left: 0;
    }

    .editor .bottom.library-open {
        left: $libraryWidth + $barSize + 1px;
    }
    ui-body {
        &.show-overflow {
            .left {
                z-index: 2;
            }
        }
    }
    ui-loader {
        z-index: 103;
    }

    .editor {
        display: flex;
        flex-direction: row;
        height: 100%;
        user-select: none;

        .left {
            z-index: 1;
            display: flex;
            flex-direction: row;
            position: relative;

            &:hover {
                z-index: 102;
            }
        }

        .bottom {
            background: var(--studio-color-background-second);
            position: absolute;
            left: 0;
            right: $propertiesWidth;
            bottom: 0;
            z-index: 103;
        }

        .right {
            position: absolute;
            right: 0;
            top: 0;
            background-color: var(--studio-color-surface);
            border-left: 1px solid var(--studio-color-border-second);
            width: 22rem;
            z-index: 2;
            flex-shrink: 0;
            height: calc(100%);
        }

        .element-edit {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: 103;
        }
    }

    .body {
        position: absolute;
        top: 0;
        bottom: 0;
        left: $barSize;
        right: 22rem;
        z-index: 1;
    }

    .hide {
        visibility: hidden;
        opacity: 0;
    }

    ::ng-deep .delete-element-dialog-table {
        td {
            text-overflow: ellipsis;
            overflow: hidden;
            max-width: 120px;
            white-space: nowrap;
            padding-right: 10px;
        }
    }

    ::ng-deep .ui-notification.warning {
        padding-right: 9px !important;
    }
}
