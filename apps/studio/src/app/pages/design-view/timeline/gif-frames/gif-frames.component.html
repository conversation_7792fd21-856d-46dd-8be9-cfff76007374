<div class="left-panel">
    @if (timeline.isNewUI) {
        <ui-label
            leadingIcon="gif_box"
            class="placeholder-elements"
            size="sm">
            GIF frames
        </ui-label>
        <ui-button
            id="interaction-add-gif-frame"
            size="sm"
            nuiType="plain-secondary"
            nuiSvgIcon="add"
            [disabled]="!canAddFrame"
            (click)="addFrame()" />
    } @else {
        <div class="gif-icon">
            <ui-svg-icon
                icon="gif-frame"
                nuiIcon="gif_box"
                size="sm"></ui-svg-icon>
        </div>
        <div class="name">GIF frames</div>

        <div
            #addIcon
            id="interaction-add-gif-frame"
            class="add-icon"
            (click)="addFrame()">
            <ui-svg-icon
                icon="plus-small"
                size="sm"></ui-svg-icon>
        </div>
    }
</div>
<div
    class="right-canvas"
    [ngClass]="{ scrolled: scrollService.scroll.x }">
    <div
        id="interaction-gif-frames"
        class="frames">
        @for (frame of frames; track $index) {
            <div
                class="frame"
                [ngClass]="{
                    selected: frame === selectedFrame || frame === openFrame,
                    disabled: frame.time > document.duration
                }"
                (mousedown)="onMouseDown(frame)"
                (click)="openFramePopover(frameElement, frame)"
                ui-popover-target
                #frameElement="ui-popover-target"
                style.transform="translateX({{ getXFromTime(frame) }}px)">
                <ui-svg-icon
                    size="sm"
                    icon="gif-frame-small"
                    [nuiIconFilled]="true"
                    nuiIcon="label"></ui-svg-icon>
            </div>
        }
    </div>
    <time-ruler
        [showNumbers]="false"
        [height]="timeline.timelineElementHeight"></time-ruler>
</div>

<ui-popover
    (close)="onFramePopoverClose()"
    #framePopover="ui-popover"
    [config]="{
        arrowPosition: 'bottom',
        position: 'top',
        theme: 'tiny',
        panelClass: 'no-padding',
        width: 130
    }">
    <ng-template ui-popover-template>
        @if (openFrame) {
            <div class="frame-popover">
                <div class="row">
                    <div class="text">Time</div>
                    <ui-number-input
                        [id]="'popover-time'"
                        [value]="openFrame.time"
                        (valueChange)="moveFrame(openFrame, $event)"
                        [step]="0.1"
                        [min]="0"
                        [max]="document.duration"
                        unitLabel="s">
                    </ui-number-input>
                </div>
                <div class="row">
                    <div class="text">Duration</div>
                    <ui-number-input
                        [id]="'popover-duration'"
                        [value]="openFrame.duration"
                        (valueChange)="setFrameDuration(openFrame, $event)"
                        [allowEmpty]="true"
                        placeholder="Auto"
                        [min]="0"
                        [step]="0.1"
                        [unitLabel]="openFrame.duration !== undefined ? 's' : undefined">
                    </ui-number-input>
                </div>
                <div class="row icon">
                    <ui-svg-icon
                        (click)="deleteFrame(openFrame)"
                        icon="delete">
                    </ui-svg-icon>
                </div>
            </div>
        }
    </ng-template>
</ui-popover>
