:host {
    display: grid;
    width: 100%;
    height: var(--timeline-row-height);
    grid-template-columns: var(--left-panel-width) auto;
    position: relative;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid var(--border-color);
}

:where(:root:not([data-uinew])) :host .left-panel {
    display: grid;
    grid-template-columns: var(--icon-width) minmax(0, 1fr) var(--kebab-menu-width);
    height: 100%;
    position: relative;
    padding: 0 var(--left-panel-padding);
}

:where(:root[data-uinew]) :host .left-panel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--left-panel-padding);
}

.right-canvas {
    z-index: 1;
    position: relative;
    width: 100%;
    height: var(--timeline-row-height);
    overflow: visible;

    &.scrolled {
        overflow: hidden;
    }
}

time-ruler {
    pointer-events: none;
}

.gif-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--studio-color-grey-61);
    padding-left: var(--name-input-padding);
}

.name {
    display: flex;
    align-items: center;
    padding-left: var(--name-input-padding);
    color: var(--studio-color-grey-61);
}

.add-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    opacity: 1;
    transition:
        color 0.2s ease,
        opacity 0.2s ease;
    color: var(--icon-color);
    overflow: hidden;

    &.disabled {
        opacity: 0.3;
        pointer-events: none;
    }

    &.hidden {
        opacity: 0 !important;
        pointer-events: none;
    }
}

.frames {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    z-index: 1;
}

:where(:root:not([data-uinew])) :host .frames .frame {
    position: absolute;
    top: calc(50% + -5px);
    left: -5px;
    width: 10px;
    height: 10px;
    text-align: center;
    line-height: 10px;
    transition: color 0.2s ease;
    color: var(--studio-color-grey-61);

    ui-svg-icon {
        font-size: 9px;
    }

    &.selected,
    &:hover {
        color: var(--studio-color-hover);
    }

    &.disabled {
        opacity: 0.5;
    }
}

:where(:root[data-uinew]) :host .frames .frame {
    position: absolute;
    left: -5px;
    width: 10px;
    height: 100%;
    text-align: center;
    line-height: 10px;
    transition: color 0.2s ease;
    color: var(--studio-color-grey-61);
    display: flex;
    justify-content: center;
    align-items: center;
    top: -5px;

    ui-svg-icon {
        width: 13px;
        transform: rotate(90deg);
        --color: var(--nui-icon-secondary);
    }

    &.selected,
    &:hover {
        color: var(--studio-color-hover);
    }

    &.disabled {
        opacity: 0.5;
    }
}

.frame-popover {
    padding: 7px;

    .row {
        display: grid;
        grid-template-columns: minmax(50px, 70px) 50px;
        grid-gap: 8px;
        width: 100%;
        margin-bottom: 10px;

        .text {
            display: flex;
            align-items: center;
            color: var(--studio-color-grey-61);
            user-select: none;

            .help-icon {
                padding-left: 2px;
                opacity: 0.5;
            }

            &:hover {
                .help-icon {
                    opacity: 1;
                }
            }
        }

        &.icon {
            display: block;
            text-align: center;
            margin-bottom: 0;
            color: var(--studio-color-grey-61);
            transition: color 0.2s ease;
            cursor: pointer;

            &:hover {
                color: var(--default-alert-color);
            }
        }
    }
}
