import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    inject,
    input,
    Input,
    On<PERSON><PERSON>roy
} from '@angular/core';
import { fromWidthResize } from '@studio/utils/resize-observable';
import { isMultipleOf } from '@studio/utils/utils';
import { BehaviorSubject, merge, Subject } from 'rxjs';
import { takeUntil, tap } from 'rxjs/operators';
import { ICanvasStyle, OneOfCanvasShapes } from '../../../../shared/components';
import { changeFilter, EditorEventService } from '../../services/editor-event';
import { HistoryService } from '../../services/history.service';
import {
    NewStudioTimelineComponent,
    StudioTimelineComponent
} from '../studio-timeline/studio-timeline.component';
import { TimelineScrollService } from '../timeline-scroll.service';
import { TimelineZoomService } from '../timeline-zoom.service';
import {
    DOT_DEFAULTS,
    HALF_SECOND_TEXT_DEFAULTS,
    NUI_DOT_DEFAULTS,
    NUI_HALF_SECOND_TEXT_DEFAULTS,
    NUI_SECOND_DOT_DEFAULTS,
    NUI_SECOND_TEXT_DEFAULTS,
    SECOND_DOT_DEFAULTS,
    SECOND_TEXT_DEFAULTS,
    NUI_ZOOM_CONTROL_WIDTH
} from './time-ruler.constants';
import { resolveCSSVariable } from '@studio/common/css/css-variables';

@Component({
    selector: 'time-ruler',
    templateUrl: './time-ruler.component.html',
    styleUrls: ['./time-ruler.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class TimeRulerComponent implements AfterViewInit, OnDestroy {
    private timeline =
        inject(NewStudioTimelineComponent, { optional: true }) || inject(StudioTimelineComponent);
    @Input() showNumbers = true;
    height = input.required<number>();

    shapes$ = new BehaviorSubject<OneOfCanvasShapes[]>([]);
    style: ICanvasStyle = {
        fontSize: 9,
        textAlign: 'center'
    };

    private unsubscribe$ = new Subject<void>();
    private width = 4000;

    constructor(
        private host: ElementRef,
        private scrollService: TimelineScrollService,
        private zoomService: TimelineZoomService,
        private historyService: HistoryService,
        private editorEvent: EditorEventService
    ) {}

    ngAfterViewInit(): void {
        merge(
            this.scrollService.scroll$,
            this.zoomService.zoom$,
            this.editorEvent.elements.immediateChange$.pipe(
                changeFilter({
                    explicitProperties: ['duration', 'time']
                })
            ),
            fromWidthResize(this.host.nativeElement).pipe(tap(({ width }) => (this.width = width))),
            this.historyService.snapshotApply$
        )
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe(this.updateShapes);

        this.updateShapes();
    }

    private updateShapes = (): void => {
        const shapes: OneOfCanvasShapes[] = [];
        const duration = this.timeline.duration;
        const width = Math.max(this.width, this.zoomService.secondsToPixels(duration + 1));
        const widthInSeconds = this.zoomService.pixelsToSeconds(width);
        const height = this.height();
        const durationInPixels = this.timeline.secondsToScrolledPixels(this.timeline.duration);
        const timeOffset = 1 / this.getDotsPerSecond();

        // Gray area after duration
        shapes.push({
            fill: this.timeline.isNewUI
                ? resolveCSSVariable('--nui-border-neutral-subtle')
                : 'rgba(235, 235, 235, 0.4)',
            x: durationInPixels - 1,
            y: 0,
            width: width - durationInPixels + 1,
            height
        });

        for (let t = timeOffset; t < widthInSeconds; t += timeOffset) {
            const shape = this.getShapeAtTime(t);
            if (!shape) {
                continue;
            }
            shapes.push(shape);
        }

        this.shapes$.next(shapes);
    };

    private getShapeAtTime(time: number): OneOfCanvasShapes | undefined {
        const x = this.timeline.secondsToScrolledPixels(time);
        const height = this.height();
        const y = Math.floor(height / 2);
        // removing the shapes under the collapse toggle button
        if (
            this.timeline.isNewUI &&
            x >
                this.timeline.host.nativeElement.clientWidth -
                    this.timeline.leftPanelWidth -
                    NUI_ZOOM_CONTROL_WIDTH
        ) {
            return;
        }
        if (this.showNumbers) {
            if (isMultipleOf(time, 1)) {
                return {
                    ...(this.timeline.isNewUI ? NUI_SECOND_TEXT_DEFAULTS : SECOND_TEXT_DEFAULTS),
                    text: this.timeline.isNewUI ? `${Math.round(time)}s` : `${Math.round(time)} s`,
                    x,
                    height
                };
            }
            // Half seconds
            else if (isMultipleOf(time, 0.5) && this.shouldShowHalfSeconds()) {
                return {
                    ...(this.timeline.isNewUI
                        ? NUI_HALF_SECOND_TEXT_DEFAULTS
                        : HALF_SECOND_TEXT_DEFAULTS),
                    text: this.timeline.isNewUI ? `${time.toFixed(1)}s` : `${time.toFixed(1)} s`,
                    x,
                    height
                };
            }
        }
        // Half and whole seconds
        if (isMultipleOf(time, 0.5)) {
            return {
                ...(this.timeline.isNewUI ? NUI_SECOND_DOT_DEFAULTS : SECOND_DOT_DEFAULTS),
                x,
                y
            };
        }

        return {
            ...(this.timeline.isNewUI ? NUI_DOT_DEFAULTS : DOT_DEFAULTS),
            x,
            y
        };
    }

    private shouldShowHalfSeconds(): boolean {
        return this.timeline.secondsToPixels(1) > 150;
    }

    private getDotsPerSecond(): number {
        const secondInPixels = this.timeline.secondsToPixels(1);

        if (secondInPixels > 120) {
            return 10;
        } else if (secondInPixels > 100) {
            return 5;
        } else {
            return 2;
        }
    }

    ngOnDestroy(): void {
        this.unsubscribe$.next();
        this.unsubscribe$.complete();
    }
}
