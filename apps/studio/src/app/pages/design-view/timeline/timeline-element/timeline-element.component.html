<div class="summary">
    <div
        class="left-panel"
        [ngClass]="{
            hidden: inHiddenNodeTree,
            'keyframes-enabled': elementFeatures.has(Feature.Animations)
        }"
        (click)="setSelections($event)">
        <div
            data-test-id="element-toggle-visibility"
            class="icon eye"
            (click)="toggleVisibility($event)">
            <ui-svg-icon
                [icon]="node.hidden ? 'visibility-hidden' : 'visibility-visible'"
                size="sm"
                [nuiIcon]="node.hidden ? 'visibility_off' : 'visibility'"></ui-svg-icon>
        </div>
        <div
            class="name"
            [attr.data-test-id]="'te-' + node.name"
            [ngClass]="'indent-level-' + indentLevel"
            #elementName>
            @if (node | isGroupNode) {
                <div
                    id="toggle-group"
                    data-test-id="toggle-group"
                    class="icon toggle-group"
                    [ngClass]="{ active: !collapsedGroup }"
                    (click)="toggleGroup($event)">
                    <ui-svg-icon
                        [icon]="collapsedGroup ? 'folder' : 'folder-open'"
                        size="sm"
                        [nuiIcon]="collapsedGroup ? 'folder' : 'folder_open'"></ui-svg-icon>
                </div>
            }
            @if (isMask) {
                <div class="icon masking">
                    <svg
                        class="masking-path start"
                        width="14"
                        [attr.height]="timeline.timelineElementDurationBoxHeight"
                        [attr.viewBox]="`0 0 ${timeline.timelineElementDurationBoxHeight} ${timeline.timelineElementDurationBoxHeight}`"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <use href="#masking-path-start"></use>
                    </svg>
                    @if (!timeline.isNewUI) {
                        <ui-svg-icon icon="mask"></ui-svg-icon>
                    }
                </div>
                <ui-svg-icon
                    class="masking-icon"
                    icon="none"
                    nuiIcon="square_dot"
                    size="sm"></ui-svg-icon>
            }

            @if (isMasked) {
                <div class="icon masking">
                    <svg
                        class="masking-path line"
                        width="1"
                        [attr.height]="timeline.timelineElementHeight"
                        [attr.viewBox]="`0 0 1 ${timeline.timelineElementHeight}`"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <use href="#masking-path-line"></use>
                    </svg>
                </div>
            }

            @if (isMaskedLast) {
                <div class="icon masking">
                    <svg
                        class="masking-path end"
                        width="14"
                        height="29"
                        viewBox="0 0 14 29"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <use href="#masking-path-end"></use>
                    </svg>
                </div>
            }
            <ui-input
                #name
                ui-theme="tiny"
                kind="secondary"
                [id]="'name-' + elementIndex"
                size="xs"
                [discrete]="true"
                [dynamicWidth]="true"
                [value]="node.name"
                (cancel)="cancelSetName()"
                (mousedown)="startEditName($event)"
                (blur)="setName(name.value)"
                (dynamicWidthUpdated)="onDynamicWidthUpdate()"
                (submit)="submitName()"></ui-input>
            @if (elementFeatures.size > 0) {
                <div class="element-features">
                    @if (elementFeatures.has(Feature.Feeds)) {
                        <div class="icon feature">
                            <ui-svg-icon
                                icon="feed"
                                nuiIcon="rss_feed"
                                size="sm"></ui-svg-icon>
                        </div>
                    }
                    @if (elementFeatures.has(Feature.Actions)) {
                        <div class="icon feature">
                            <ui-svg-icon
                                icon="de-action"
                                nuiIcon="bolt"
                                size="sm"></ui-svg-icon>
                        </div>
                    }
                </div>
            }
        </div>
        @if (node | isElementNode; as element) {
            <div
                id="toggle-animations-btn"
                data-test-id="toggle-animations-btn"
                class="icon toggle-animations"
                [ngClass]="{ active: expanded, hidden: element.locked || !element.animations.length }"
                (click)="toggleAnimations($event)">
                <ui-svg-icon
                    icon="animation"
                    nuiIcon="timelapse"
                    size="sm"></ui-svg-icon>
            </div>
        }
        @if (node.locked) {
            <div
                class="icon lock"
                (click)="toggleLocked()">
                <ui-svg-icon
                    icon="lock-closed"
                    nuiIcon="lock"
                    size="sm"></ui-svg-icon>
            </div>
        }
        @if (!node.locked) {
            <div
                class="icon menu"
                [ngClass]="{ active: selected && timeline.editor.workspace.contextMenuOpen }"
                (mousedown)="openContextMenu($event, true)">
                <ui-svg-icon
                    icon="kebab"
                    nuiIcon="more_vert"
                    size="sm"></ui-svg-icon>
            </div>
        }
    </div>
    <div class="right-canvas">
        <div class="canvas-wrapper">
            <canvas
                #elementCanvas
                class="element-canvas"></canvas>
        </div>
    </div>
</div>

@if (node | isElementNode; as element) {
    <div class="animations-expanded">
        <section-expand
            class="animations"
            #animationSection
            [scrollIntoView]="false"
            [expanded]="expanded"
            arrowPosition="calc(var(--left-panel-width) - 33px)"
            [arrowSize]="6"
            [showBackground]="true"
            [showShadow]="false"
            (animationStateChanged)="sectionStateChange($event)">
            @for (animation of element.animations; track getAnimationId($index, animation)) {
                <timeline-animation
                    [class.first]="$first"
                    [class.last]="$last"
                    #animation
                    [animation]="animation"
                    [element]="element"
                    [indentLevel]="indentLevel">
                </timeline-animation>
            }
        </section-expand>
    </div>
}
