:where(:root:not([data-uinew])) :host {
    opacity: 0;
    transition: 0.2s ease-in-out opacity;

    --toggle-animations-icon-width: 0;
    --feeds-icon-width: 0;
    --actions-icon-width: 0;

    display: block;
    min-height: var(--timeline-row-height);
    position: relative;

    &.group-node {
        &:not(.collapsed-group) {
            .name,
            ui-input ::ng-deep .input {
                font-weight: var(--ui-font-weight-semibold);
                letter-spacing: -0.25px;
            }
        }

        .left-panel {
            grid-template-columns:
                var(--icon-width)
                minmax(0, 1fr)
                var(--kebab-menu-width);
        }

        .canvas-wrapper {
            height: 100%;
            overflow: hidden;
        }

        .element-canvas {
            height: 100%;
        }
    }

    &.expanded {
        &:not(.selected, .in-group) {
            background-color: transparent;
        }

        .name,
        ui-input ::ng-deep .input {
            font-weight: var(--ui-font-weight-semibold);
            letter-spacing: -0.4px !important;
        }
    }

    &.hover,
    &.selected {
        &:not(.scrolling):not(.transforming) {
            background-color: rgba(230, 230, 230, 0.35);
        }
    }

    &:hover,
    &.selected,
    &.expanded {
        .icon:not(ui-svg-icon) {
            opacity: 1;
        }

        .element-features .icon {
            &.actions,
            &.feeds {
                .icon {
                    color: var(--studio-color-text);
                }
            }
        }
    }

    &.selecting .left-panel {
        pointer-events: none;
    }

    &.hidden {
        .name {
            color: var(--text-color-second);
        }

        .eye {
            opacity: 1;
        }
    }

    .input {
        margin-right: 1px;
    }

    .summary {
        display: grid;
        height: var(--timeline-row-height);
        grid-template-columns: var(--left-panel-width) auto;
        position: relative;
        transition: background-color 0.2s ease;
    }

    .left-panel {
        display: grid;
        grid-template-columns:
            var(--icon-width)
            minmax(0, 1fr)
            var(--toggle-animations-icon-width) var(--kebab-menu-width);

        height: 100%;
        position: relative;
        padding: 0 var(--left-panel-padding);

        &.hidden {
            .icon.toggle-group,
            .element-features {
                opacity: 0.4;
            }

            ui-input ::ng-deep .input {
                opacity: 0.4;

                &:focus {
                    opacity: 1;
                }
            }
        }

        &.keyframes-enabled {
            --toggle-animations-icon-width: var(--icon-width);
        }
    }

    .right-canvas {
        z-index: 1;
        margin-left: calc(var(--canvas-padding) * (-1));
        position: relative;
        width: calc(100% + var(--canvas-padding));
        height: var(--timeline-row-height);
    }

    .name {
        display: flex;
        align-items: center;

        .visible-button {
            color: var(--text-color-second);
            margin-right: var(--name-input-padding);
        }

        .name-placeholder {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            padding: 1px calc(var(--name-input-padding) + 2px) 0 var(--name-input-padding);
            margin-top: 1px;
        }

        .element-features {
            margin-left: 4px;
            display: flex;

            .icon {
                pointer-events: none;
                margin-right: 4px;

                &:last-child {
                    margin-right: 0;
                }

                .icon {
                    color: var(--studio-color-text-disabled);
                }
            }
        }

        ui-input ::ng-deep .input {
            cursor: default;
            padding-left: calc(var(--name-input-padding) - 1px);
            padding-right: calc(var(--name-input-padding) - 1px);
            height: var(--timeline-name-input-height);
            letter-spacing: -0.25px;

            &:focus {
                cursor: text;
            }
        }
    }

    .icon:not(ui-svg-icon) {
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        opacity: 0;
        transition:
            color 0.2s ease,
            opacity 0.2s ease;
        color: var(--icon-color);
        overflow: hidden;

        &.lock,
        &.toggle-group,
        &.masking {
            opacity: 1;
            margin-right: 2px;
        }

        &.eye {
            padding-left: var(--name-input-padding);
        }

        &.feature {
            opacity: var(--ui-disabled-opacity);
        }

        &.active,
        &:hover {
            color: var(--icon-color-active);
        }

        &.hidden {
            opacity: 0 !important;
            pointer-events: none;
        }

        &.masking {
            overflow: visible;
            position: absolute;
        }
    }

    .track {
        position: relative;
        overflow: hidden;
    }

    .animations-expanded {
        display: block;

        section-expand {
            --border-color: var(--studio-color-border-second);
        }
    }

    .element-canvas {
        pointer-events: none;
        width: 100%;
        height: var(--timeline-row-height);
    }

    .toggle-group {
        flex-shrink: 0;
        flex-basis: 14px;
    }

    .masking {
        height: 100%;

        .masking-path {
            position: absolute;
            left: 0;
            top: 0;

            &.start {
                transform: translate(-15px, 12px);
            }
            &.line {
                transform: translate(-11px, 0px);
            }
            &.end {
                transform: translate(-15px, 0px);
            }
        }
    }
}
