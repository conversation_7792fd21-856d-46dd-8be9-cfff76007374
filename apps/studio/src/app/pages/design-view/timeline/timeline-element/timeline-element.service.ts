import { Injectable } from '@angular/core';
import { ITimelineNode, OneOfDataNodes } from '@domain/nodes';
import { BehaviorSubject, Subject, tap, withLatestFrom } from 'rxjs';
import { map } from 'rxjs/operators';
import { TimelineElementComponent } from './timeline-element.component';
import { SentinelService } from '@bannerflow/sentinel';
import { GroupDataNode } from '@creative/nodes';

const COLLAPSED_GROUPS_KEY = 'collapsed-groups';

@Injectable({ providedIn: 'root' })
export class TimelineElementService {
    isRenaming = false;
    private _toggleGroup$ = new Subject<TimelineElementComponent>();
    private _toggleVisibility$ = new Subject<void>();
    private _nodes$ = new BehaviorSubject<ITimelineNode[]>([]);
    nodeIsMoving = false;
    nodes$ = this._nodes$.asObservable().pipe(tap(nodes => (this._nodes = nodes)));
    toggleGroup$ = this._toggleGroup$.asObservable().pipe(withLatestFrom(this.nodes$));
    toggleVisibility$ = this._toggleVisibility$.asObservable().pipe(
        withLatestFrom(this.nodes$),
        map(([_, nodes]) => nodes)
    );
    private collapsedGroups?: Record<string, boolean>;
    private groupPersistPrefix = '';

    _nodes: ITimelineNode[] = [];
    get nodes(): ITimelineNode[] {
        return this._nodes;
    }

    constructor(private sentinelService: SentinelService) {}

    toggleGroup(tlComponent: TimelineElementComponent): void {
        this.persistGroupCollapseMode(tlComponent);
        this._toggleGroup$.next(tlComponent);
    }

    private loadPersistedCollapsedGroups(): Record<string, boolean> {
        try {
            this.collapsedGroups = JSON.parse(
                localStorage.getItem(COLLAPSED_GROUPS_KEY) || '{}'
            ) as Record<string, boolean>;

            return this.collapsedGroups;
        } catch (error) {
            this.sentinelService.error(error, {
                localStorageValue: localStorage.getItem(COLLAPSED_GROUPS_KEY) || ''
            });
            return {};
        }
    }

    removeGroupCollapseMode(groupNode: GroupDataNode): void {
        const collapsedGroups = this.loadPersistedCollapsedGroups();
        const groupId = groupNode.id;
        const key = `${this.groupPersistPrefix}-${groupId}`;
        delete collapsedGroups[key];
        this.collapsedGroups = collapsedGroups;
        localStorage.setItem(COLLAPSED_GROUPS_KEY, JSON.stringify(this.collapsedGroups));
    }

    setGroupPersistPrefix(prefix: string): void {
        this.groupPersistPrefix = prefix;
        this.loadPersistedCollapsedGroups();
    }

    private persistGroupCollapseMode(tlComponent: TimelineElementComponent): void {
        const collapsedGroups = this.loadPersistedCollapsedGroups();
        const groupId = tlComponent.node.id;
        const isCollapsed = tlComponent.collapsedGroup;
        const key = `${this.groupPersistPrefix}-${groupId}`;
        if (isCollapsed) {
            collapsedGroups[key] = true;
        } else {
            delete collapsedGroups[key];
        }
        this.collapsedGroups = collapsedGroups;
        localStorage.setItem(COLLAPSED_GROUPS_KEY, JSON.stringify(collapsedGroups));
    }

    toggleVisibility(): void {
        this._toggleVisibility$.next();
    }

    setNodeList(timelineNodeList: ITimelineNode[]): void {
        this._nodes$.next(timelineNodeList);
    }

    isGroupCollapsed(node: OneOfDataNodes): boolean {
        const groupId = node.id;
        const key = `${this.groupPersistPrefix}-${groupId}`;

        return !!this.collapsedGroups?.[key];
    }

    isCollapsed(node: OneOfDataNodes): boolean {
        return !!this._nodes.find(n => n.node.id === node.id)?.collapsed;
    }
}
