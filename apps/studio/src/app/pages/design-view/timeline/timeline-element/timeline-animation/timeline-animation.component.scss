:where(:root:not([data-uinew])) :host {
    display: grid;
    height: var(--timeline-row-height);
    grid-template-columns: var(--left-panel-width) auto;
    position: relative;

    background: linear-gradient(
        90deg,
        var(--studio-color-transparent) calc(var(--durationLineX) - 1px),
        var(--durationLineColor) calc(var(--durationLineX)),
        var(--studio-color-transparent) calc(var(--durationLineX) + 1px)
    );

    &:before {
        content: '';
        z-index: 1;
        width: 1px;
        height: 100%;
        position: absolute;
        left: var(--left-panel-width);
        top: 0;
        background: var(--border-color);
    }

    &.hidden {
        .name {
            color: var(--studio-color-text-second);
        }

        .eye {
            opacity: 1;
            color: var(--studio-color-black);
        }
    }

    &.selected,
    &.hover,
    &:hover {
        .icon {
            opacity: 1;

            &:hover {
                color: var(--studio-color-black);
            }
        }
    }

    &:not(.scrolling):not(.transforming) {
        &:hover,
        &.hover {
            background-color: var(--studio-color-background);

            .menu {
                display: flex;
            }
        }
    }

    &.selecting .wrapper {
        pointer-events: none;
    }

    .left-panel {
        display: grid;
        grid-template-columns: var(--icon-width) minmax(0, 1fr) var(--kebab-menu-width);
        height: var(--timeline-row-height);
        position: relative;
        padding: 0 var(--left-panel-padding);
    }

    .right-canvas {
        z-index: 1;
        margin-left: calc(var(--canvas-padding) * (-1));
        position: relative;
        width: calc(100% + var(--canvas-padding));
        height: var(--timeline-row-height);
    }

    .name {
        display: flex;
        align-items: center;

        .visible-button {
            color: var(--studio-color-text-second);
            margin-right: var(--name-input-padding);
        }

        .name-placeholder {
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            padding: 1px var(--name-input-padding) 0 var(--name-input-padding);
        }

        &.active {
            & ::ng-deep .input {
                font-weight: var(--ui-font-weight-semibold);
            }
        }

        ui-input {
            & ::ng-deep .input {
                cursor: default;
                padding-left: calc(var(--name-input-padding) - 1px);
                padding-right: calc(var(--name-input-padding) - 1px);
                height: var(--timeline-name-input-height);

                &:hover:not(:focus) {
                    background: transparent;
                }

                &:focus {
                    cursor: text;
                }
            }

            & ::ng-deep .input:disabled {
                background: transparent;
                color: var(--studio-color-text);
            }
        }
    }

    .icon:not(ui-svg-icon) {
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition:
            color 0.2s ease,
            opacity 0.2s ease;
        opacity: 0;

        &.eye {
            width: 100%;
            height: 100%;
            cursor: pointer;
            color: var(--studio-color-grey-84);
            padding-left: 4px;

            &.visibility-hidden {
                opacity: 1;

                .icon {
                    opacity: 1;
                }
            }
        }

        &.menu,
        &.toggle-animations {
            color: var(--studio-color-text-second);

            &.active,
            &:hover {
                color: var(--studio-color-black);
            }
        }
    }

    .track {
        position: relative;
        overflow: hidden;
    }

    .animations-wrapper {
        grid-column: 1 / 2;
    }

    .animation-canvas {
        pointer-events: none;
        width: 100%;
        height: var(--timeline-row-height);
    }
}
