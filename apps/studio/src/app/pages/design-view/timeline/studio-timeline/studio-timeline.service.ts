import { Injectable, computed, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { UserSettingsService } from '@studio/common/user-settings/user-settings.service';
import { take } from 'rxjs';
import { TIMELINE_MIN_HEIGHT } from '../timeline.constants';

@Injectable({ providedIn: 'root' })
export class StudioTimelineService {
    private userSettingsService = inject(UserSettingsService);
    resizeExpandedHeight: number;
    defaultHeight = 180;
    collapsed = false;
    private _playheadTime = signal(0);
    playheadTime = computed(() => this._playheadTime());

    constructor() {
        this.userSettingsService.timelineHeight$
            .pipe(take(1), takeUntilDestroyed())
            .subscribe(height => {
                this.defaultHeight = height;
                if (this.defaultHeight < TIMELINE_MIN_HEIGHT) {
                    this.collapsed = true;
                }
            });
    }

    setPlayheadTime(time: number): void {
        this._playheadTime.set(time);
    }
}
