@if (playheadPosition >= 0) {
    <div
        class="playhead"
        data-test-id="playhead"
        #playhead
        [style.transform]="'translateX(' + playheadPosition + 'px)'"
        [class.hidden]="!playheadIsVisible"
        [class.active]="timelineTransformService.actionMode === ActionMode.Seek"
        [class.nohover]="timelineTransformService.actionMode !== ActionMode.None"
        [class.recording]="animationRecorderService.recording$ | async">
        <div class="head"></div>
        <div class="line"></div>
    </div>
}
<div
    class="resize-handle resize-90"
    (dblclick)="toggle()"
    (mousedown)="onStartResizeTimeline($event)"></div>
<div class="wrapper">
    @if (!nodes.length && !collapsed) {
        <div
            class="placeholder"
            [style.height]="
                'calc(100% - var(--time-ruler-height) * ' + (creative.gifExport.show ? 2 : 1) + ')'
            ">
            <ui-label
                class="placeholder-elements"
                size="sm"
                type="secondary">
                No elements
            </ui-label>
            <div class="placeholder-timeruler"></div>
        </div>
    }
    <div class="top">
        <div class="playback">
            <div class="controls">
                <ui-button
                    id="step-back-btn"
                    data-test-id="step-back-btn"
                    nuiType="plain-primary"
                    #backButton
                    [uiTooltipDisabled]="!animator.isPlaying"
                    uiTooltip="Jump to start (↑)"
                    uiTooltipPosition="top"
                    size="sm"
                    [nuiSvgIcon]="animator.isPlaying ? 'first_page' : 'skip_previous'"
                    (click)="stepBack($event)" />
                <ui-button
                    id="play-pause-btn"
                    data-test-id="play-pause-btn"
                    nuiType="plain-primary"
                    #playButton
                    size="sm"
                    (click)="togglePlay($event)">
                    <ui-label
                        class="play-pause-icon"
                        weight="bold"
                        size="sm"
                        [leadingIcon]="animator.isPlaying ? 'pause' : 'play_arrow'" />
                </ui-button>
                <ui-button
                    id="step-forward-btn"
                    data-test-id="step-forward-btn"
                    nuiType="plain-primary"
                    #forwardButton
                    size="sm"
                    [nuiSvgIcon]="'skip_next'"
                    (click)="stepForward($event)" />
                <ui-button
                    id="record-btn"
                    nuiType="plain-primary"
                    #recordButton
                    size="sm"
                    class="record-button"
                    (click)="toggleRecording($event)"
                    ><ui-label
                        [class.recording]="animationRecorderService.recording$ | async"
                        weight="bold"
                        size="sm"
                        leadingIcon="nest_heat_link_e"
                /></ui-button>
            </div>
            <div class="time">
                <input
                    class="seek-input"
                    type="number"
                    step="0.01"
                    #seekInput
                    [value]="currentTime"
                    (cancel)="seek()"
                    (blur)="seek($any(seekInput.value))"
                    (submit)="seek($any(seekInput.value))" />

                <span class="time-slash">/</span>

                <input
                    class="duration-input"
                    #durationInput
                    type="number"
                    step="0.01"
                    ui-theme="tiny"
                    id="duration"
                    [ngClass]="{ 'duration-warning': duration > 15 && (isSocialCreative$ | async) }"
                    [uiTooltip]="
                        'Duration of this creative is longer than 15 seconds. If you use it as a video in Facebook or Instagram story, it will be truncated.'
                    "
                    [uiTooltipDelay]="0"
                    [uiTooltipWidth]="250"
                    [uiTooltipDisabled]="!(duration > 15 && (isSocialCreative$ | async))"
                    [value]="duration.toFixed(2)"
                    (cancel)="setDuration(undefined)"
                    (blur)="setDuration(durationInput.value)"
                    (submit)="setDuration(durationInput.value)" />
            </div>
        </div>
        <div class="timeruler-space">
            <time-ruler
                [showNumbers]="true"
                [height]="timeRulerHeight"></time-ruler>
            @if (nodes.length) {
                @if (creative.loops > 0 && stopTimePosition() >= 0) {
                    <ui-pill
                        type="secondary"
                        uiTooltip="Stop here on the {{ creative.loops | uiOrdinal }} loop"
                        [uiTooltipDisabled]="
                            timelineTransformService.actionMode === ActionMode.SetStopTime ||
                            timelineTransformService.actionMode === ActionMode.Seek
                        "
                        class="timeline-draggable-icon stop-time"
                        [ngClass]="{ active: timelineTransformService.stopTimeMarkerActive }"
                        [style.transform]="'translateX(' + stopTimePosition() + 'px)'">
                        {{ creative.loops }}
                    </ui-pill>
                }
                @if (scroll.x < secondsToPixels(creative.getFirstPreloadImageFrame()) + 5) {
                    <div
                        uiTooltip="Capture preload image at this point in time"
                        [uiTooltipDisabled]="
                            timelineTransformService.actionMode === ActionMode.SetPreloadFrame ||
                            timelineTransformService.actionMode === ActionMode.Seek
                        "
                        class="timeline-draggable-icon preload-frame"
                        data-test-id="preload-image-frame"
                        [ngClass]="{ active: timelineTransformService.preloadFrameActive }"
                        [style.transform]="
                            'translateX(' +
                            preloadFramePosition(creative.getFirstPreloadImageFrame()) +
                            'px)'
                        ">
                        <ui-svg-icon
                            icon="none"
                            nuiIcon="panorama"
                            size="sm" />
                    </div>
                }
            }
        </div>
        <ui-button
            nuiType="ghost-primary"
            class="toggle-button"
            size="sm"
            [nuiSvgIcon]="collapsed ? 'keyboard_arrow_up' : 'keyboard_arrow_down'"
            (click)="toggle()" />
    </div>
    @if (creative.gifExport.show) {
        <gif-frames></gif-frames>
    }
    <div class="elements-container">
        <div
            id="timeline-elements"
            class="elements"
            #elementsContainer>
            <div class="elements-inner-wrapper">
                @for (nodeItem of nodes$ | async; track getElementId(i, nodeItem); let i = $index) {
                    <timeline-element
                        [ngStyle]="{
                            display: nodeItem.collapsed && nodeItem.node.__parentNode ? 'none' : 'block'
                        }"
                        [ngClass]="{
                            scrolling: scrollService.isScrolling,
                            selecting: selectionNet,
                            'group-node': nodeItem.node.kind === ElementKind.Group
                        }"
                        [style.z-index]="nodeItem.node.kind === ElementKind.Group ? 0 : 1"
                        [elementIndex]="nodes.length - i - 1"
                        [attr.data-test-id]="'timeline-element-' + i"
                        [attr.data-test-name]="'timeline-element-' + nodeItem.node.name"
                        [node]="nodeItem.node"
                        [isGroupCollapsed]="!!nodeItem.isGroupCollapsed"
                        [collapsed]="
                            nodeItem.collapsed && !!nodeItem.node.__parentNode
                        "></timeline-element>
                }
            </div>
        </div>
    </div>
    <div
        class="zoom"
        [ngClass]="{
            zooming,
            disabled: zoomBox || !nodes.length || scrollService.isScrolling
        }">
        <div
            class="zoom-controls"
            #zoomControl
            uiCard
            size="xs">
            <ui-range
                [min]="zoomConfig.min"
                size="xs"
                [max]="zoomConfig.max"
                [direction]="'vertical'"
                [step]="1"
                [tracklight]="true"
                [value]="(zoomService.zoom$ | async) || zoomConfig.min"
                (valueChange)="zoomService.setZoom($event)"
                (isMouseDown)="setZooming($event)"
                (mousemove)="$event.stopPropagation()" />
            <ui-button
                nuiType="plain-primary"
                class="zoom-in"
                size="sm"
                nuiSvgIcon="zoom_in"
                (click)="zoomIn($event)" />
            <ui-button
                nuiType="plain-primary"
                class="zoom-out"
                size="sm"
                nuiSvgIcon="zoom_out"
                (click)="zoomOut($event)" />
        </div>
        <ui-svg-icon
            class="zoom-toggle-icon"
            nuiIcon="search"
            size="sm"
            icon="none"></ui-svg-icon>
    </div>
    <canvas
        #gizmoOverlayCanvas
        class="gizmo-overlay-canvas"></canvas>
</div>

<!-- Animation menu -->
<div
    class="dropdown-trigger"
    #transitionAnimationMenuTrigger="uiDropdownTarget"
    [uiDropdownTarget]="transitionAnimationMenu"
    [hasBackdrop]="true"></div>

<ui-dropdown
    #transitionAnimationMenu
    id="timeline-animation-menu"
    type="menu"
    [minWidth]="140"
    size="sm"
    [positions]="transitionAnimationMenuPositions"
    [width]="'140'">
    <ui-dropdown-item
        nuiIcon="remove"
        (click)="clearAnimation(selectedElementAnimations?.type)">
        No animation
    </ui-dropdown-item>
    <ui-dropdown-divider></ui-dropdown-divider>
    @for (animation of animationTemplates; track animation.id) {
        @if (!animation.settings) {
            <ui-dropdown-item
                class="custom-dropdown"
                [nuiIcon]="
                    selectedElementAnimations && selectedElementAnimations.templateId === animation.id
                        ? 'check'
                        : undefined
                "
                [attr.data-test-id]="'timeline-animation-' + animation.name"
                (click)="setTransitionAnimationOnElement(animation)">
                {{ animation.name }}
            </ui-dropdown-item>
        }
        @if (animation.settings) {
            <ui-dropdown-item
                class="custom-dropdown"
                (click)="setTransitionAnimationOnElement(animation)"
                (mouseenter)="setCurrentTemplate(animation)"
                [uiDropdownTarget]="directionMenu"
                [nuiIcon]="
                    selectedElementAnimations && selectedElementAnimations.templateId === animation.id
                        ? 'check'
                        : undefined
                ">
                {{ animation.name }}
            </ui-dropdown-item>
        }
    }
</ui-dropdown>

<ui-dropdown
    type="menu"
    #directionMenu
    [width]="'120'"
    [minWidth]="120"
    size="sm"
    [useTargetWidth]="false"
    [positions]="transitionAnimationSubMenuPositions">
    @if (selectedElementAnimations) {
        <ui-dropdown-item
            nuiIcon="west"
            (itemClicked)="
                setAnimationWithSettingOnElement({
                    direction: {
                        name: 'Direction',
                        value: animationDirectionValues[selectedElementAnimations.type].left
                    }
                })
            ">
            Left
        </ui-dropdown-item>
        <ui-dropdown-item
            nuiIcon="north"
            (itemClicked)="
                setAnimationWithSettingOnElement({
                    direction: {
                        name: 'Direction',
                        value: animationDirectionValues[selectedElementAnimations.type].up
                    }
                })
            ">
            Up
        </ui-dropdown-item>
        <ui-dropdown-item
            nuiIcon="east"
            (itemClicked)="
                setAnimationWithSettingOnElement({
                    direction: {
                        name: 'Direction',
                        value: animationDirectionValues[selectedElementAnimations.type].right
                    }
                })
            ">
            Right
        </ui-dropdown-item>
        <ui-dropdown-item
            nuiIcon="south"
            (itemClicked)="
                setAnimationWithSettingOnElement({
                    direction: {
                        name: 'Direction',
                        value: animationDirectionValues[selectedElementAnimations.type].down
                    }
                })
            ">
            Down
        </ui-dropdown-item>
    }
</ui-dropdown>

<masking-svgs [isNewUI]="isNewUI"></masking-svgs>
