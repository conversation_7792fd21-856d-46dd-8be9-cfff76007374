@if (playheadPosition >= 0) {
    <div
        class="playhead"
        data-test-id="playhead"
        #playhead
        [style.transform]="'translateX(' + playheadPosition + 'px)'"
        [class.hidden]="!playheadIsVisible"
        [class.active]="timelineTransformService.actionMode === ActionMode.Seek"
        [class.nohover]="timelineTransformService.actionMode !== ActionMode.None"
        [class.recording]="animationRecorderService.recording$ | async">
        <div class="head"></div>
        <div class="line"></div>
    </div>
}
<div
    class="resize-handle resize-90"
    (dblclick)="toggle()"
    (mousedown)="onStartResizeTimeline($event)"></div>
<div class="wrapper">
    @if (!nodes.length && !collapsed) {
        <div
            class="placeholder"
            [style.height]="
                'calc(100% - var(--time-ruler-height) * ' + (creative.gifExport.show ? 2 : 1) + ')'
            ">
            <div class="placeholder-elements">No elements</div>
            <div class="placeholder-timeruler">
                Control duration and animation of elements here after creating them on canvas
            </div>
        </div>
    }
    <div class="top">
        <div class="playback">
            <div class="controls">
                <ui-svg-icon
                    id="step-back-btn"
                    data-test-id="step-back-btn"
                    #backButton
                    [icon]="animator.isPlaying ? 'playback-start-small' : 'playback-step-left-small'"
                    [uiTooltipDisabled]="!animator.isPlaying"
                    [uiTooltip]="'Jump to start (↑)'"
                    (click)="stepBack($event)"></ui-svg-icon>
                <ui-svg-icon
                    id="play-pause-btn"
                    data-test-id="play-pause-btn"
                    #playButton
                    class="primary"
                    (click)="togglePlay($event)"
                    [icon]="
                        animator.isPlaying ? 'playback-pause-small' : 'playback-play-small'
                    "></ui-svg-icon>
                <ui-svg-icon
                    id="step-forward-btn"
                    data-test-id="step-forward-btn"
                    #forwardButton
                    icon="playback-step-right-small"
                    (click)="stepForward($event)"></ui-svg-icon>
                <ui-svg-icon
                    id="record-btn"
                    #recordButton
                    class="record"
                    [class.recording]="animationRecorderService.recording$ | async"
                    icon="playback-rec"
                    (click)="toggleRecording($event)"></ui-svg-icon>
            </div>
            <div class="time">
                <ui-input
                    class="seek-input"
                    #seekInput
                    ui-theme="tiny"
                    [discrete]="true"
                    [selectTextOnFocus]="true"
                    [blurOnSubmit]="true"
                    [value]="currentTime"
                    (cancel)="seek()"
                    (blur)="seek($any(seekInput.value))"
                    (submit)="seek($any(seekInput.value))">
                </ui-input>
                <span class="time-slash">/</span>
                <ui-input
                    class="duration-input"
                    #durationInput
                    ui-theme="tiny"
                    id="duration"
                    [ngClass]="{ 'duration-warning': duration > 15 && (isSocialCreative$ | async) }"
                    [uiTooltip]="
                        'Duration of this creative is longer than 15 seconds. If you use it as a video in Facebook or Instagram story, it will be truncated.'
                    "
                    [uiTooltipDelay]="0"
                    [uiTooltipWidth]="250"
                    [uiTooltipDisabled]="!(duration > 15 && (isSocialCreative$ | async))"
                    [selectTextOnFocus]="true"
                    [blurOnSubmit]="true"
                    [discrete]="true"
                    [value]="duration.toFixed(2)"
                    (cancel)="setDuration(undefined)"
                    (blur)="setDuration(durationInput.value)"
                    (submit)="setDuration(durationInput.value)">
                </ui-input>
            </div>
        </div>
        <div class="timeruler-space">
            <time-ruler
                [showNumbers]="true"
                [height]="timeRulerHeight"></time-ruler>
            @if (nodes.length) {
                @if (creative.loops > 0 && stopTimePosition() >= 0) {
                    <div
                        uiTooltip="Stop here on the {{ creative.loops | uiOrdinal }} loop"
                        [uiTooltipDisabled]="
                            timelineTransformService.actionMode === ActionMode.SetStopTime ||
                            timelineTransformService.actionMode === ActionMode.Seek
                        "
                        class="timeline-draggable-icon stop-time"
                        [ngClass]="{ active: timelineTransformService.stopTimeMarkerActive }"
                        [style.transform]="'translateX(' + stopTimePosition() + 'px)'">
                        <div class="loops">{{ creative.loops }}</div>
                    </div>
                }
                @if (scroll.x < secondsToPixels(creative.getFirstPreloadImageFrame()) + 5) {
                    <div
                        uiTooltip="Capture preload image at this point in time"
                        [uiTooltipDisabled]="
                            timelineTransformService.actionMode === ActionMode.SetPreloadFrame ||
                            timelineTransformService.actionMode === ActionMode.Seek
                        "
                        class="timeline-draggable-icon preload-frame"
                        data-test-id="preload-image-frame"
                        [ngClass]="{ active: timelineTransformService.preloadFrameActive }"
                        [style.transform]="
                            'translateX(' +
                            preloadFramePosition(creative.getFirstPreloadImageFrame()) +
                            'px)'
                        ">
                        <ui-svg-icon icon="image"></ui-svg-icon>
                    </div>
                }
            }
        </div>
        <div
            class="toggle-button"
            [ngClass]="{ collapsed: collapsed }"
            (click)="toggle()">
            <ui-svg-icon icon="arrow-down"></ui-svg-icon>
        </div>
    </div>
    @if (creative.gifExport.show) {
        <gif-frames></gif-frames>
    }
    <div class="elements-container">
        <div
            id="timeline-elements"
            class="elements"
            #elementsContainer>
            <div class="elements-inner-wrapper">
                @for (nodeItem of nodes$ | async; track getElementId(i, nodeItem); let i = $index) {
                    <timeline-element
                        [ngStyle]="{
                            display: nodeItem.collapsed && nodeItem.node.__parentNode ? 'none' : 'block'
                        }"
                        [ngClass]="{
                            scrolling: scrollService.isScrolling,
                            selecting: selectionNet,
                            'group-node': nodeItem.node.kind === ElementKind.Group
                        }"
                        [style.z-index]="nodeItem.node.kind === ElementKind.Group ? 0 : 1"
                        [elementIndex]="nodes.length - i - 1"
                        [attr.data-test-id]="'timeline-element-' + i"
                        [attr.data-test-name]="'timeline-element-' + nodeItem.node.name"
                        [node]="nodeItem.node"
                        [isGroupCollapsed]="!!nodeItem.isGroupCollapsed"
                        [collapsed]="
                            nodeItem.collapsed && !!nodeItem.node.__parentNode
                        "></timeline-element>
                }
            </div>
        </div>
    </div>
    <div
        #zoomControl
        class="zoom"
        [ngClass]="{
            zooming: slider.mouseIsDown,
            disabled: zoomBox || !nodes.length || scrollService.isScrolling
        }">
        <div class="slider">
            <!--
      This is the only occurrence of old studio-ui-slider in the Studio codebase.
      All other instances use ui-range from bf-ui. The new ui-range uses native <input type="range"> under the hood.
      I couldn't make it work in the timeline zoom slider. In isolation and in all other places in Studio it works as expected.

      Potential reasons for ui-range not working here:
      - Complex event handling for zoom interactions may conflict with native range input behavior
      - Integration with existing zoom service and transform logic

      Might be a helpful info:
      ui-range works fine when event.preventDefault() is removed from this.mouseDownMove$  (mouse-observable.ts)
      -->
            <studio-ui-slider
                #slider
                [value]="(zoomService.zoom$ | async) || zoomConfig.min"
                (valueChange)="zoomService.setZoom($event)"
                [config]="{
                    min: zoomConfig.min,
                    max: zoomConfig.max
                }"
                [direction]="'vertical'"
                [tracklight]="false"></studio-ui-slider>
        </div>
        <ui-svg-icon
            (click)="zoomIn($event)"
            class="zoom-in"
            icon="zoom-in"></ui-svg-icon>
        <ui-svg-icon
            (click)="zoomOut($event)"
            class="zoom-out"
            icon="zoom-out">
        </ui-svg-icon>

        <ui-svg-icon
            class="magnifier"
            icon="zoom"></ui-svg-icon>
    </div>
    <canvas
        #gizmoOverlayCanvas
        class="gizmo-overlay-canvas"></canvas>
</div>

<!-- Animation menu -->
<div
    class="dropdown-trigger"
    #transitionAnimationMenuTrigger="uiDropdownTarget"
    [uiDropdownTarget]="transitionAnimationMenu"
    [hasBackdrop]="true"></div>

<ui-dropdown
    #transitionAnimationMenu
    id="timeline-animation-menu"
    type="menu"
    [minWidth]="120"
    [positions]="transitionAnimationMenuPositions"
    [width]="'120'">
    <ui-dropdown-item
        class="custom-dropdown"
        (click)="clearAnimation(selectedElementAnimations?.type)">
        <div
            class="custom-item"
            data-test-id="timeline-animation-none">
            <ui-svg-icon
                class="custom-column icon"
                icon="minus-small"></ui-svg-icon>
            <div class="custom-text">No animation</div>
        </div>
    </ui-dropdown-item>
    <ui-dropdown-divider></ui-dropdown-divider>
    @for (animation of animationTemplates; track animation.id) {
        @if (!animation.settings) {
            <ui-dropdown-item
                class="custom-dropdown"
                [attr.data-test-id]="'timeline-animation-' + animation.name"
                (click)="setTransitionAnimationOnElement(animation)">
                <div class="custom-item">
                    <div class="custom-column">
                        @if (
                            selectedElementAnimations &&
                            selectedElementAnimations.templateId === animation.id
                        ) {
                            <ui-svg-icon
                                class="icon active"
                                icon="menu-checked">
                            </ui-svg-icon>
                        }
                    </div>
                    <div class="custom-text">
                        {{ animation.name }}
                    </div>
                </div>
            </ui-dropdown-item>
        }
        @if (animation.settings) {
            <ui-dropdown-item
                class="custom-dropdown"
                (click)="setTransitionAnimationOnElement(animation)"
                (mouseenter)="setCurrentTemplate(animation)"
                [uiDropdownTarget]="directionMenu">
                <div class="custom-item">
                    <div class="custom-column">
                        @if (
                            selectedElementAnimations &&
                            selectedElementAnimations.templateId === animation.id
                        ) {
                            <ui-svg-icon
                                class="icon active"
                                icon="menu-checked"></ui-svg-icon>
                        }
                    </div>
                    <div
                        class="custom-text"
                        [attr.data-test-id]="'timeline-animation-' + animation.name">
                        {{ animation.name }}
                    </div>
                </div>
            </ui-dropdown-item>
        }
    }
</ui-dropdown>

<ui-dropdown
    type="menu"
    #directionMenu
    [width]="'120'"
    [minWidth]="120"
    [useTargetWidth]="false"
    [positions]="transitionAnimationSubMenuPositions">
    @if (selectedElementAnimations) {
        <ui-dropdown-item
            class="custom-dropdown"
            (itemClicked)="
                setAnimationWithSettingOnElement({
                    direction: {
                        name: 'Direction',
                        value: animationDirectionValues[selectedElementAnimations.type].left
                    }
                })
            ">
            <div class="custom-item">
                <ui-svg-icon
                    class="custom-column icon active"
                    icon="direction-left"></ui-svg-icon>
                <div
                    class="custom-text"
                    id="timeline-animation-direction-left">
                    Left
                </div>
            </div>
        </ui-dropdown-item>
    }
    @if (selectedElementAnimations) {
        <ui-dropdown-item
            class="custom-dropdown"
            (itemClicked)="
                setAnimationWithSettingOnElement({
                    direction: {
                        name: 'Direction',
                        value: animationDirectionValues[selectedElementAnimations.type].up
                    }
                })
            ">
            <div class="custom-item">
                <ui-svg-icon
                    class="custom-column icon"
                    icon="direction-up"></ui-svg-icon>
                <div
                    class="custom-text"
                    id="timeline-animation-direction-up">
                    Up
                </div>
            </div>
        </ui-dropdown-item>
    }
    @if (selectedElementAnimations) {
        <ui-dropdown-item
            class="custom-dropdown"
            (itemClicked)="
                setAnimationWithSettingOnElement({
                    direction: {
                        name: 'Direction',
                        value: animationDirectionValues[selectedElementAnimations.type].right
                    }
                })
            ">
            <div class="custom-item">
                <ui-svg-icon
                    class="custom-column icon"
                    icon="direction-right"></ui-svg-icon>
                <div
                    class="custom-text"
                    id="timeline-animation-direction-right">
                    Right
                </div>
            </div>
        </ui-dropdown-item>
    }
    @if (selectedElementAnimations) {
        <ui-dropdown-item
            class="custom-dropdown"
            (itemClicked)="
                setAnimationWithSettingOnElement({
                    direction: {
                        name: 'Direction',
                        value: animationDirectionValues[selectedElementAnimations.type].down
                    }
                })
            ">
            <div class="custom-item">
                <ui-svg-icon
                    class="custom-column icon"
                    icon="direction-down"></ui-svg-icon>
                <div
                    class="custom-text"
                    id="timeline-animation-direction-down">
                    Down
                </div>
            </div>
        </ui-dropdown-item>
    }
</ui-dropdown>

<masking-svgs></masking-svgs>
