$leftPanelWidth: 180px;
$timeRulerHeight: 24px;
:host {
    --durationLineX: 0px;
    --durationLineColor: transparent;
    --left-panel-width: 180px;
    --left-panel-padding: 0px;
    --name-input-padding: 2px;
    /** Should be synced with TIMELINE_ELEMENT_HEIGHT constant */
    --timeline-row-height: 26px;
    --timeline-name-input-height: 18px;
    --time-ruler-height: 24px; // Note that a 1px border is added onto this
    --zoom-control-width: 26px;
    --icon-width: 24px;
    --feature-count: 0;
    --canvas-padding: 5px; // Enable selections being drawn "outside" canvas
    --kebab-menu-width: 20px;
    --background-color: var(--studio-color-surface);
    --secondary-background-color: var(--studio-color-surface-second);
    --border-color: var(--studio-color-border-second);
    --secondary-border-color: var(--studio-color-border);
    --icon-color: var(--studio-color-text-second);
    --icon-color-active: var(--studio-color-text);
    --text-color: var(--studio-color-text);
    --text-color-second: var(--studio-color-text-second);

    position: relative;
    display: block;
    width: 100%;
    height: 180px;
    transition: height 0.25s ease;
    box-sizing: border-box;
    border-top: 1px solid var(--studio-color-border-second);

    &.resizing {
        transition: none;
    }

    &.collapsed {
        .zoom {
            display: none;
        }
    }

    &.playhead-active {
        cursor: pointer !important;
    }

    &:before {
        content: '';
        width: 1px;
        height: 100%;
        position: absolute;
        left: var(--left-panel-width);
        top: 0;
        background: var(--studio-color-border-second);
    }
}

.dropdown-trigger {
    visibility: hidden;
    position: fixed;
    width: 120px;
}

.wrapper {
    width: 100%;
    height: 100%;
    overflow: hidden;
    margin-top: -1px;
}

.resize-handle {
    left: 0;
    top: -4px;
    right: 0;
    height: 8px;
    z-index: 4;
    position: absolute;
}

.top {
    height: calc(var(--time-ruler-height) + 1px);
    line-height: var(--time-ruler-height);
    border-bottom: 0.1rem solid var(--studio-color-border-second);
    display: grid;
    grid-template-rows: var(--time-ruler-height);
    grid-template-columns: var(--left-panel-width) minmax(0, 1fr) var(--zoom-control-width);
    position: relative;
    z-index: 5;
}

.timeruler-space {
    position: relative;
    user-select: none;
}

.timeline-draggable-icon {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    left: -20px;
    top: 0px;
    height: $timeRulerHeight;
    width: 40px;
    font-size: 9px;
    line-height: 11px;
    z-index: 4;
    background: linear-gradient(
        90deg,
        var(--studio-color-transparent) 0%,
        var(--studio-color-white) 50%,
        var(--studio-color-transparent) 100%
    );

    ui-svg-icon {
        background-color: var(--studio-color-background-second);
    }

    &.stop-time {
        z-index: 5;

        .loops {
            border-radius: 1px;
            z-index: 3;
            background: var(--studio-color-second);
            color: var(--studio-color-background-second);
            width: 11px;
            height: 11px;
            text-align: center;
            transition: background-color 0.2s ease;
        }

        &.active .loops {
            background-color: var(--studio-color-text);
        }
    }

    &.preload-frame {
        color: var(--studio-color-text-second);
        transition: color 0.2s ease;

        &.active {
            color: var(--icon-color-active);
        }
    }
}

.zoom {
    position: absolute;
    width: var(--zoom-control-width);
    right: 0;
    bottom: 0;
    z-index: 10000;
    display: grid;
    grid-template-rows: var(--zoom-control-width);
    opacity: 0.3;
    cursor: default;

    &.hidden {
        display: none;
    }

    &.disabled {
        pointer-events: none;
    }

    .slider {
        display: none;
    }

    .zoom-out,
    .zoom-in,
    .magnifier {
        display: flex;
        justify-content: center;
        align-items: center;
        color: var(--studio-color-black);
        font-size: 14px;
        height: 13px;
        width: 100%;
        border-right: 1px solid transparent;
        cursor: pointer;
    }

    .zoom-in {
        margin-top: 1px;
    }

    .magnifier {
        height: 25px;
    }

    .zoom-out,
    .zoom-in {
        display: none;
    }

    &:hover,
    &.zooming {
        opacity: 1;
        background: var(--studio-color-background-second);
        grid-template-rows: auto 20px 20px;
        border-left: 1px solid var(--studio-color-border-second);
        top: var(--zoom-control-width);
        height: auto;
        border-radius: 0;
        box-sizing: border-box;

        .slider {
            display: block;
            padding: 6px 0;
            margin-left: -1px;

            ::ng-deep {
                .vertical {
                    height: 100%;
                }
            }
        }

        .zoom-out,
        .zoom-in {
            display: flex;
            color: var(--studio-color-text-second);
        }

        .magnifier {
            display: none;
        }
    }
}

.toggle-button {
    background: var(--studio-color-background-second);
    border-left: 1px solid var(--studio-color-border-second);
    border-top: 1px solid var(--studio-color-border-second);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    height: 23px;
    z-index: 5;

    &.collapsed {
        ui-svg-icon {
            transform: rotateZ(180deg);
        }
    }

    ui-svg-icon {
        color: var(--studio-color-text-second);
        margin-left: -1px;
    }
}

.playback {
    position: relative;
    z-index: 3;
    display: grid;
    grid-template-columns: 90px minmax(80px, 1fr);

    a {
        cursor: pointer;
        color: blue;
    }

    .controls {
        padding: 0 var(--left-panel-padding);
        display: grid;
        grid-template-columns: auto auto auto;
        justify-items: center;
        align-items: center;
        line-height: $timeRulerHeight;

        ui-svg-icon {
            cursor: pointer;
            color: var(--studio-color-grey-84);

            &:hover:not(.primary):not(.record) {
                color: var(--studio-color-black-off);
            }

            &.primary {
                color: var(--studio-color-primary);
            }
        }

        :host(.keyframes-enabled) & {
            grid-template-columns: auto auto auto auto;
        }

        .record {
            margin-left: 3px;

            &.recording {
                color: var(--studio-color-alert);
            }
        }
    }

    .time {
        text-align: right;
        padding-right: 2px;
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .total-time {
            color: var(--studio-color-text-second);
        }

        ui-input {
            max-width: 39px;
            min-width: 39px;

            & ::ng-deep .input {
                font-weight: 300;
                cursor: default;

                &:focus {
                    cursor: text;
                }
            }
        }

        .time-slash {
            padding: 0 3px;
            color: var(--studio-color-text-second);
        }

        .duration-input ::ng-deep .input {
            color: var(--studio-color-text-second);

            &:focus {
                color: var(--studio-color-black);
            }
        }

        .duration-warning ::ng-deep .input {
            color: var(--studio-color-static-deep-red);

            &:focus {
                color: var(--studio-color-black);
            }
        }

        .seek-input ::ng-deep .input {
            text-align: right;
        }

        ui-input ::ng-deep .input {
            --padding: 3px;
        }
    }
}

.playhead {
    position: absolute;
    top: 0;
    left: -5px;
    width: 10px;
    height: $timeRulerHeight;
    z-index: 10;
    visibility: visible;

    &:not(.active):not(.nohover) {
        &:hover {
            .head {
                background: var(--studio-color-selection);

                &:after {
                    border-top-color: var(--studio-color-primary);
                }
            }

            .line {
                background: var(--studio-color-selection);
            }
        }
    }

    &.hidden {
        visibility: hidden;
    }

    &.active {
        .line {
            box-shadow:
                0 0 0 1px var(--studio-color-transparent-white-50),
                0 0 10px var(--studio-color-transparent-black-10);
            left: 4.5px;
        }

        .head {
            top: -4px;
            height: 27px;
            box-shadow: 0 0 10px var(--studio-color-transparent-black-10);
        }
    }

    &.recording {
        .head,
        .line {
            background: var(--studio-color-alert);
        }

        .head:after {
            border-top-color: var(--studio-color-alert);
        }
    }

    .head {
        position: absolute;
        left: 2.5px;
        top: -2px;
        width: 5px;
        height: calc(var(--time-ruler-height) + 1px);
        background: var(--studio-color-primary);
        z-index: 1;
        border-radius: 1px 1px 0 0;
        box-shadow: 0 0 0 1px var(--studio-color-transparent-white-50);

        &:after {
            content: '';
            bottom: -2px;
            left: 0;
            width: 0;
            height: 0;
            border-left: 2.5px solid transparent;
            border-right: 2.5px solid transparent;
            border-top: 2px solid var(--studio-color-primary);
            position: absolute;
            z-index: 100;
        }
    }

    .line {
        z-index: 0;
        top: 0;
        left: 4.5px;
        width: 1px;
        height: 2000px;
        position: absolute;
        background: var(--studio-color-primary);
        pointer-events: none;
    }
}

.elements-container {
    padding: 0;
    height: calc(100% - var(--time-ruler-height));
    overflow: hidden;
    position: relative;
}

.placeholder {
    position: absolute;
    left: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    color: var(--studio-color-text-second);
    font-size: 11px;
    text-align: center;
    z-index: 4;

    .placeholder-elements {
        width: var(--left-panel-width);
    }

    .placeholder-timeruler {
        width: calc(100% - var(--left-panel-width));
    }
}

.elements {
    height: 100%;
    background: linear-gradient(
        90deg,
        var(--studio-color-transparent) calc(var(--durationLineX) - 1px),
        var(--durationLineColor) calc(var(--durationLineX)),
        var(--studio-color-transparent) calc(var(--durationLineX) + 1px)
    );

    &-inner-wrapper {
        overflow-y: hidden;
        position: relative;
    }
}

timeline-animation {
    width: 100%;
}

.custom-dropdown {
    grid-template-columns: 0 auto 0;

    .custom-item {
        display: inline-grid;
        width: 100%;
        grid-template-columns: 24px auto auto;
        margin: 0;
        align-items: center;

        &:hover {
            .icon {
                color: var(--studio-color-text);
            }
        }

        .custom-column {
            color: var(--studio-color-grey-84);
            justify-self: center;
            margin-right: 0;
            font-size: 14px;
            align-items: center;
            justify-content: center;
            display: flex;

            .icon {
                margin-top: 1px;

                &.active {
                    color: var(--studio-color-text);
                }
            }
        }
    }
}

.timeline-lines {
    position: absolute;
    width: 168px;
    height: 100%;
    top: 0px;
    left: 0px;
    z-index: -1;

    &::before,
    &::after {
        height: 100%;
        position: absolute;
        content: '';
        width: 1px;
        background: var(--studio-color-grey-93);
    }

    &::before {
        left: var(--left-panel-width);
    }

    &::after {
        left: var(--duration-line-position);
    }
}

.gizmo-overlay-canvas {
    position: absolute;
    pointer-events: none;
    width: calc(100% - var(--left-panel-width));
    height: 100%;
    z-index: 3;
    top: 0;
    left: var(--left-panel-width);
}
