$leftPanelWidth: 205px;
$timeRulerHeight: 48px;
:where(:root[data-uinew]) :host {
    --durationLineX: 0px;
    --durationLineColor: transparent;
    --left-panel-width: 205px;
    --left-panel-padding: var(--nui-space-200);
    --name-input-padding: 2px;
    /** Should be synced with TIMELINE_ELEMENT_HEIGHT constant */
    --timeline-row-height: 40px;
    --timeline-name-input-height: 18px;
    --time-ruler-height: 48px; // Note that a 1px border is added onto this
    --zoom-control-width: 40px;
    --icon-width: 20px;
    --feature-count: 0;
    --canvas-padding: 5px; // Enable selections being drawn "outside" canvas
    --kebab-menu-width: 20px;

    position: relative;
    display: block;
    width: 100%;
    height: 260px;
    transition: height 0.2s ease;
    box-sizing: border-box;

    &.resizing {
        transition: none;
    }

    &.collapsed {
        .zoom {
            display: none;
        }
    }

    &.playhead-active {
        cursor: pointer !important;
    }

    &:before {
        content: '';
        width: 1px;
        height: 100%;
        position: absolute;
        left: calc(var(--left-panel-width) - 1px);
        top: 0;
        border-right: 1px solid var(--nui-border-neutral-secondary-bold);
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 47, 0.149);
        clip-path: inset(0 -6px 0 0);
        z-index: 1;
    }

    .dropdown-trigger {
        visibility: hidden;
        position: fixed;
        width: 120px;
    }

    .wrapper {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    .resize-handle {
        left: 0;
        top: -4px;
        right: 0;
        height: 8px;
        z-index: 4;
        position: absolute;
    }

    .top {
        height: var(--time-ruler-height);
        line-height: var(--time-ruler-height);
        border-bottom: 0.1rem solid var(--studio-color-border-second);
        display: grid;
        grid-template-rows: var(--time-ruler-height);
        grid-template-columns: var(--left-panel-width) minmax(0, 1fr) 0;
        position: relative;
        z-index: 5;
    }

    .timeruler-space {
        position: relative;
        user-select: none;
    }

    .timeline-draggable-icon {
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 4;

        &.stop-time {
            top: calc(50% - 10px);
            left: -10px;
            z-index: 5;

            .loops {
                transition: background-color 0.2s ease;
            }

            &.active .loops {
                background-color: var(--studio-color-text);
            }
        }

        &.preload-frame {
            top: 0px;
            height: $timeRulerHeight;
            width: 40px;
            left: -20px;
            background-color: var(--nui-fill-neutral-subtlest);
            transition: color 0.2s ease;

            ui-svg-icon {
                --color: var(--nui-icon-secondary);
            }

            &.active ui-svg-icon {
                --color: var(--nui-icon-primary);
            }
        }
    }

    .zoom {
        position: absolute;
        width: var(--zoom-control-width);
        right: var(--nui-space-100);
        bottom: var(--nui-space-200);
        z-index: 10000;
        cursor: default;
        display: flex;
        justify-content: center;
        align-items: center;

        &.hidden {
            display: none;
        }

        &.disabled {
            pointer-events: none;
        }

        ui-range {
            width: 100%;
            display: flex;
            justify-content: center;
            margin-bottom: var(--nui-space-200);
        }

        .zoom-controls {
            position: absolute;
            bottom: 0;
            right: var(--nui-space-100);
            display: none !important;
            flex-direction: column;
            gap: var(--nui-space-100);
            --nui-card-space-padding-vertical: var(--nui-space-100);
            --nui-card-space-padding-horizontal: var(--nui-space-100);
        }

        .zoom-toggle-icon {
            padding-bottom: var(--nui-space-100);
            padding-right: var(--nui-space-50);
            --color: var(--nui-text-primary);
        }

        &:hover,
        &.zooming {
            .zoom-controls {
                display: flex !important;
            }

            .zoom-toggle-icon {
                display: none;
            }
        }
    }

    .toggle-button {
        z-index: 5;
        margin: var(--nui-space-200);
        background-color: transparent;
        position: absolute;
        right: 0;
    }

    .playback {
        position: relative;
        z-index: 3;
        display: grid;
        grid-template-columns: 112px minmax(93px, 1fr);
        padding-left: var(--nui-space-300);
        background-color: var(--nui-surface-neutral-subtlest);

        a {
            cursor: pointer;
            color: blue;
        }

        .controls {
            padding-right: 0;
            display: flex;
            justify-items: center;
            align-items: center;
            line-height: $timeRulerHeight;
            gap: var(--nui-space-200);

            .record-button {
                margin-left: 3px;

                .recording {
                    --color: var(--nui-button-text-primary-destructive-inverted);
                }
            }
            .play-pause-icon {
                --color: var(--nui-button-fill-primary);
            }
        }

        .time {
            text-align: right;
            padding-right: 2px;
            display: flex;
            justify-content: flex-end;
            align-items: center;

            input,
            .duration-size-span {
                min-width: 39px;
                max-width: 39px;
                appearance: none;
                -moz-appearance: none;
                border: none;
                outline: none;
                font-weight: var(--nui-label-regular-font-weight);
                font-size: var(--nui-label-regular-font-size);
                padding: 0;
                color: var(--nui-text-primary);
                background-color: transparent;

                &::-webkit-outer-spin-button,
                &::-webkit-inner-spin-button {
                    -webkit-appearance: none;
                    margin: 0;
                }
            }

            .time-slash {
                padding: 0 0.5rem;
            }

            .duration-input {
                color: var(--nui-label-text-secondary);

                &:focus {
                    color: var(--nui-label-text-neutral);
                }
            }

            .duration-warning {
                color: var(--nui-label-text-destructive);

                &:focus {
                    color: var(--nui-label-text-neutral);
                }
            }

            .seek-input {
                text-align: right;
            }
        }
    }

    .playhead {
        position: absolute;
        top: 0;
        left: -5px;
        width: 10px;
        height: $timeRulerHeight;
        z-index: 10;
        visibility: visible;
        mix-blend-mode: multiply;

        &:not(.active):not(.nohover):not(.recording) {
            &:hover {
                .head {
                    background: var(--nui-border-brand-primary-boldest-hover);
                }

                .line {
                    background: var(--nui-border-brand-primary-boldest-hover);
                }
            }
        }

        &.hidden {
            visibility: hidden;
        }

        &.active {
            .head {
                top: 0;
                height: 27px;
            }
        }

        &.recording {
            .head,
            .line {
                background: var(--nui-fill-system-danger-bold);
            }
            &:hover {
                .head {
                    background: var(--nui-fill-system-danger-subtle);
                }

                .line {
                    background: var(--nui-fill-system-danger-subtle);
                }
            }
        }

        .head {
            position: absolute;
            left: 3px;
            top: 3px;
            width: 6px;
            height: 24px;
            background: var(--nui-border-brand-primary-boldest);
            z-index: 1;
            border-radius: var(--nui-border-radius-medium);
            transition:
                top 0.2s ease,
                height 0.2s ease;
        }

        .line {
            z-index: 0;
            top: 3px;
            left: 4.5px;
            width: 2px;
            height: 2000px;
            position: absolute;
            background: var(--nui-border-brand-primary-boldest);
            pointer-events: none;
        }
    }

    .elements-container {
        padding: 0;
        height: calc(100% - var(--time-ruler-height));
        overflow: hidden;
        position: relative;
    }

    .placeholder {
        position: absolute;
        left: 0;
        bottom: 0;
        height: 100%;
        width: 100%;
        display: flex;
        align-items: center;
        color: var(--studio-color-text-second);
        font-size: 11px;
        text-align: center;
        z-index: 4;

        .placeholder-elements {
            width: var(--left-panel-width);
            justify-content: center;
        }

        .placeholder-timeruler {
            display: flex;
            justify-content: center;
            align-items: center;
            width: calc(100% - var(--left-panel-width));
            height: 100%;
            background-color: var(--nui-fill-brand-secondary-subtler);
            content: '';
        }
    }

    .elements {
        height: 100%;

        &-inner-wrapper {
            overflow-y: hidden;
            position: relative;
        }
    }

    timeline-animation {
        width: 100%;
    }

    .gizmo-overlay-canvas {
        position: absolute;
        pointer-events: none;
        width: calc(100% - var(--left-panel-width));
        height: 100%;
        z-index: 5;
        top: 0;
        left: var(--left-panel-width);
    }
}
