import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { NO_ERRORS_SCHEMA, QueryList } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { provideRouter } from '@angular/router';
import { provideFeatureFlags } from '@bannerflow/feature-flags';
import { UIModule } from '@bannerflow/ui';
import { IRenderer } from '@domain/creative/renderer.header';
import { createRendererFixture } from '@fixtures/renderer.fixture';
import { FixNavigationTriggeredOutsideAngularZoneNgModule } from '@mocks/routing-module';
import { createFontFamiliesServiceMock } from '@mocks/services/font-families-service.mock';
import { createUserServiceMock } from '@mocks/services/user-service.mock';
import { createUserSettingsServiceMock } from '@mocks/services/user-settings-service.mock';
import { createVersionServiceMock } from '@mocks/services/versions-service.mock';
import { createMockWorkspaceTransformService } from '@mocks/services/workspace-transform-service.mock';
import { createMockSnapshot } from '@mocks/snapshot.mock';
import { ISharedSettings } from '@studio/common';
import { BrandService } from '@studio/common/brand/brand.service';
import { provideEnvironment } from '@studio/common/environment/environment.provider';
import { FontFamiliesService } from '@studio/common/font-families/font-families.service';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { UserSettingsService } from '@studio/common/user-settings/user-settings.service';
import { UserService } from '@studio/common/user/user.service';
import { ApolloTestingModule } from 'apollo-angular/testing';
import { EMPTY, Observable, of } from 'rxjs';
import { environment } from '../../../environments/environment.test';
import { MediaLibraryService } from '../../shared/media-library/state/media-library.service';
import { defineMatchMedia } from '../../shared/mocks/matchMedia.mock';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../shared/mocks/store.mock';
import { VersionsService } from '../../shared/versions/state/versions.service';
import { DesignViewComponent } from './design-view.component';
import { SERVICES } from './design-view.services';
import { DraggableContainerDirective } from './draggable-container.directive';
import { StudioTimelineComponent } from './timeline/studio-timeline/studio-timeline.component';
import { StudioWorkspaceComponent } from './workspace/studio-workspace.component';
import { ZoomControlService } from './workspace/zoom-control/zoom-control.service';

describe('DesignView Component', () => {
    let component: DesignViewComponent;
    let fixture: ComponentFixture<DesignViewComponent>;
    let renderer: IRenderer;

    beforeEach(async () => {
        defineMatchMedia();
        TestBed.overrideComponent(DesignViewComponent, {
            set: {
                providers: [
                    SERVICES,
                    {
                        provide: MediaLibraryService,
                        useValue: {
                            isOpen$: of(false),
                            isEditingElement$: of(false),
                            openMediaLibrary: jest.fn(),
                            setIsEditingElement: jest.fn(),
                            init: jest.fn()
                        }
                    }
                ]
            }
        });
        await TestBed.configureTestingModule({
            declarations: [DesignViewComponent],
            imports: [
                UIModule,
                FixNavigationTriggeredOutsideAngularZoneNgModule,
                ApolloTestingModule,
                NoopAnimationsModule
            ],
            providers: [
                provideHttpClient(),
                provideHttpClientTesting(),
                provideRouter([]),
                provideEnvironment(environment),
                {
                    provide: BrandService,
                    useValue: {
                        loadBrandPalettes: (): Observable<never> => EMPTY,
                        localizations$: of([]),
                        error$: of(undefined),
                        brandId$: of('123'),
                        accountSlug$: of('accountSlug')
                    }
                },
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER,
                {
                    provide: ZoomControlService,
                    useValue: {
                        changeZoomObservable: of()
                    }
                },
                {
                    provide: EnvironmentService,
                    useValue: {
                        setPage: jest.fn(),
                        origins: environment.origins
                    } as Partial<EnvironmentService>
                },
                {
                    provide: FontFamiliesService,
                    useValue: createFontFamiliesServiceMock()
                },
                {
                    provide: UserSettingsService,
                    useValue: createUserSettingsServiceMock({
                        sharedSettings$: of({} as ISharedSettings),
                        setSharedSetting: jest.fn()
                    })
                },
                {
                    provide: UserService,
                    useValue: createUserServiceMock()
                },
                {
                    provide: VersionsService,
                    useValue: createVersionServiceMock()
                },
                provideFeatureFlags({
                    enabled: false
                })
            ],
            schemas: [NO_ERRORS_SCHEMA]
        }).compileComponents();
        fixture = TestBed.createComponent(DesignViewComponent);
        component = fixture.componentInstance;
        component['draggableContainer'] = {} as DraggableContainerDirective;
        component.isMediaLibraryOpen = true;
        renderer = createRendererFixture(300, 250, []);

        component.renderer = renderer;
        component.initCreative = jest.fn();
        component['createAd'] = jest.fn();

        fixture.detectChanges();
    });

    it('should create component', () => {
        expect(component).toBeTruthy();
    });

    it('should update properties from snapshot', () => {
        component.workspace = {
            transform: createMockWorkspaceTransformService(),
            gizmoDrawer: {
                draw: jest.fn() as any
            }
        } as StudioWorkspaceComponent;
        component.timeline = {
            timelineElementComponents: new QueryList()
        } as StudioTimelineComponent;

        const snapshot = createMockSnapshot();
        component['useSnapshot'](snapshot);
        const upsertVersionsSpy = jest.spyOn(component['versionsService'], 'upsertVersionProperty');
        expect(upsertVersionsSpy).toHaveBeenCalled();
    });
});
