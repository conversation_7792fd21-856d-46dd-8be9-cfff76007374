import { isVideoStreamable } from './streaming-validation';
import { VideoElementFixture } from '@fixtures/video-element.fixture';
import { cloneDeep } from '@studio/utils/clone';

describe('isVideoStreamable', () => {
    it('should return false for a video element with a video asset height of 63px', () => {
        const videoFixture = cloneDeep(VideoElementFixture);
        videoFixture.videoAsset!.height = 63;
        videoFixture.height = 100;

        expect(isVideoStreamable(videoFixture)).toBe(false);
    });
});
