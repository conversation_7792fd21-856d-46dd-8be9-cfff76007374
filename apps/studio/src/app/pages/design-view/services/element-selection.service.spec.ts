import { ElementSelectionService } from './element-selection.service';
import { createDataNodeMock } from '@mocks/element.mock';

describe('Element selection service', () => {
    let elementSelectionService: ElementSelectionService;
    const mockedElement = createDataNodeMock({ id: 'Scooby' });
    const secondElement = createDataNodeMock({ id: 'Shaggy' });

    beforeEach(() => {
        elementSelectionService = new ElementSelectionService();
        elementSelectionService.change$.subscribe();
    });

    it('setSelection should set the current selection', () => {
        elementSelectionService.setSelection(mockedElement, secondElement);
        expect(elementSelectionService.currentSelection.elements).toEqual([
            mockedElement,
            secondElement
        ]);
    });

    it('clearSelection should clear the current selection', () => {
        elementSelectionService.setSelection(mockedElement, secondElement);
        elementSelectionService.clearSelection();
        expect(elementSelectionService.currentSelection.elements).toEqual([]);
    });

    it('deleteSelection should delete the elements passed in from the current selection', () => {
        elementSelectionService.setSelection(mockedElement, secondElement);
        elementSelectionService.deleteSelection(secondElement);
        expect(elementSelectionService.currentSelection.elements).toEqual([mockedElement]);
    });

    it('addSelection should add the elements passed in to the current selection', () => {
        elementSelectionService.setSelection(mockedElement);
        elementSelectionService.addSelection(secondElement);
        expect(elementSelectionService.currentSelection.elements).toEqual([
            mockedElement,
            secondElement
        ]);
    });

    it('should only emit one time when element ids does not differs', () => {
        const next = jest.fn();
        elementSelectionService.change$.subscribe(next);

        elementSelectionService.setSelection(mockedElement, secondElement);
        elementSelectionService.setSelection(mockedElement, secondElement);

        expect(next).toHaveBeenCalledTimes(1);
    });

    it('should emit every time element ids differs', () => {
        const next = jest.fn();
        elementSelectionService.change$.subscribe(next);

        elementSelectionService.setSelection(mockedElement, secondElement);
        elementSelectionService.setSelection(
            createDataNodeMock({ id: 'Scrappy' }),
            createDataNodeMock({ id: 'Velma' })
        );

        expect(next).toHaveBeenCalledTimes(2);
    });
});
