import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { cloneDeep } from '@apollo/client/utilities';
import { ITextCreationOptions } from '@domain/creativeset';
import { ElementKind } from '@domain/elements';
import { IButtonElementDataNode, ITextElementDataNode } from '@domain/nodes';
import { createMockCreativeSize } from '@mocks/creative/creative-size.mock';
import { createMockCreativesetDataService } from '@mocks/creativeset.mock';
import { createDataNodeMock, createElementMock, createElementPropertyMock } from '@mocks/element.mock';
import { createFontFamiliesServiceMock } from '@mocks/services/font-families-service.mock';
import { createVersionServiceMock } from '@mocks/services/versions-service.mock';
import { CreativesetDataService, UserService } from '@studio/common';
import { BrandService } from '@studio/common/brand';
import { provideEnvironment } from '@studio/common/environment/environment.provider';
import { FontFamiliesService } from '@studio/common/font-families';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { ApolloTestingModule } from 'apollo-angular/testing';
import { of } from 'rxjs';
import { environment } from '../../../../environments/environment.test';
import { defineMatchMedia } from '../../../shared/mocks/matchMedia.mock';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../../shared/mocks/store.mock';
import { NodeCreatorService } from '../../../shared/services/data-node-creator.service';
import { VersionsService } from '../../../shared/versions/state/versions.service';
import { EditorStateService } from './editor-state.service';
import { ElementCreatorService } from './element-creator.service';
import { createMockTextCreationOptions } from './mocks/text-creation-options.mock';
import { provideFeatureFlags } from '@bannerflow/feature-flags';

describe('ElementCreatorService', () => {
    let elementCreatorService: ElementCreatorService;
    let editorStateService: EditorStateService;

    beforeAll(() => {
        defineMatchMedia();
    });

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [ApolloTestingModule],
            providers: [
                provideHttpClient(),
                provideHttpClientTesting(),
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER,
                provideEnvironment(environment),
                EnvironmentService,
                ElementCreatorService,
                BrandService,
                EditorStateService,
                NodeCreatorService,
                {
                    provide: UserService,
                    useValue: {
                        isEmployee$: of(false)
                    }
                },
                { provide: CreativesetDataService, useValue: createMockCreativesetDataService() },
                {
                    provide: FontFamiliesService,
                    useValue: createFontFamiliesServiceMock()
                },
                {
                    provide: VersionsService,
                    useValue: createVersionServiceMock()
                },
                provideFeatureFlags({
                    enabled: false
                })
            ]
        });

        elementCreatorService = TestBed.inject(ElementCreatorService);
        editorStateService = TestBed.inject(EditorStateService);
        editorStateService.size = createMockCreativeSize();
    });

    it('should be created', () => {
        expect(elementCreatorService).toBeTruthy();
    });

    describe('createWidget', () => {
        it('should not modify original base element data', () => {
            const properties = [
                createElementPropertyMock({ name: 'html', value: 'html' }),
                createElementPropertyMock({ name: 'css', value: 'css' }),
                createElementPropertyMock({ name: 'js', value: 'js' }),
                createElementPropertyMock({ name: 'ts', value: 'ts' })
            ];
            const mockElement = createElementMock(ElementKind.BannerflowLibraryWidget, { properties });
            const mockWidget = createDataNodeMock({ kind: ElementKind.Widget });

            const options = { element: mockElement };
            const originalWidget = cloneDeep(mockWidget);

            elementCreatorService.createWidget(mockWidget, options);

            expect(mockWidget.customProperties[0].value).toEqual('');
            expect(mockWidget.customProperties[0].name).toEqual(
                originalWidget.customProperties[0].name
            );
            expect(mockWidget.customProperties[0].unit).toEqual(
                originalWidget.customProperties[0].unit
            );
            expect(mockWidget.customProperties[0].versionPropertyId).toBeTruthy();

            expect(mockWidget.customProperties[1].value).toEqual('');
            expect(mockWidget.customProperties[1].name).toEqual(
                originalWidget.customProperties[1].name
            );
            expect(mockWidget.customProperties[1].unit).toEqual(
                originalWidget.customProperties[1].unit
            );
            expect(mockWidget.customProperties[1].versionPropertyId).toBeTruthy();
        });

        it('should not re-add properties to selected version when skipEmit is true', () => {
            const spy = jest.spyOn(editorStateService, 'propertyAsVersionableProperty');
            const properties = [
                createElementPropertyMock({ name: 'html', value: 'html' }),
                createElementPropertyMock({ name: 'css', value: 'css' }),
                createElementPropertyMock({ name: 'js', value: 'js' }),
                createElementPropertyMock({ name: 'ts', value: 'ts' }),
                createElementPropertyMock({ name: 'custom:text', value: '', unit: 'text' }),
                createElementPropertyMock({ name: 'custom:feed', value: undefined, unit: 'feed' })
            ];

            const mockElement = createElementMock(ElementKind.BannerflowLibraryWidget, { properties });
            const mockWidget = createDataNodeMock({ kind: ElementKind.Widget });
            const options = { element: mockElement, skipEmit: true };

            elementCreatorService.createWidget(mockWidget, options);

            expect(spy).not.toHaveBeenCalled();
        });
    });

    describe('createText', () => {
        let mockData: ITextElementDataNode;
        let mockOptions: ITextCreationOptions;

        beforeEach(() => {
            mockData = createDataNodeMock({
                kind: ElementKind.Text,
                fontSize: 80
            });
            mockOptions = createMockTextCreationOptions();
        });

        it('should create a text element with scaled values', () => {
            const result = elementCreatorService.createText(mockData, mockOptions);
            expect(result.kind).toEqual('text');
            expect(result.width).toEqual(mockData.width);
            expect(result.height).toEqual(mockData.height);
            expect(result.fontSize).toEqual(30);
        });

        it('should not scale text element when clickedOut', () => {
            mockOptions.clickedOut = true;
            const result = elementCreatorService.createText(mockData, mockOptions);
            expect(result.kind).toEqual('text');
            expect(result.width).toEqual(mockData.width);
            expect(result.height).toEqual(mockData.height);
            expect(result.fontSize).toEqual(mockData.fontSize);
        });

        it('should not scale text element from brand library', () => {
            mockOptions.shouldRescale = false;
            const result = elementCreatorService.createText(mockData, mockOptions);
            expect(result.kind).toEqual('text');
            expect(result.width).toEqual(mockData.width);
            expect(result.height).toEqual(mockData.height);
            expect(result.fontSize).toEqual(mockData.fontSize);
        });

        it('should scale text element when creativeSize width is greater than 500', () => {
            editorStateService.size.width = 600;
            const result = elementCreatorService.createText(mockData, mockOptions);
            expect(result.kind).toEqual('text');
            expect(result.width).toEqual(mockData.width);
            expect(result.height).toEqual(mockData.height);
            expect(result.fontSize).toEqual(30);
        });

        it('should not scale text element from brand library and adjust width and height when the creative size width is greater than 600px and clicked out', () => {
            editorStateService.size.width = 600;
            mockOptions.clickedOut = true;
            mockOptions.shouldRescale = false;
            const result = elementCreatorService.createText(mockData, mockOptions);
            expect(result.kind).toEqual('text');
            expect(result.width).toEqual(210);
            expect(result.height).toEqual(140);
            expect(result.fontSize).toEqual(mockData.fontSize);
        });

        it('should not scale text element from brand library when the creative size width is greater than 600px', () => {
            editorStateService.size.width = 600;
            mockOptions.clickedOut = false;
            mockOptions.shouldRescale = false;
            const result = elementCreatorService.createText(mockData, mockOptions);
            expect(result.kind).toEqual('text');
            expect(result.width).toEqual(mockData.width);
            expect(result.height).toEqual(mockData.height);
            expect(result.fontSize).toEqual(mockData.fontSize);
        });
    });

    describe('createButton', () => {
        let mockData: IButtonElementDataNode;
        let mockOptions: ITextCreationOptions;

        beforeEach(() => {
            mockData = createDataNodeMock({
                kind: ElementKind.Button,
                fontSize: 80
            });
            mockOptions = createMockTextCreationOptions();
        });

        it('should create a button element', () => {
            const result = elementCreatorService.createButton(mockData, mockOptions);
            expect(result.kind).toEqual('button');
            expect(result.width).toEqual(mockData.width);
            expect(result.height).toEqual(mockData.height);
            expect(result.fontSize).toEqual(mockData.fontSize);
        });

        it('should not scale button element from brand library', () => {
            mockOptions.shouldRescale = false;
            const result = elementCreatorService.createButton(mockData, mockOptions);
            expect(result.kind).toEqual('button');
            expect(result.fontSize).toEqual(mockData.fontSize);
        });

        it('should scale button element from brand library when the creative size width is greater than 500px', () => {
            editorStateService.size.width = 600;
            mockOptions.clickedOut = true;
            mockOptions.shouldRescale = false;
            const result = elementCreatorService.createButton(mockData, mockOptions);
            expect(result.kind).toEqual('button');
            expect(result.fontSize).toEqual(mockData.fontSize);
        });
    });
});
