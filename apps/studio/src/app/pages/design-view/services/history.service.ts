import { Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SentinelService } from '@bannerflow/sentinel';
import { Logger } from '@bannerflow/sentinel-logger';
import { hasSameStyle, isContentSpan, isVariableSpan } from '@creative/elements/rich-text/utils';
import { cloneNodes } from '@creative/nodes/data-node.utils';
import { isState, isTextDataElement, isTextNode } from '@creative/nodes/helpers';
import { ElementSelection } from '@creative/nodes/selection';
import { getStateById } from '@creative/rendering';
import { cloneCreativeDocument, stringifyCreativeDataNode } from '@creative/serialization/index';
import { serializeStyle } from '@creative/serialization/text-serializer';
import { serializeVersions } from '@creative/serialization/versions/version-serializer';
import { getGlobalElementsFromCreativeDataNode } from '@data/deserialization/design-api/sapi-conversion-helpers';
import { IAnimationKeyframe } from '@domain/animation';
import { ISerializedVersion, IVersion } from '@domain/creativeset/version';
import { IFontFamily } from '@domain/font-families';
import { ICreativeDataNode, OneOfTextViewElements } from '@domain/nodes';
import { ITextSelection } from '@domain/rich-text/rich-text.selection.header';
import { IState } from '@domain/state';
import { IVariableSpan } from '@domain/text';
import { IGuideline } from '@domain/workspace';
import { concatLatestFrom } from '@ngrx/operators';
import { CreativesetDataService } from '@studio/common';
import { cloneDeep, simpleClone } from '@studio/utils/clone';
import { HistoryQueue } from '@studio/utils/history-queue';
import { deepEqual, deepSortObject } from '@studio/utils/utils';
import { Subject, filter, tap } from 'rxjs';
import { VersionsService } from '../../../shared/versions/state/versions.service';
import { DesignViewComponent } from '../design-view.component';
import { PropertiesService } from '../properties-panel/properties.service';
import { KeyframeService } from '../timeline';
import { EditorSaveStateService } from './editor-save-state.service';
import { EditorStateService } from './editor-state.service';
import { ElementSelectionService, InteractableInstance } from './element-selection.service';

export type IEditorSnapshot = {
    creativeDataNode: ICreativeDataNode;
    selection: ElementSelection | undefined;
    latestSelectionType: InteractableInstance;
    textSelection: ITextSelection | undefined;
    versions: IVersion[];
    selectedVersionId: string;
    defaultVersionId: string | undefined;
    activeGuideline: IGuideline | undefined;
    activeState: IState | undefined;
    keyframeSelection: { keyframes: IAnimationKeyframe[]; states: IState[] } | undefined;
    expandedAnimationElements: string[];
    fontFamilies?: IFontFamily[];
};

export interface ISaveSnapshotOption {
    isVersionable?: boolean;
    hasChanged?: boolean;
}

interface IBackendStateData {
    designId: string;
    creativeDataNode: ICreativeDataNode;
}

export interface IBackendState {
    /** Current design state */
    current: IBackendStateData;
    /** All other design states (current excluded) */
    others: IBackendStateData[];
}

@Injectable()
export class HistoryService {
    private _onChange$ = new Subject<{
        undos: ReadonlyArray<IEditorSnapshot>;
        redos: ReadonlyArray<IEditorSnapshot>;
    }>();
    onChange$ = this._onChange$.asObservable();
    undo$ = new Subject<void>();
    redo$ = new Subject<void>();
    private _onDirtyChange$ = new Subject<boolean>();
    onDirtyChange$ = this._onDirtyChange$.asObservable();
    private _snapshotApply$ = new Subject<void>();
    snapshotApply$ = this._snapshotApply$.asObservable();
    private _backendState$ = new Subject<IBackendState>();
    backendState$ = this._backendState$
        .asObservable()
        .pipe(tap(backendState => (this.backendState = backendState)));

    editor: DesignViewComponent;
    isApplyingSnapshot = false;

    private historyQueue = new HistoryQueue<IEditorSnapshot>();
    private snapshotCandidates: IEditorSnapshot[] = [];

    private lastDirtyCheck = false;

    // Backend state is only used for keeping track of dirty designs during save.
    backendState: IBackendState;

    private versionsBackendState: ISerializedVersion[];

    private selectedVersion?: IVersion;
    private defaultVersion: IVersion;
    private versions: IVersion[];

    private logger = new Logger('HistoryService');

    constructor(
        private editorSaveStateService: EditorSaveStateService,
        private editorStateService: EditorStateService,
        private elementSelectionService: ElementSelectionService,
        private keyframeService: KeyframeService,
        private propertiesService: PropertiesService,
        private sentinelService: SentinelService,
        private versionsService: VersionsService,
        private creativesetDataService: CreativesetDataService
    ) {
        this.versionsService.loaded$
            .pipe(
                takeUntilDestroyed(),
                filter(Boolean),
                concatLatestFrom(() => this.versionsService.versions$)
            )
            .subscribe(([_, versions]) => {
                this.versionsBackendState = serializeVersions(
                    versions,
                    this.sentinelService
                ) as ISerializedVersion[];
            });

        this.versionsService.selectedVersion$.pipe(takeUntilDestroyed()).subscribe(selectedVersion => {
            this.selectedVersion = selectedVersion;
        });

        this.versionsService.defaultVersion$.pipe(takeUntilDestroyed()).subscribe(defaultVersion => {
            this.defaultVersion = defaultVersion;
        });

        this.versionsService.versions$.pipe(takeUntilDestroyed()).subscribe(versions => {
            this.versions = versions;
        });

        this.editorSaveStateService.saveSuccess$.pipe(takeUntilDestroyed()).subscribe(() => {
            this.storeCurrentStateAsBackendState();
            this.clear();
        });
    }

    public popUndo(): IEditorSnapshot | undefined {
        this.logger.verbose('Popping undo stack');
        const value = this.historyQueue.popUndo();
        // Keep the cloneDeep here so no pesky programmer starts mutating the undo stack
        return cloneDeep(value);
    }

    public popRedo(): IEditorSnapshot | undefined {
        this.logger.verbose('Popping redo stack');
        const value = this.historyQueue.popRedo();
        // Keep the cloneDeep here so no pesky programmer starts mutating the redo stack
        return cloneDeep(value);
    }

    addSnapshot(): void {
        this.logger.verbose('Adding snapshot');

        this.createSnapshotCandidate();
        this.saveLastSnapshotCandidate();
    }

    createSnapshotCandidate = (): void => {
        this.logger.verbose('Creating snapshot candidate');

        const snapshot = this.createSnapshot();
        this.snapshotCandidates.push(snapshot);
    };

    saveLastSnapshotCandidate = (): void => {
        const snapshot = this.snapshotCandidates.pop();
        if (snapshot) {
            this.saveSnapshot(snapshot);
        }
        this.checkDirtiness();

        this.disposeSnapshotCandidates();
    };

    private saveSnapshot(snapshot: IEditorSnapshot): void {
        this.logger.verbose('Saving snapshot');
        this.historyQueue.push(snapshot);
    }

    isClean = (): boolean => !this.isDirty();

    isDirty = (): boolean => {
        const backendState = stringifyCreativeDataNode(this.backendState.current.creativeDataNode);
        const editorState = stringifyCreativeDataNode(this.editorStateService.creativeDataNode);
        const documentIsDirty = backendState !== editorState;

        if (documentIsDirty) {
            return true;
        }

        for (const element of this.editorStateService.creativeDataNode.elements) {
            if (isTextDataElement(element)) {
                if (!element.__dirtyContent) {
                    continue;
                }
                if (element.__dirtyContent.spans.length !== element.content.spans.length) {
                    return true;
                }

                const dirtyStyle = element.__dirtyContent.style;
                const cleanStyle = element.content.style;

                if (serializeStyle(dirtyStyle) !== serializeStyle(cleanStyle)) {
                    return true;
                }

                const dirtySpans = element.__dirtyContent.spans;
                const cleanSpans = element.content.spans;

                for (let i = 0; i < dirtySpans.length; i++) {
                    const dirtySpan = dirtySpans[i];
                    const cleanSpan = cleanSpans[i];
                    const isDifferentType = dirtySpan.type !== cleanSpan.type;
                    const isDifferentContent = dirtySpan.content !== cleanSpan.content;

                    if (isDifferentType || isDifferentContent) {
                        return true;
                    }

                    const bothAreContentSpans = isContentSpan(dirtySpan) && isContentSpan(cleanSpan);
                    const bothAreVariableSpans = isVariableSpan(dirtySpan) && isVariableSpan(cleanSpan);

                    if (bothAreVariableSpans && !this.spansHasSameFeed(dirtySpan, cleanSpan)) {
                        return true;
                    }

                    if (bothAreContentSpans && !hasSameStyle(dirtySpan.style, cleanSpan.style)) {
                        return true;
                    }
                }
            }
        }

        const serializedCurrentElements = getGlobalElementsFromCreativeDataNode(
            this.editorStateService.creativeDataNode
        );
        const elementsIsDirty = !deepEqual(
            serializedCurrentElements,
            getGlobalElementsFromCreativeDataNode(this.backendState.current.creativeDataNode)
        );

        if (elementsIsDirty) {
            return true;
        }

        const versionIsDirty = this.isVersionDirty();
        if (versionIsDirty) {
            return true;
        }

        return false;
    };

    private spansHasSameFeed(dirtySpan: IVariableSpan, cleanSpan: IVariableSpan): boolean {
        const dirtyVariable = dirtySpan.style.variable;
        const cleanVariable = cleanSpan.style.variable;

        if (!dirtyVariable && cleanVariable !== dirtyVariable) {
            return false;
        }

        const sameStart = dirtyVariable?.step.start === cleanVariable?.step.start;
        const sameSize = dirtyVariable?.step.size === cleanVariable?.step.size;

        return sameStart && sameSize;
    }

    checkDirtiness(): boolean {
        const status = this.isDirty();
        if (status !== this.lastDirtyCheck) {
            this.lastDirtyCheck = status;
            this.emitDirtyChange(this.lastDirtyCheck);
        }
        this.notifyChange();
        return status;
    }

    emitSnapshotChange(): void {
        this._snapshotApply$.next();
    }

    private emitDirtyChange(isDirty: boolean): void {
        this._onDirtyChange$.next(isDirty);
    }

    isPristine = (): boolean => !this.isDirty();

    storeCurrentStateAsBackendState(): void {
        const creativeDataNode = cloneCreativeDocument(this.editorStateService.creativeDataNode);
        const otherDesigns = this.creativesetDataService.creativeset.designs.filter(
            ({ id }) => id !== this.editorStateService.designId
        );

        const backendState: IBackendState = {
            current: {
                creativeDataNode: creativeDataNode,
                designId: this.editorStateService.designId
            },
            others: cloneDeep(otherDesigns).map(({ document, id }) => ({
                designId: id,
                creativeDataNode: document
            }))
        };

        this.versionsBackendState = serializeVersions(
            this.versions,
            this.sentinelService
        ) as ISerializedVersion[];

        if (this.lastDirtyCheck === true) {
            this.emitDirtyChange(false);
        }

        this.lastDirtyCheck = false;

        this.backendState = backendState;
        this._backendState$.next(backendState);
    }

    disposeSnapshotCandidates(): void {
        this.snapshotCandidates = [];
        this.historyQueue.clearRedos();
    }

    clear(): void {
        this.logger.verbose('Clearing history');
        this.disposeSnapshotCandidates();
        this.historyQueue.clearUndos();
        this.notifyChange();
    }

    createSnapshot(): IEditorSnapshot {
        const activeGuideline = this.editor.workspace.transform.onGuidelineChange$.getValue();
        const selection = this.elementSelectionService.currentSelection;
        const selectedKeyframes = this.keyframeService.keyframes;
        const selectedStates = this.getStatesFromKeyframes([...selectedKeyframes]);
        const activeState = isState(this.propertiesService.stateData)
            ? { ...this.propertiesService.stateData }
            : undefined;

        const expandedAnimationElements: string[] = [];

        this.editor.timeline?.timelineElementComponents.forEach(c => {
            if (c.expanded) {
                expandedAnimationElements.push(c.node.id);
            }
        });

        const element = selection?.element;
        const viewElement =
            element &&
            this.editorStateService.renderer.getViewElementById<OneOfTextViewElements>(element.id);

        const snapshot: IEditorSnapshot = {
            creativeDataNode: cloneCreativeDocument(this.editorStateService.creativeDataNode, true),
            selection: new ElementSelection(cloneNodes(selection.nodes)),
            latestSelectionType: this.elementSelectionService.latestSelectionType,
            textSelection: isTextNode(selection?.element)
                ? viewElement?.__richTextRenderer?.editor_m?.selection.getTextSelection()
                : undefined,
            versions: cloneDeep(this.versions),
            selectedVersionId: this.selectedVersion!.id,
            defaultVersionId: this.defaultVersion.id,
            activeGuideline: activeGuideline ? simpleClone(activeGuideline) : activeGuideline,
            activeState,
            keyframeSelection: {
                keyframes: simpleClone(Array.from(selectedKeyframes)),
                states: cloneDeep(Array.from(selectedStates))
            },
            expandedAnimationElements
        };

        snapshot.selection = new ElementSelection(snapshot.selection?.nodesAsSortedArray());

        return snapshot;
    }

    private getStatesFromKeyframes(keyframes: IAnimationKeyframe[]): IState[] {
        const states = keyframes.reduce((accumulator: IState[], value) => {
            this.editor.renderer.creativeDocument.elements.forEach(el => {
                const state = getStateById(el, value.stateId);
                if (state) {
                    accumulator.push(state);
                }
            });
            return accumulator;
        }, []);
        return states;
    }

    private isVersionDirty(): boolean {
        // Remove view attributes
        const cleanVersionsArray = serializeVersions(this.versions, this.sentinelService);

        // Avoid falsely dirtying design because of different ordering
        const sortedVersions = deepSortObject(cleanVersionsArray);
        const sortedVersionsBackendState = deepSortObject(this.versionsBackendState);

        return !deepEqual(sortedVersions, sortedVersionsBackendState);
    }

    getAllSnapshots(): IEditorSnapshot[] {
        const currentSnapshot = this.historyQueue.current ? [this.historyQueue.current] : [];
        const snapshots = [...this.historyQueue.undos, ...this.historyQueue.redos, ...currentSnapshot];
        const validSnapshots = snapshots.filter(Boolean);

        if (validSnapshots.length < snapshots.length) {
            this.logger.warn('Found invalid snapshots! Filtering them!');
        }

        return validSnapshots;
    }

    private notifyChange(): void {
        this._onChange$.next({ undos: this.historyQueue.undos, redos: this.historyQueue.redos });
    }
}
