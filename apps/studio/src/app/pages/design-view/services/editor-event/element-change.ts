import { isGroupDataNode } from '@creative/nodes/helpers';
import { Changes, IElementChange, IFilterOptions } from '@domain/element-change';
import { AllDataNodes, OneOfDataNodes, OneOfElementPropertyKeys } from '@domain/nodes';
import { cloneDeep } from '@studio/utils/clone';
import { deepEqual } from '@studio/utils/utils';
import { interval, Observable, of, Subject } from 'rxjs';
import { debounce, debounceTime, filter, switchMap, tap } from 'rxjs/operators';

export enum ElementChangeType {
    /** Adds to snapshot history */
    Instant = 'instant',
    /** Skips adding to snapshot history */
    Skip = 'skip',
    /** Always add to snapshot history, even if element has not changed */
    Force = 'force',
    /** Debounce value change */
    Burst = 'burst'
}

interface IElementsChange {
    elements: ReadonlyArray<IElementChange>;
    type: ElementChangeType;
}

export class ElementChanges {
    private _elementChange$ = new Subject<IElementChange>();
    change$ = this._elementChange$.asObservable();
    private _immediateElementChange$ = new Subject<IElementChange>();
    immediateChange$ = this._immediateElementChange$.asObservable();
    private _elementsChange$ = new Subject<IElementsChange>();
    changes$ = this._elementsChange$.asObservable().pipe(
        debounceTime(50),
        switchMap(changes =>
            of(changes).pipe(
                tap(_changes => {
                    for (const elementChange of _changes.elements) {
                        this._elementChange$.next({
                            element: elementChange.element,
                            changes: elementChange.changes
                        });
                    }
                }),
                tap(() => this.elementChangesMap.clear())
            )
        )
    );

    private _valuesChange$ = new Subject<{
        element: OneOfDataNodes;
        values: Partial<OneOfDataNodes>;
        type: ElementChangeType;
    }>();
    private dataElementMap = new Map<string, OneOfDataNodes>();
    private elementChangesMap = new Map<string, IElementChange>();
    private suspended: boolean;

    constructor() {
        this._valuesChange$
            .pipe(filter(() => !this.suspended))
            .subscribe(({ element, values, type }) => {
                const keys = Object.keys(values);
                let elementCached = true;

                if (!keys.length && type !== ElementChangeType.Force) {
                    return;
                }

                if (!this.dataElementMap.has(element.id)) {
                    this.dataElementMap.set(element.id, cloneDeep(element));
                    elementCached = false;
                }

                const dataElement = this.dataElementMap.get(element.id)!;

                const changedKeys = keys.filter(key => !deepEqual(dataElement[key], values[key]));

                if (!elementCached || changedKeys.length || type === ElementChangeType.Force) {
                    if (!this.elementChangesMap.has(element.id)) {
                        this.elementChangesMap.set(element.id, {
                            element,
                            changes: {
                                ...values
                            } as AllDataNodes
                        });
                    }
                    const changedElement = this.elementChangesMap.get(element.id)!;

                    changedKeys.forEach(
                        key => (changedElement.changes[key] = dataElement[key] = cloneDeep(values[key]))
                    );

                    this._immediateElementChange$.next({ element, changes: changedElement.changes });

                    const aggregatedElements: Omit<IElementChange, 'type'>[] = [];
                    this.elementChangesMap.forEach(value => {
                        aggregatedElements.push(value);
                    });

                    this._elementsChange$.next({ elements: aggregatedElements, type });
                }
            });
    }

    change<Element extends OneOfDataNodes>(
        element: OneOfDataNodes,
        values: Partial<Element>,
        type: ElementChangeType = ElementChangeType.Instant
    ): void {
        this._valuesChange$.next({ element, values: values || {}, type });
    }

    clear(): void {
        this.dataElementMap.clear();
    }

    /** Suspend the service until restored */
    suspend(): void {
        this.suspended = true;
    }

    /** Restores the service from suspension */
    restore(): void {
        this.suspended = false;
    }
}

function explicitAnyChangesFilter(changes: Changes, properties: OneOfElementPropertyKeys[]): boolean {
    for (const property of properties) {
        if (property in changes) {
            return true;
        }
    }
    return false;
}

export function changeFilter(options?: Partial<IFilterOptions>) {
    const { explicitElement, explicitProperties } = options || {};
    const isSameNodeOrDescendant = (
        node: OneOfDataNodes,
        _explicitElement: OneOfDataNodes
    ): boolean => {
        if (node.id === _explicitElement.id) {
            return true;
        }

        if (isGroupDataNode(_explicitElement)) {
            // Find closest descendant
            return !!_explicitElement.findNodeById_m(node.id);
        }

        return false;
    };

    return function (source: Observable<IElementChange>): Observable<IElementChange> {
        return source.pipe(
            filter(({ element }) =>
                explicitElement && element ? isSameNodeOrDescendant(element, explicitElement) : true
            ),
            filter(({ changes }) =>
                explicitProperties ? explicitAnyChangesFilter(changes, explicitProperties) : true
            )
        );
    };
}

export function isElementChange(value: unknown): value is IElementChange {
    if (value && typeof value === 'object') {
        return 'element' in value && 'changes' in value;
    }

    return false;
}

export function filterChangeEvent<T extends { type: ElementChangeType }>(
    source: Observable<T>
): Observable<T> {
    return source.pipe(
        filter(({ type }) => type !== undefined && type !== ElementChangeType.Skip),
        debounce(({ type }) => (type === ElementChangeType.Burst ? interval(500) : of(true)))
    );
}
