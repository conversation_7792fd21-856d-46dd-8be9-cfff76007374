import { CreativeDataNode } from '@creative/nodes/base-data-node';
import { IElementChange } from '@domain/element-change';
import { createCreativeDataNodeFixture } from '@fixtures/data-node.fixture';
import { EditorEventService } from './editor-event.service';
import { changeFilter, isElementChange } from './element-change';

describe('EditorEvent', () => {
    let editorEvent: EditorEventService;

    beforeEach(() => {
        editorEvent = new EditorEventService();
        editorEvent.elements.changes$.subscribe();
    });

    it('should have created EditorEventService', () => {
        expect(editorEvent).toBeTruthy();
    });

    describe('creative event', () => {
        it('should emit the changed property', done => {
            editorEvent.creative.change$.subscribe(v => {
                expect(v.property).toBe('height');
                expect(v.value).toBe(200);
                done();
            });
            editorEvent.creative.change('height', 200);
        });
    });

    describe('element event', () => {
        describe('onChange observable', () => {
            let creativeDataNode: CreativeDataNode;

            beforeEach(() => {
                creativeDataNode = createCreativeDataNodeFixture();
                editorEvent = new EditorEventService();
                editorEvent.elements.changes$.subscribe();
            });

            it('should get the changed element', done => {
                const element = creativeDataNode.elements[0];
                editorEvent.elements.change$.subscribe(({ element: changedElement }) => {
                    expect(element).toBe(changedElement);
                    done();
                });
                editorEvent.elements.change(element, { x: 50, y: 50 });
            });

            it('should get the changed property', done => {
                const element = creativeDataNode.elements[0];
                editorEvent.elements.change$.subscribe(({ element: changedElement, changes }) => {
                    expect(element).toBe(changedElement);
                    expect(changes).toEqual({ x: 50, y: 50 });
                    done();
                });
                editorEvent.elements.change(element, { x: 50, y: 50 });
            });

            it('should filter out non-explicit element', done => {
                const element = creativeDataNode.elements[0];
                const secondElement = creativeDataNode.elements[1];
                editorEvent.elements.change$
                    .pipe(changeFilter({ explicitElement: element }))
                    .subscribe(({ element: changedElement }) => {
                        expect(changedElement).toBe(element);
                        expect(changedElement).not.toBe(secondElement);
                        done();
                    });
                editorEvent.elements.change(secondElement, { x: 50, y: 50 });
                editorEvent.elements.change(element, { x: 50, y: 50 });
            });

            it('should filter out non-explicit properties', done => {
                const element = creativeDataNode.elements[0];
                editorEvent.elements.change$
                    .pipe(
                        changeFilter({
                            explicitProperties: ['time']
                        })
                    )
                    .subscribe(({ element: changedElement, changes }) => {
                        expect(changedElement).toBe(element);
                        expect(changes.time).toBeTruthy();
                        expect(changes.time).toEqual(1);
                        done();
                    });
                editorEvent.elements.change(element, { x: 50 });
                editorEvent.elements.change(element, { time: 1 });
            });

            it('should filter out non-explicit element and non-explicit properties', done => {
                const element = creativeDataNode.elements[0];
                const secondElement = creativeDataNode.elements[1];
                editorEvent.elements.change$
                    .pipe(
                        changeFilter({
                            explicitElement: element,
                            explicitProperties: ['time']
                        })
                    )
                    .subscribe(({ element: changedElement, changes }) => {
                        expect(changedElement).toBe(element);
                        expect(changes.time).toBeTruthy();
                        expect(changes.time).toEqual(1);
                        done();
                    });
                editorEvent.elements.change(secondElement, { x: 50 });
                editorEvent.elements.change(element, { x: 50 });
                editorEvent.elements.change(secondElement, { time: 1 });
                editorEvent.elements.change(element, { time: 1 });
            });
        });

        describe('immediateElementChange$ observable', () => {
            let creativeDataNode: CreativeDataNode;

            beforeEach(() => {
                creativeDataNode = createCreativeDataNodeFixture();
            });

            it('should get the changed element', done => {
                const element = creativeDataNode.elements[0];
                editorEvent.elements.immediateChange$.subscribe(({ element: changedElement }) => {
                    expect(element).toBe(changedElement);
                    done();
                });
                editorEvent.elements.change(element, { x: 0 });
            });

            it('should get the changed property', done => {
                const element = creativeDataNode.elements[0];
                editorEvent.elements.immediateChange$.subscribe(
                    ({ element: changedElement, changes }) => {
                        expect(element).toBe(changedElement);
                        expect(changes).toEqual({ x: 50, y: 50 });
                        done();
                    }
                );
                editorEvent.elements.change(element, { x: 50, y: 50 });
            });
        });

        describe('elementsChange observable', () => {
            let creativeDataNode: CreativeDataNode;

            beforeEach(() => {
                creativeDataNode = createCreativeDataNodeFixture();
            });

            it('should get the changed elements', done => {
                const element = creativeDataNode.elements[0];
                const secondElement = creativeDataNode.elements[1];
                editorEvent.elements.changes$.subscribe(changes => {
                    expect(Array.isArray(changes.elements)).toBe(true);
                    expect(changes.elements.find(el => el.element!.id === element.id)).toBeTruthy();
                    expect(
                        changes.elements.find(el => el.element!.id === secondElement.id)
                    ).toBeTruthy();
                    done();
                });
                editorEvent.elements.change(element, { x: 0 });
                editorEvent.elements.change(secondElement, { x: 0 });
            });
        });

        describe('isElementChange', () => {
            it('should return true when elementChange object is passed to it', () => {
                const creativeDataNode = createCreativeDataNodeFixture();
                const elementChange: IElementChange = {
                    changes: {
                        x: 50
                    },
                    element: creativeDataNode.elements[0]
                };
                expect(isElementChange(elementChange)).toBeTruthy();
            });

            it('should return false when argument is not elementChange object', () => {
                expect(isElementChange({ foo: 'bar' })).toBeFalsy();
            });
        });
    });
});
