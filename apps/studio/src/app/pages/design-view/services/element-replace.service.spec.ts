import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { isImageNode, isVideoNode } from '@creative/nodes';
import { createCreativeDataNodeMock } from '@creative/nodes/__tests__/mocks/data-node.mock';
import { ElementKind } from '@domain/elements';
import { ICreativeDataNode, IImageElementDataNode, IVideoElementDataNode } from '@domain/nodes';
import { createImageElementAssetMock, createVideoElementAssetMock } from '@mocks/brand-library.mock';
import { createCreativesetMock, createMockCreativesetDataService } from '@mocks/creativeset.mock';
import { createMockEditorStateService } from '@mocks/editor.mock';
import { createDataNodeMock } from '@mocks/element.mock';
import { createMockBrandLibraryDataService } from '@mocks/services/brand-library-data-service.mock';
import { createBackendStateMock, createHistoryServiceMock } from '@mocks/services/history.service.mock';
import { createVersionServiceMock } from '@mocks/services/versions-service.mock';
import { CreativesetDataService } from '@studio/common';
import { BrandService } from '@studio/common/brand';
import { provideEnvironment } from '@studio/common/environment/environment.provider';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { provideMock } from '@studio/testing/utils/provide-mock';
import { ApolloTestingModule } from 'apollo-angular/testing';
import { of, startWith } from 'rxjs';
import { environment } from '../../../../environments/environment.test';
import { BrandLibraryDataService } from '../../../shared/media-library/brand-library.data.service';
import { defineMatchMedia } from '../../../shared/mocks/matchMedia.mock';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../../shared/mocks/store.mock';
import { VersionsService } from '../../../shared/versions/state/versions.service';
import { DesignViewComponent } from '../design-view.component';
import { BrandLibraryElementService } from '../media-library/brandlibrary-element.service';
import { CopyPasteService } from './copy-paste.service';
import { EditorStateService } from './editor-state.service';
import { ElementCreatorService } from './element-creator.service';
import { ElementReplaceService } from './element-replace.service';
import { ElementSelectionService } from './element-selection.service';
import { HistoryService } from './history.service';
import { MutatorService } from './mutator.service';

describe('ElementReplaceService', () => {
    let elementReplaceService: ElementReplaceService;
    let imageElement: IImageElementDataNode;
    let videoElement: IVideoElementDataNode;
    let creativeDataNodeMock: ICreativeDataNode;
    let editorStateServiceMock: EditorStateService;
    let historyService: HistoryService;

    beforeAll(() => {
        defineMatchMedia();
    });

    beforeEach(() => {
        imageElement = createDataNodeMock({ kind: ElementKind.Image });
        videoElement = createDataNodeMock({ kind: ElementKind.Video });

        creativeDataNodeMock = createCreativeDataNodeMock({
            elements: [createDataNodeMock(), imageElement, videoElement]
        });

        editorStateServiceMock = createMockEditorStateService({
            creativeDataNode: creativeDataNodeMock
        });

        const creativesetDataServiceMock = createMockCreativesetDataService({
            creativeset$: of(createCreativesetMock())
        });

        const historyServiceBackendState = createBackendStateMock({
            current: {
                creativeDataNode: creativeDataNodeMock,
                designId: '1'
            },
            others: []
        });

        TestBed.configureTestingModule({
            declarations: [],
            imports: [ApolloTestingModule],
            providers: [
                provideHttpClient(),
                provideHttpClientTesting(),
                provideEnvironment(environment),
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER,
                EnvironmentService,
                ElementReplaceService,
                BrandLibraryElementService,
                provideMock(CopyPasteService),
                provideMock(ElementCreatorService),
                ElementSelectionService,
                provideMock(BrandLibraryDataService, createMockBrandLibraryDataService()),
                provideMock(DesignViewComponent),
                provideMock(EditorStateService, editorStateServiceMock),
                provideMock(CreativesetDataService, creativesetDataServiceMock),
                provideMock(VersionsService, createVersionServiceMock()),
                provideMock(BrandService),
                provideMock(
                    HistoryService,
                    createHistoryServiceMock({
                        backendState$: of().pipe(startWith(historyServiceBackendState)),
                        backendState: historyServiceBackendState
                    })
                ),
                provideMock(MutatorService)
            ]
        });

        elementReplaceService = TestBed.inject(ElementReplaceService);
        historyService = TestBed.inject(HistoryService);
    });

    it('should be created', () => {
        expect(elementReplaceService).toBeTruthy();
    });

    describe('replaceImageInAllDesigns', () => {
        it('should call replaceImageInDesign with correct designs', () => {
            const imageElementAsset = createImageElementAssetMock();

            const spy = jest.spyOn(elementReplaceService, 'replaceImageInDesign');
            elementReplaceService.replaceImageInAllDesigns(imageElement, imageElementAsset);

            const allDataNodes = [
                ...historyService.backendState.current.creativeDataNode.elements,
                ...historyService.backendState.others.map(state => state.creativeDataNode.elements)
            ].filter(isImageNode);

            allDataNodes.forEach(dataNode => {
                expect(spy).toHaveBeenCalledWith(dataNode, imageElementAsset);
            });
        });
    });

    describe('replaceVideoInAllDesigns', () => {
        it('should call replaceVideoInDesign with correct designs', () => {
            const videoElementAsset = createVideoElementAssetMock();

            const spy = jest.spyOn(elementReplaceService, 'replaceVideoInDesign');
            elementReplaceService.replaceVideoInAllDesigns(videoElement, videoElementAsset);

            const allDataNodes = [
                ...historyService.backendState.current.creativeDataNode.elements,
                ...historyService.backendState.others.map(state => state.creativeDataNode.elements)
            ].filter(isVideoNode);

            allDataNodes.forEach(dataNode => {
                expect(spy).toHaveBeenCalledWith(dataNode, videoElementAsset);
            });
        });
    });
});
