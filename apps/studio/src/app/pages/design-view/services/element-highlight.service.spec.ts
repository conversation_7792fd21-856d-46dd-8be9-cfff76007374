import { ElementHighlightService } from './element-highlight.service';
import { createDataNodeMock } from '@mocks/element.mock';

describe('Element highlight service', () => {
    let elementHighlightService: ElementHighlightService;
    const mockedElement = createDataNodeMock({ id: 'foo' });

    beforeEach(() => {
        elementHighlightService = new ElementHighlightService();
        elementHighlightService.elementHighlight$.subscribe();
    });

    it('setHighlight should set the current highlight', () => {
        elementHighlightService.setHighlight(mockedElement, 'workspace');
        expect(elementHighlightService.currentHighlight?.node).toEqual(mockedElement);
    });

    it('clearHighlight should clear the current highlight', () => {
        elementHighlightService.setHighlight(mockedElement, 'workspace');
        elementHighlightService.clearHighlight();
        expect(elementHighlightService.currentHighlight).toBeUndefined();
    });

    it('clearHighlight should have correct highlight context', () => {
        elementHighlightService.setHighlight(mockedElement, 'workspace');
        elementHighlightService.setHighlight(mockedElement, 'timeline');
        expect(elementHighlightService.currentHighlight?.node).toEqual(mockedElement);
        expect(elementHighlightService.currentHighlight?.context).toEqual('timeline');
    });

    it('should only emit one time when element id does not differs', () => {
        const next = jest.fn();
        elementHighlightService.elementHighlight$.subscribe(next);

        elementHighlightService.setHighlight(mockedElement, 'workspace');
        elementHighlightService.setHighlight(mockedElement, 'workspace');

        expect(next).toHaveBeenCalledTimes(1);
    });

    it('should emit every time element id differs', () => {
        const next = jest.fn();
        elementHighlightService.elementHighlight$.subscribe(next);

        elementHighlightService.setHighlight(mockedElement, 'workspace');
        elementHighlightService.setHighlight(mockedElement, 'workspace');
        elementHighlightService.setHighlight(createDataNodeMock({ id: 'bar' }), 'workspace');

        expect(next).toHaveBeenCalledTimes(2);
    });
});
