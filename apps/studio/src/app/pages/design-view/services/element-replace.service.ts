import { inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { cloneDeep } from '@apollo/client/utilities';
import { Logger } from '@bannerflow/sentinel-logger';
import { isWidgetContentBlobReference } from '@creative/elements/widget/utils';
import { isImageNode, isVideoNode, isWidgetNode } from '@creative/nodes/helpers';
import { IBrandLibraryWidgetElement } from '@domain/brand/brand-library';
import {
    AssetReference,
    IImageElementAsset,
    IVideoElementAsset
} from '@domain/creativeset/element-asset';
import { IVersionProperty } from '@domain/creativeset/version';
import { IFeed } from '@domain/feed';
import {
    IImageElementDataNode,
    IMediaElementDataNode,
    IVideoElementDataNode,
    OneOfElementDataNodes
} from '@domain/nodes';
import { IWidgetCustomProperty, IWidgetElementDataNode } from '@domain/widget';
import { SaveType } from '@studio/domain/components/ai-studio.types';
import {
    EventLoggerService,
    ImagePropertyChangeEvent,
    VideoPropertyChangeEvent
} from '@studio/monitoring/events';
import { distinctArrayById } from '@studio/utils/array';
import { getAssetReferenceTypeOfElement, getFeedReferenceTypeOfElement } from '@studio/utils/asset';
import { createElementProperty, getWidgetContentUrlOfElement } from '@studio/utils/element.utils';
import { isMediaNode } from '@studio/utils/media';
import { filter } from 'rxjs';
import { GenAIService } from '../../../shared/ai-studio/state/gen-ai.service';
import { BrandLibraryDataService } from '../../../shared/media-library/brand-library.data.service';
import { VersionsService } from '../../../shared/versions/state/versions.service';
import { DesignViewComponent } from '../design-view.component';
import { BrandLibraryElementService } from '../media-library/brandlibrary-element.service';
import { EditorStateService } from './editor-state.service';
import { ElementSelectionService } from './element-selection.service';
import { HistoryService } from './history.service';
import { MutatorService } from './mutator.service';
import { isVideoStreamable } from './streaming/streaming-validation';

@Injectable()
export class ElementReplaceService {
    private editorStateService = inject(EditorStateService);
    private historyService = inject(HistoryService);
    private brandLibraryElementService = inject(BrandLibraryElementService);
    private editor = inject(DesignViewComponent);
    private elementSelectionService = inject(ElementSelectionService);
    private brandLibraryDataService = inject(BrandLibraryDataService);
    private eventLoggerService = inject(EventLoggerService);
    private mutatorService = inject(MutatorService);
    private versionsService = inject(VersionsService);
    private genAIService = inject(GenAIService);
    private dataNodes: OneOfElementDataNodes[] = [];
    private versionProperties: IVersionProperty[] = [];

    private logger = new Logger('ElementReplaceService');

    constructor() {
        this.historyService.backendState$.pipe(takeUntilDestroyed()).subscribe(backendState => {
            const backendStateDataNodes = backendState.others.flatMap(
                ({ creativeDataNode }) => creativeDataNode.elements
            );

            this.dataNodes = [
                ...this.editorStateService.creativeDataNode.elements,
                ...backendStateDataNodes
            ];
        });

        this.genAIService.saveOnCanvasPayload$
            .pipe(
                filter(payload => payload?.saveType === SaveType.Replace),
                takeUntilDestroyed()
            )
            .subscribe(payload => {
                if (!payload) return;

                this.replaceImage(payload.imageAsset, payload.replaceInAllDesigns);
            });

        this.versionsService.selectedVersionProperties$
            .pipe(takeUntilDestroyed())
            .subscribe(versionProperties => (this.versionProperties = versionProperties));
    }

    replaceImage(imageAsset: IImageElementAsset, replaceInAllDesigns = false): void {
        const imageElements =
            this.elementSelectionService.currentSelection.elements.filter(isImageNode);
        for (const element of imageElements) {
            this.eventLoggerService.log(
                new ImagePropertyChangeEvent('imageAsset', element.imageAsset?.url, imageAsset.url),
                this.logger
            );

            this.mutatorService.setElementPropertyValue(element, 'imageAsset', imageAsset);

            // replace in design
            if (replaceInAllDesigns) {
                this.replaceImageInAllDesigns(element, imageAsset);
            } else {
                this.replaceImageInDesign(element, imageAsset);
            }

            this.replaceElementWithMediaReference(element, imageAsset.id);
        }
    }

    replaceVideo(videoAsset: IVideoElementAsset, replaceInAllDesigns = false): void {
        const videoElements =
            this.elementSelectionService.currentSelection.elements.filter(isVideoNode);
        for (const element of videoElements) {
            this.eventLoggerService.log(
                new VideoPropertyChangeEvent('videoAsset', element.videoAsset?.url, videoAsset.url),
                this.logger
            );

            this.mutatorService.setElementPropertyValue(element, 'videoAsset', videoAsset);

            if (replaceInAllDesigns) {
                this.replaceVideoInAllDesigns(element, videoAsset);
            } else {
                this.replaceVideoInDesign(element, videoAsset);
            }

            this.replaceElementWithMediaReference(element, videoAsset.id);
        }
    }

    replaceImageInAllDesigns(element: IImageElementDataNode, imageAsset: IImageElementAsset): void {
        for (const dataNode of this.dataNodes) {
            if (!isImageNode(dataNode) || dataNode.id !== element.id) {
                continue;
            }

            this.replaceImageInDesign(dataNode, imageAsset);
        }
    }

    replaceImageInDesign(dataNode: IImageElementDataNode, imageAsset: IImageElementAsset): void {
        const newImageAsset: IImageElementAsset = {
            id: imageAsset.id,
            url: imageAsset.url,
            name: imageAsset.name,
            width: imageAsset.width,
            height: imageAsset.height,
            isGenAi: imageAsset.isGenAi
        };

        // holding the image reference in the shared element properties list
        // is only used for BE to know what images being used in the creative set
        const property = dataNode.globalElement.properties.find(e => e.name === AssetReference.Image);
        if (property) {
            property.value = newImageAsset.id;
        }

        const libraryElement = this.brandLibraryDataService.getElementByAssetId(
            AssetReference.Image,
            imageAsset.id
        );

        dataNode.feed = undefined;
        dataNode.imageAsset = newImageAsset;
        dataNode.imageSettings = cloneDeep(dataNode.imageSettings); // also set new settings when switching to svg
        dataNode.parentId = libraryElement?.id;
    }

    replaceVideoInAllDesigns(element: IVideoElementDataNode, videoAsset: IVideoElementAsset): void {
        for (const dataNode of this.dataNodes) {
            if (!isVideoNode(dataNode) || dataNode.id !== element.id) {
                continue;
            }

            this.replaceVideoInDesign(dataNode, videoAsset);
        }
    }

    replaceVideoInDesign(dataNode: IVideoElementDataNode, videoAsset: IVideoElementAsset): void {
        const newVideoAsset: IVideoElementAsset = {
            id: videoAsset.id,
            url: videoAsset.url,
            width: videoAsset.width,
            height: videoAsset.height,
            name: videoAsset.name,
            fileSize: videoAsset.fileSize
        };

        const property = dataNode.globalElement.properties.find(e => e.name === AssetReference.Video);
        if (property) {
            property.value = newVideoAsset.id;
        }

        const libraryElement = this.brandLibraryDataService.getElementByAssetId(
            AssetReference.Video,
            newVideoAsset.id
        );

        dataNode.feed = undefined;
        dataNode.videoAsset = newVideoAsset;
        dataNode.videoSettings = cloneDeep(dataNode.videoSettings); // also set new settings when switching to svg
        const isStreamable = isVideoStreamable(dataNode);
        dataNode.videoSettings.streaming.enabled = isStreamable;
        dataNode.videoSettings.optimization.enabled = isStreamable;
        dataNode.parentId = libraryElement?.id;
    }

    replaceElementWithMediaReference(element: IMediaElementDataNode, assetId?: string): void {
        const editorElement = element.globalElement;
        const type = element.globalElement.type;

        const assetReferenceName = getAssetReferenceTypeOfElement(editorElement);
        const feededReferenceName = getFeedReferenceTypeOfElement(editorElement);

        const currentAssetReference = editorElement.properties.find(
            prop => prop.name === assetReferenceName
        );

        const feededReference = editorElement.properties.find(
            prop => prop.name === feededReferenceName
        );

        const referenceProperty = currentAssetReference ?? feededReference;

        if (!referenceProperty) {
            throw new Error(`Asset reference for ${type} is missing.`);
        }

        let imageOrVideoAsset: undefined | string;

        if (isImageNode(element)) {
            imageOrVideoAsset = element.imageAsset?.id;
        } else if (isVideoNode(element)) {
            imageOrVideoAsset = element.videoAsset?.id;
        }

        if (imageOrVideoAsset) {
            if (!assetId) {
                throw new Error(`Can not assign assetId of undefined.`);
            }

            referenceProperty.name = assetReferenceName;
            referenceProperty.value = assetId;

            return;
        }

        if (!element.feed) {
            throw new Error(`Can not assign feed value reference of undefined.`);
        }

        referenceProperty.name = feededReferenceName;
        referenceProperty.value = `${element.feed.id}.${element.feed.path}`;
    }

    replaceFeededMediaInAllDesigns(element: IMediaElementDataNode, feed: IFeed): void {
        for (const dataNode of this.dataNodes) {
            if (!isMediaNode(dataNode) || dataNode.id !== element.id) {
                continue;
            }

            this.replaceFeededMediaInDesign(dataNode, feed);
        }
    }

    replaceFeededMediaInDesign(element: IMediaElementDataNode, feed: IFeed): void {
        element.feed = cloneDeep(feed);
        element.parentId = undefined;

        if (isVideoNode(element)) {
            element.videoAsset = undefined;
        }

        if (isImageNode(element)) {
            element.imageAsset = undefined;
        }

        this.replaceElementWithMediaReference(element);
    }

    async updateWidgetInAllDesigns(widgetLibraryElement: IBrandLibraryWidgetElement): Promise<void> {
        // Get parentId on document elements since it's more reliable
        const dataElements = this.editorStateService.creativeDataNode.elements.filter(isWidgetNode);
        const widgetDataElements = dataElements.filter(node =>
            this.brandLibraryDataService.isParentElementOfNode(widgetLibraryElement, node)
        );
        const globalDataElements = this.dataNodes.filter(dataNode =>
            widgetDataElements.find(
                widgetElement => dataNode.id === widgetElement.id && dataNode !== widgetElement
            )
        ) as IWidgetElementDataNode[];

        // Update custom properties for all document elements with the element reference
        for (const dataElement of widgetDataElements) {
            const element = dataElement.globalElement;
            // Update code properties & content blob for each element
            const newContentUrl = getWidgetContentUrlOfElement(widgetLibraryElement);
            const blobReference = element.properties.find(isWidgetContentBlobReference);

            if (newContentUrl) {
                if (blobReference) {
                    blobReference.value = newContentUrl;
                } else {
                    element.properties.push(
                        createElementProperty({
                            name: AssetReference.WidgetContentUrl,
                            unit: 'string',
                            value: newContentUrl
                        })
                    );
                }
            }

            await this.brandLibraryElementService.updateWidgetFromLibrary(
                dataElement,
                widgetLibraryElement
            );
        }

        this.reflectPropertyChanges(globalDataElements, widgetDataElements);

        this.cleanOrphanVersionProperties();

        await this.editorStateService.applyWidgetCodeOnWidgetNodes();

        const selectedNodes = this.elementSelectionService.currentSelection.nodes;

        this.editor.rerenderCanvas();

        if (selectedNodes.length > 0) {
            this.elementSelectionService.setSelection(...selectedNodes);
            this.editor.workspace.redrawGizmos();
        }
    }

    /**
     * Reflect changes made to current elements on the global elements.
     * This occurs after doing the actual updating in order to prevent properties
     * to be created when they shouldn't
     */
    private reflectPropertyChanges(
        globalDataElements: IWidgetElementDataNode[],
        localDataElements: IWidgetElementDataNode[]
    ): void {
        const localElementsMap = new Map<string, IWidgetElementDataNode>();

        for (const dataElement of localDataElements) {
            localElementsMap.set(dataElement.id, dataElement);
        }

        for (const globalDataElement of globalDataElements) {
            const matchingLocalElement = localElementsMap.get(globalDataElement.id);

            if (!matchingLocalElement) {
                continue;
            }

            globalDataElement.globalElement.properties = cloneDeep(
                matchingLocalElement.globalElement.properties
            );

            const globalCustomPropertiesMap = new Map<string, IWidgetCustomProperty>();

            for (const customProperty of globalDataElement.customProperties) {
                globalCustomPropertiesMap.set(customProperty.name, customProperty);
            }

            for (let i = 0; i < matchingLocalElement.customProperties.length; i++) {
                const customProperty = matchingLocalElement.customProperties[i];
                const globalPropertyExists = globalCustomPropertiesMap.get(customProperty.name);

                if (globalPropertyExists) {
                    const globalPropertyValue = globalPropertyExists.value;
                    // Keep same value if the unit is the same
                    const value =
                        globalPropertyExists.unit === customProperty.unit
                            ? globalPropertyValue
                            : customProperty.value;
                    globalDataElement.customProperties[i] = {
                        ...customProperty,
                        value: value
                    };
                    continue;
                }

                globalDataElement.customProperties.splice(i, 0, customProperty);
            }
        }
    }

    private cleanOrphanVersionProperties(): void {
        const allElements = distinctArrayById([
            ...this.dataNodes,
            ...this.editorStateService.creativeDataNode.elements
        ]).map(({ globalElement }) => globalElement);
        const versionPropertyReferences = allElements
            .flatMap(({ properties }) => properties.map(({ versionPropertyId }) => versionPropertyId))
            .filter(Boolean) as string[];

        const orphanVersionProperties = this.versionProperties
            .filter(versionProperty => !versionPropertyReferences.some(id => id === versionProperty.id))
            .map(vp => vp.id);

        this.versionsService.removeVersionPropertiesByIds(orphanVersionProperties);
    }
}
