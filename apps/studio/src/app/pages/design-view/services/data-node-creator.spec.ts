import { TestBed } from '@angular/core/testing';
import { I<PERSON><PERSON><PERSON> } from '@domain/creative/renderer.header';
import { ElementKind } from '@domain/elements';
import { IElementDataNode } from '@domain/nodes';
import { createRendererFixture } from '@fixtures/renderer.fixture';
import { provideEnvironment } from '@studio/common/environment/environment.provider';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { environment } from '../../../../environments/environment.test';
import { defineMatchMedia } from '../../../shared/mocks/matchMedia.mock';
import { NodeCreatorService } from '../../../shared/services/data-node-creator.service';

describe('DataNodeCreatorService', () => {
    let nodeCreator: NodeCreatorService;
    let renderer: IRenderer;

    beforeAll(() => {
        defineMatchMedia();
    });
    beforeEach(() => {
        renderer = createRendererFixture(300, 250, []);
        TestBed.configureTestingModule({
            providers: [provideEnvironment(environment), EnvironmentService, NodeCreatorService],
            imports: []
        });

        nodeCreator = TestBed.inject(NodeCreatorService);
        nodeCreator.setCreative(renderer.creativeDocument);
    });

    it('should have a creative document ref', () => {
        expect(nodeCreator['creative']).toBe(renderer.creativeDocument);
    });

    it('should throw error if node kind is unknown', () => {
        expect(() => nodeCreator.create('foo' as any)).toThrow();
    });

    it('should create a rectangle', () => {
        const node = nodeCreator.create(ElementKind.Rectangle);
        expect(node.kind).toBe(ElementKind.Rectangle);
        expect(node.name).toBe('Rectangle');
        expect(node.id).not.toBe('123');
        expect(node.__rootNode).toBe(renderer.creativeDocument);
    });

    it('should create a rectangle with overridden data', () => {
        const dataOverride: Partial<IElementDataNode<ElementKind.Rectangle>> = {
            width: 100,
            height: 50,
            x: 420,
            y: 69,
            name: 'My rectangle',
            id: '123',
            opacity: undefined
        };
        const node = nodeCreator.create(ElementKind.Rectangle, dataOverride);
        expect(node.kind).toBe(ElementKind.Rectangle);
        expect(node.name).toBe('My rectangle');
        expect(node.id).toBe('123');
        expect(node.width).toBe(100);
        expect(node.width).toBe(100);
        expect(node.height).toBe(50);
        expect(node.x).toBe(420);
        expect(node.y).toBe(69);
        expect(node.opacity).toBe(undefined);
    });

    it('should create multiple nodes with name incrementation', () => {
        const creative = renderer.creativeDocument;
        const rect = nodeCreator.create(ElementKind.Rectangle);
        creative.addNode_m(rect);

        const ellipse = nodeCreator.create(ElementKind.Ellipse);
        creative.addNode_m(ellipse);

        const rect2 = nodeCreator.create(ElementKind.Rectangle);
        creative.addNode_m(rect2);

        const ellipse2 = nodeCreator.create(ElementKind.Ellipse);
        creative.addNode_m(ellipse2);

        const rect3 = nodeCreator.create(ElementKind.Rectangle);
        creative.addNode_m(rect3);

        expect(rect.name).toBe('Rectangle');
        expect(rect2.name).toBe('Rectangle (1)');
        expect(rect3.name).toBe('Rectangle (2)');
        expect(ellipse.name).toBe('Ellipse');
        expect(ellipse2.name).toBe('Ellipse (1)');
    });
});
