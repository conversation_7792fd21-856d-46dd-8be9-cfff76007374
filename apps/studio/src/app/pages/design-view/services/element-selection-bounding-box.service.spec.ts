import { TestBed } from '@angular/core/testing';
import { createDataNodeMock } from '@mocks/element.mock';
import { provideMock } from '@studio/testing/utils/provide-mock';
import { PropertiesService } from '../properties-panel/properties.service';
import { EditorEventService } from './editor-event/editor-event.service';
import { EditorStateService } from './editor-state.service';
import { ElementSelectionBoundingBoxService } from './element-selection-bounding-box.service';
import { ElementSelectionService } from './element-selection.service';

describe('Element selection bounding box service', () => {
    let elementSelectionBoundingBoxService: ElementSelectionBoundingBoxService;
    let editorEventService: EditorEventService;
    let elementSelectionService: ElementSelectionService;

    const mockedElement = createDataNodeMock({ id: 'Scooby' });
    const secondElement = createDataNodeMock({ id: 'Shaggy' });

    beforeEach(() => {
        TestBed.configureTestingModule({
            declarations: [],
            imports: [],
            providers: [
                ElementSelectionBoundingBoxService,
                EditorEventService,
                ElementSelectionService,
                PropertiesService,
                provideMock(EditorStateService)
            ]
        }).compileComponents();

        const propertiesService = TestBed.inject(PropertiesService);
        editorEventService = TestBed.inject(EditorEventService);
        elementSelectionService = TestBed.inject(ElementSelectionService);
        elementSelectionBoundingBoxService = TestBed.inject(ElementSelectionBoundingBoxService);
        propertiesService.selectedStateChange$.next({});
    });

    it('should recalculate bounding box when a selected element has changed', () => {
        const next = jest.fn();
        elementSelectionBoundingBoxService.boundingBox$.subscribe(next);

        elementSelectionService.setSelection(mockedElement);

        editorEventService.elements.change(mockedElement, { x: 10 });

        // One for setting the selection, and one for setting the element
        expect(next).toHaveBeenCalledTimes(2);
    });

    it('should not recalculate bounding box when an element which is not selected has changed', () => {
        const next = jest.fn();
        elementSelectionBoundingBoxService.boundingBox$.subscribe(next);

        elementSelectionService.setSelection(secondElement);

        editorEventService.elements.change(mockedElement, { x: 10 });

        expect(next).toHaveBeenCalledTimes(1);
    });

    it('should recalculate bounding box when the selection has changed', () => {
        const next = jest.fn();
        elementSelectionBoundingBoxService.boundingBox$.subscribe(next);

        elementSelectionService.setSelection(mockedElement);
        elementSelectionService.setSelection(secondElement);

        expect(next).toHaveBeenCalledTimes(2);
    });

    it('should not recalculate bounding box when the selection has not changed', () => {
        const next = jest.fn();
        elementSelectionBoundingBoxService.boundingBox$.subscribe(next);

        elementSelectionService.setSelection(mockedElement);
        elementSelectionService.setSelection(mockedElement);

        expect(next).toHaveBeenCalledTimes(1);
    });
});
