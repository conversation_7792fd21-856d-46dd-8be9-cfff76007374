import { computed, inject, Injectable, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FFFeatureFlagsService } from '@bannerflow/feature-flags';
import { isVideoNode } from '@creative/nodes/helpers';
import { IVideoElementDataNode } from '@domain/nodes';
import { IVideoSettings } from '@domain/video';
import { StudioFeatureFlags } from '@studio/domain/feature-flags';
import { cloneDeep } from '@studio/utils/clone';
import { v4 as uuidv4 } from 'uuid';
import { StudioTimelineService } from '../timeline/studio-timeline/studio-timeline.service';
import { EditorEventService, ElementChangeType } from './editor-event';
import { ElementCreatorService } from './element-creator.service';
import { ElementSelectionService } from './element-selection.service';
import { MutatorService } from './mutator.service';

@Injectable()
export class VideoEditingService {
    private elementCreatorService = inject(ElementCreatorService);
    private mutatorService = inject(MutatorService);
    private elementSelectionService = inject(ElementSelectionService);
    private editorEventService = inject(EditorEventService);
    private timelineService = inject(StudioTimelineService);
    private ffFeatureFlagService = inject(FFFeatureFlagsService);

    private _isEditing = signal(false);
    isEditingFeatureEnabled = toSignal(
        this.ffFeatureFlagService.isEnabled$(StudioFeatureFlags.VideoEditingEnabled)
    );
    isEditing = computed(() => this._isEditing());

    enableEditing(): void {
        this._isEditing.set(true);
    }

    disableEditing(): void {
        this._isEditing.set(false);
    }

    async split(): Promise<IVideoElementDataNode> {
        const selection = this.elementSelectionService.currentSelection;
        const videoElement = selection.element;

        if (!videoElement || !isVideoNode(videoElement)) {
            throw new Error('No video element selected for splitting');
        }

        const playheadTime = this.timelineService.playheadTime();
        if (
            playheadTime <= videoElement.time ||
            playheadTime >= videoElement.time + videoElement.duration
        ) {
            throw new Error('Split position must be within the video element duration');
        }

        const newElement = await this.createSplitSecondPart(videoElement, playheadTime);

        this.truncateSplitFirstPart(videoElement, playheadTime);
        this.updateDocumentAfterSplit(videoElement, newElement);

        return newElement;
    }

    private async createSplitSecondPart(
        originalNodeElement: IVideoElementDataNode,
        splitTime: number
    ): Promise<IVideoElementDataNode> {
        const clonedNodeElement = cloneDeep(originalNodeElement);
        const originalStartTime = clonedNodeElement.time;
        const originalDuration = clonedNodeElement.duration;
        const originalVideoStartTime = clonedNodeElement.videoSettings.startTime;

        // Element time/duration
        clonedNodeElement.time = splitTime;
        clonedNodeElement.duration = originalDuration - (splitTime - originalStartTime);

        // Video settings
        const videoTimeOffset = splitTime - originalStartTime;
        const newVideoSettings: IVideoSettings = {
            ...clonedNodeElement.videoSettings,
            startTime: originalVideoStartTime + videoTimeOffset
        };
        clonedNodeElement.videoSettings = newVideoSettings;

        // Name/id
        const id = uuidv4();
        clonedNodeElement.globalElement.id = id;
        clonedNodeElement.id = id;
        const newElement = await this.elementCreatorService.createElementCopy(
            clonedNodeElement,
            {
                values: {
                    elementProperties: clonedNodeElement.globalElement.properties,
                    versionProperties: [],
                    defaultVersionProperties: []
                },
                element: clonedNodeElement.globalElement
            },
            false
        );
        // TODO Discuss with ADA how to make it more elegant without creating -> removing -> adding later
        this.mutatorService.creativeDocument.removeNodeById_m(id); // Node added to document in updateDocumentAfterSplit

        return newElement as IVideoElementDataNode;
    }

    private truncateSplitFirstPart(originalElement: IVideoElementDataNode, splitTime: number): void {
        const newDuration = splitTime - originalElement.time;
        this.mutatorService.setElementValues(originalElement, {
            duration: newDuration
        });
    }

    private updateDocumentAfterSplit(
        video: IVideoElementDataNode,
        newVideo: IVideoElementDataNode
    ): void {
        const parent = video.__parentNode ?? video.__rootNode;
        const elementIndex = parent ? parent.nodes.findIndex(e => e.id === video.id) : 0;
        if (parent) {
            parent.addNode_m(newVideo, elementIndex);
        } else {
            this.mutatorService.creativeDocument.addNode_m(newVideo, elementIndex);
        }

        this.mutatorService.renderer.updateElementOrder_m();

        this.editorEventService.creative.change(
            'elements',
            [video, newVideo],
            ElementChangeType.Instant
        );

        this.elementSelectionService.clearSelection();
    }
}
