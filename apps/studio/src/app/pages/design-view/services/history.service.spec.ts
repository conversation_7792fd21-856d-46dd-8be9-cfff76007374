import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { createCreativeDataNodeMock } from '@creative/nodes/__tests__/mocks/data-node.mock';
import { getGlobalElementsFromCreativeDataNode } from '@data/deserialization/design-api/sapi-conversion-helpers';
import { ElementKind } from '@domain/elements';
import { createMockCreativesetDataService } from '@mocks/creativeset.mock';
import { createMockEditorStateService } from '@mocks/editor.mock';
import { createDataNodeMock } from '@mocks/element.mock';
import { createEditorSaveStateServiceMock } from '@mocks/services/editor-save-state.service.mock';
import { createVersionServiceMock } from '@mocks/services/versions-service.mock';
import { CreativesetDataService } from '@studio/common';
import { BrandService } from '@studio/common/brand';
import { provideEnvironment } from '@studio/common/environment/environment.provider';
import { FontFamiliesService } from '@studio/common/font-families';
import { EnvironmentService } from '@studio/common/services/environment.service';
import { ApolloTestingModule } from 'apollo-angular/testing';
import { of } from 'rxjs';
import { environment } from '../../../../environments/environment.test';
import { BrandLibraryDataService } from '../../../shared/media-library/brand-library.data.service';
import { defineMatchMedia } from '../../../shared/mocks/matchMedia.mock';
import { NGRX_STORES_CONFIG_PROVIDER, STORE_PROVIDER } from '../../../shared/mocks/store.mock';
import { VersionsService } from '../../../shared/versions/state/versions.service';
import { PropertiesService } from '../properties-panel/properties.service';
import { KeyframeService } from '../timeline';
import { EditorEventService } from './editor-event';
import { EditorSaveStateService } from './editor-save-state.service';
import { EditorStateService } from './editor-state.service';
import { ElementSelectionService } from './element-selection.service';
import { HistoryService } from './history.service';

describe('HistoryService', () => {
    let historyService: HistoryService;
    let editorStateServiceMock: EditorStateService;

    beforeAll(() => {
        defineMatchMedia();
    });

    beforeEach(() => {
        const rectangleMock = createDataNodeMock({ kind: ElementKind.Rectangle });
        const ellipseMock = createDataNodeMock({ kind: ElementKind.Ellipse });
        const creativeDataNodeMock = createCreativeDataNodeMock({
            nodes: [rectangleMock, ellipseMock]
        });
        editorStateServiceMock = createMockEditorStateService({
            creativeDataNode: creativeDataNodeMock
        });

        TestBed.configureTestingModule({
            imports: [ApolloTestingModule],
            providers: [
                provideHttpClient(),
                provideHttpClientTesting(),
                provideEnvironment(environment),
                NGRX_STORES_CONFIG_PROVIDER,
                STORE_PROVIDER,
                EnvironmentService,
                {
                    provide: CreativesetDataService,
                    useValue: createMockCreativesetDataService()
                },
                {
                    provide: BrandService,
                    useValue: {}
                },
                HistoryService,
                ElementSelectionService,
                PropertiesService,
                EditorEventService,
                KeyframeService,
                {
                    provide: BrandLibraryDataService,
                    useValue: {
                        fetchBrandLibrary$: of()
                    }
                },
                {
                    provide: VersionsService,
                    useValue: createVersionServiceMock()
                },
                {
                    provide: EditorStateService,
                    useValue: editorStateServiceMock
                },
                {
                    provide: FontFamiliesService,
                    useValue: {}
                },
                { provide: EditorSaveStateService, useValue: createEditorSaveStateServiceMock() }
            ]
        });
        historyService = TestBed.inject(HistoryService);
        historyService.storeCurrentStateAsBackendState();
    });

    describe('storeCurrentStateAsBackendState', () => {
        it('should elementsBackendState not to be elements', () => {
            const elements = getGlobalElementsFromCreativeDataNode(
                historyService['editorStateService'].creativeDataNode
            );
            const elementsBackendState = getGlobalElementsFromCreativeDataNode(
                historyService.backendState.current.creativeDataNode
            );
            expect(elementsBackendState).not.toBe(elements);
        });
    });
});
