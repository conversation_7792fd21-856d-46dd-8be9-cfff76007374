import { Pipe, PipeTransform } from '@angular/core';
import { Observable } from 'rxjs';
import { ColorService } from './color.service';

@Pipe({ standalone: true, name: 'picker' })
export class ColorPickerPipe implements PipeTransform {
    constructor(private colorService: ColorService) {}

    transform(name: string): Observable<boolean> {
        return this.colorService.isColorPickerOpen(name);
    }
}
