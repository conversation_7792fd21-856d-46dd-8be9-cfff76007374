import { MediaLibraryService } from '../../shared/media-library/state/media-library.service';
import { NodeCreatorService } from '../../shared/services/data-node-creator.service';
import { SocialGuideService } from '../../shared/services/social-guide.service';
import { AssetPickerUploadService } from './asset-picker/asset-picker-upload.service';
import { AssetPickerService } from './asset-picker/asset-picker.service';
import { ColorService } from './color-section/color.service';
import { BrandLibraryElementService } from './media-library/brandlibrary-element.service';
import { ElementRenderingService } from './media-library/element-renderering-service';
import { LibraryElementRenderingService } from './media-library/library-element-renderer.service';
import { PropertiesService } from './properties-panel/properties.service';
import { AssetUploadService } from './services/asset-upload.service';
import { CopyPasteService } from './services/copy-paste.service';
import { EditorEventService } from './services/editor-event/editor-event.service';
import { EditorSaveStateService } from './services/editor-save-state.service';
import { EditorStateService } from './services/editor-state.service';
import { ElementCreatorService } from './services/element-creator.service';
import { ElementHighlightService } from './services/element-highlight.service';
import { ElementReplaceService } from './services/element-replace.service';
import { ElementSelectionBoundingBoxService } from './services/element-selection-bounding-box.service';
import { ElementSelectionService } from './services/element-selection.service';
import { HistoryService } from './services/history.service';
import { MutatorService } from './services/mutator.service';
import { SaveErrorHandlerService } from './services/save-error-handler.service';
import { SelectionNetService } from './services/selection-net.service';
import { ValidationService } from './services/validation.service';
import { VideoEditingService } from './services/video-edit.service';
import { AnimationRecorderService } from './timeline/animation-recorder.service';
import { AnimationService } from './timeline/timeline-element/animation.service';
import { KeyframeService } from './timeline/timeline-element/keyframe.service';
import { TimelineScrollService } from './timeline/timeline-scroll.service';
import { TimelineTransformService } from './timeline/timeline-transformer.service';
import { TimelineZoomService } from './timeline/timeline-zoom.service';
import { StudioWorkspaceService } from './workspace/services/studio-workspace.service';
import { WorkspaceGradientHelperService } from './workspace/workspace-gradient-helper.service';
import { WorkspaceTransformService } from './workspace/workspace-transform.service';

/**
 * Services that should be scoped to Design View and also respect
 * the regular lifecycle (i.e ngOnDestroy).
 * Services that depend on EditorStateService has to be provided here
 */
export const SERVICES = [
    AnimationRecorderService,
    AnimationService,
    AssetPickerService,
    AssetPickerUploadService,
    AssetUploadService,
    BrandLibraryElementService,
    ColorService,
    CopyPasteService,
    EditorEventService,
    EditorSaveStateService,
    EditorStateService,
    ElementCreatorService,
    ElementHighlightService,
    ElementRenderingService,
    ElementReplaceService,
    ElementSelectionBoundingBoxService,
    ElementSelectionService,
    HistoryService,
    KeyframeService,
    LibraryElementRenderingService,
    MediaLibraryService,
    MutatorService,
    NodeCreatorService,
    PropertiesService,
    SaveErrorHandlerService,
    SelectionNetService,
    SocialGuideService,
    StudioWorkspaceService,
    TimelineScrollService,
    TimelineTransformService,
    TimelineZoomService,
    ValidationService,
    WorkspaceGradientHelperService,
    WorkspaceTransformService,
    VideoEditingService
];
