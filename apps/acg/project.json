{"name": "acg", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/acg/src", "projectType": "application", "tags": ["type:app", "scope:server", "scope:creative-generation"], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/acg", "main": "apps/acg/src/main.ts", "tsConfig": "apps/acg/tsconfig.app.json", "assets": ["apps/acg/src/assets"], "webpackConfig": "apps/acg/webpack.config.js", "generatePackageJson": true}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "acg:build"}, "configurations": {"development": {"inspect": true, "buildTarget": "acg:build:development"}, "production": {"buildTarget": "acg:build:production"}}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/acg/jest.config.ts"}}, "generate-build-info": {"executor": "nx:run-commands", "options": {"commands": ["pnpm exec nx g @studio/tools:build-info acg --branch={args.branch}"]}}, "docker-prepare": {"dependsOn": ["ad:build", "creative:build", {"target": "generate-build-info", "params": "forward"}, "build"], "command": "echo Build Ad, Generate Info and Build ACG Success"}, "docker-build": {"dependsOn": [{"target": "docker-prepare", "params": "forward"}], "command": "docker build -f apps/acg/Dockerfile . -t bannerflow.azurecr.io/studio/acg:local"}}}