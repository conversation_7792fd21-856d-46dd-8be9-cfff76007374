import type { Config } from 'jest';

export default (): Config => {
    return {
        displayName: 'ACG',
        preset: '../../jest.preset.js',
        testEnvironment: 'node',
        setupFilesAfterEnv: ['<rootDir>/setup-jest.ts'],
        transform: {
            '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }]
        },
        moduleFileExtensions: ['ts', 'js', 'html', 'json'],
        coverageDirectory: '../../coverage/apps/acg',
        testPathIgnorePatterns: ['/src/config/options.test.ts']
    };
};
