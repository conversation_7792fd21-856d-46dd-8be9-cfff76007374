# ACG - Animated Creative Generator

ACG is a NestJS server responsible for generating ad scripts & creatives and serving the creative preview.

Main documentation found at [Confluence](https://wikiflow.atlassian.net/wiki/spaces/STUDIO/pages/1355055114/ACG+-+Animated+Creative+Generator)

## Hard Dependencies

-   Azure Blob Storage

## Feature Dependencies

-   Azure Feature Management
-   StudioAPI
-   ACG (self dependency)

## Local Setup

NO need to npm install in the `apps/acg` folder. Dependencies should only come from the root package.json.
To temporarily solve the missing dependencies in the docker (due to implicit calls), we add them in this package.json -- hbs, newrelic -- so that webpack can generate a proper package.json when building the docker image.

1. Build the ad scripts via `npm run build:scripts`
2. Run via NX CLI `nx serve acg`

Building is optional, as the app will be built on start using Nest. Although for production it's recommended to do a build to keep the artifact isolated and optimized.
That said, it is not required to use the nx cli at all.
One advantage is that assets like the preview template needs to be copied to the distribution folder, which will be taken care of by the CLI.

## Run against Sandbox (remote)

Make sure you have [remote-compose-infra](https://github.com/nordicfactory/remote-compose-infra)
Add `127.0.0.1 sandbox-api.bannerflow.com` to your hosts file
Update you `.env` file with values from `bf-studio-acg` app service
Run `nx run acg:serve:development`

## Infrastructure / Deployment

## Hosting

The app services are sunset and the new ACG is hosted on Azure Kubernetes Service (AKS).

## Pipeline

In GitHub Actions, ACG deployment is handled by the `acg-deploy` workflow. It is triggered by a push to the `main` branch if ACG was _affected_.

## Monitoring

-   [NewRelic](api.bannerflow.com/acg)

## Smoke tests

During our incursion in the CI/CD setup. We decided we needed more smoke tests in place for ACG.
This is the list of links added to the tests to serve as documentation.

Testing this:
https://api.bannerflow.com/acg/332175/4638004/preview?access-token=20489392051c065ed8ab3b01d7795e76090445ea85613ca39f5d6816986453e2

```
// preview
https://api.bannerflow.com/acg/332175/4638004/preview?access-token=20489392051c065ed8ab3b01d7795e76090445ea85613ca39f5d6816986453e2
// Health check
https://api.bannerflow.com/acg/health/live
// Ad script
https://api.bannerflow.com/acg/332175/4638004/ad.js
// Data JS
https://api.bannerflow.com/acg/332175/4638004/data.js
```
