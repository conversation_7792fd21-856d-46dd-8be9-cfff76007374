import { HttpModule } from '@nestjs/axios';
import { Global, Module } from '@nestjs/common';
import { AuthModule } from '@shared/server/auth';
import { AzureModule } from '@shared/server/azure';
import { SharedServerDataAccessModule } from '@shared/server/data-access';
import { TrackingModule } from '@shared/server/monitoring';
import { DebugController } from '@shared/server/utils';
import { BaseController } from './base.controller';
import { PerformanceService } from './services/performance.service';

@Global()
@Module({
    imports: [
        HttpModule.register({
            timeout: 15000
        }),
        TrackingModule,
        AzureModule,
        SharedServerDataAccessModule,
        AuthModule
    ],
    providers: [PerformanceService],
    controllers: [BaseController, DebugController],
    exports: [SharedServerDataAccessModule, TrackingModule, AuthModule, AzureModule, PerformanceService]
})
export class CoreModule {}
