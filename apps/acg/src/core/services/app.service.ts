import { ITemplateManifest } from '@domain/acg/creative-metadata';
import { Injectable, Logger } from '@nestjs/common';
import { AzureBlobService } from '@shared/server/azure';
import { ScriptService } from '@shared/server/scripts';
import { MD5checksum } from '@shared/server/utils';
import { retryPromiseWithDelay } from '@studio/utils/utils';

const MANIFEST_FILE = 'template-manifest.json';
const SCRIPTS_CONTAINER = 'scripts';
const TEMPLATES_CONTAINER_PATH = 'templates/';

@Injectable()
export class AppService {
    private logger = new Logger(AppService.name);

    constructor(
        private scriptService: ScriptService,
        private azureBlobService: AzureBlobService
    ) {}

    async start(): Promise<void> {
        this.logger.verbose('Starting ACG');

        await retryPromiseWithDelay(() => this.startup());
    }

    private async startup(): Promise<void> {
        this.logger.verbose('startup...');

        await this.scriptService.initializeScripts();

        await this.checkAdVersion();
    }

    private async checkAdVersion(): Promise<void> {
        this.logger.log(`Checking ad version`);
        let manifestData: ITemplateManifest;

        const adVersionDoesntExist =
            !(await this.azureBlobService.blobExists(
                SCRIPTS_CONTAINER,
                `${TEMPLATES_CONTAINER_PATH}ad_v1.js`
            )) ||
            !(await this.azureBlobService.blobExists(
                SCRIPTS_CONTAINER,
                TEMPLATES_CONTAINER_PATH + MANIFEST_FILE
            ));

        const adScriptChecksum = MD5checksum(this.scriptService.getAdScriptFile());

        if (adVersionDoesntExist) {
            manifestData = {
                version: 1,
                file: 'ad_v1.js',
                modifiedDate: new Date().toISOString(),
                checksum: adScriptChecksum
            };

            this.logger.warn('Ad scripts or manifest missing on the blob. Uploading initial one');
            await this.azureBlobService.uploadBlob(
                SCRIPTS_CONTAINER,
                TEMPLATES_CONTAINER_PATH + manifestData.file,
                this.scriptService.getAdScriptFile()
            );
            await this.azureBlobService.uploadBlob(
                SCRIPTS_CONTAINER,
                TEMPLATES_CONTAINER_PATH + MANIFEST_FILE,
                JSON.stringify(manifestData, undefined, 4)
            );
        } else {
            manifestData = JSON.parse(
                await this.azureBlobService.getBlob(
                    SCRIPTS_CONTAINER,
                    TEMPLATES_CONTAINER_PATH + MANIFEST_FILE
                )
            );

            this.logger.verbose(
                `Comparing version checksums: local: ${adScriptChecksum} - manifest: ${manifestData.checksum}`
            );
            if (adScriptChecksum !== manifestData.checksum) {
                manifestData.version++;
                manifestData.file = `ad_v${manifestData.version}.js`;
                manifestData.modifiedDate = new Date().toISOString();
                manifestData.checksum = adScriptChecksum;

                this.logger.log(`New ad script detected. Uploading version: ${manifestData.file}`);
                await this.azureBlobService.uploadBlob(
                    SCRIPTS_CONTAINER,
                    TEMPLATES_CONTAINER_PATH + manifestData.file,
                    this.scriptService.getAdScriptFile()
                );
                await this.azureBlobService.uploadBlob(
                    SCRIPTS_CONTAINER,
                    TEMPLATES_CONTAINER_PATH + MANIFEST_FILE,
                    JSON.stringify(manifestData, undefined, 4)
                );
                this.logger.log(
                    `Ad script & ${MANIFEST_FILE} uploaded successfully to ${TEMPLATES_CONTAINER_PATH}`
                );
            } else {
                this.logger.log(
                    `No changes detected in ad script. Current version: ${manifestData.file}`
                );
            }
        }

        this.scriptService.adVersion = manifestData.version;
    }
}
