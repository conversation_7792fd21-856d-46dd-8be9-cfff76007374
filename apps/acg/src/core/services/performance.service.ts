import { Injectable, Logger } from '@nestjs/common';
import * as osu from 'node-os-utils';

const REFRESH_INTERVAL = 10000;

@Injectable()
export class PerformanceService {
    private freeMemPercentage = 100;
    private freeCPU = 100;

    private logger = new Logger(PerformanceService.name);
    constructor() {
        this.initListeners();
    }

    canAcceptLoad(): boolean {
        return this.freeMemPercentage > 10 && this.freeCPU > 5;
    }

    private initListeners(): void {
        setInterval(async () => {
            this.freeCPU = await osu.cpu.free();
            this.freeMemPercentage = (await osu.mem.info()).freeMemPercentage;
        }, REFRESH_INTERVAL);
    }
}
