import { CanActivate, HttpException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PerformanceService } from './performance.service';

type PerformanceConfig = { loadProtectionEnabled: boolean };

@Injectable()
export class PerformanceGuard implements CanActivate {
    constructor(
        private configService: ConfigService<PerformanceConfig>,
        private performanceService: PerformanceService
    ) {}

    canActivate(): boolean {
        const canAcceptLoad = this.performanceService.canAcceptLoad();
        if (!canAcceptLoad && this.configService.get('loadProtectionEnabled')) {
            throw new HttpException('ACG is experiencing heavy load', 507);
        }
        return true;
    }
}
