import { deserializeCreativesetSnapshot } from '@data/deserialization/creativeset-snapshot';
import { BrandManagerBrandDto } from '@domain/brand';
import { CreativesetSnapshotDto } from '@domain/creativeset/snapshot';
import { HttpService } from '@nestjs/axios';
import {
    Body,
    Controller,
    Get,
    InternalServerErrorException,
    Logger,
    Post,
    Query,
    Req,
    Res
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '@shared/server/auth';
import { AzureBlobService } from '@shared/server/azure';
import { BrandService } from '@shared/server/data-access/brand';
import { CreativesetService } from '@shared/server/data-access/creativeset';
import { Auth } from '@shared/server/data-access/creativeset/auth.decorator';
import { AdService, CreativeService } from '@shared/server/scripts';
import { NotFoundError } from '@studio/utils/errors/apps-errors';
import { sleep } from '@studio/utils/promises';
import { AxiosResponse } from 'axios';
import { Request, Response } from 'express';
import { firstValueFrom, Observable } from 'rxjs';
import { IAnimatedCreativeServerOptions } from '../acg.types';
@Controller('test')
export class TestController {
    private logger = new Logger('TestController');

    constructor(
        private configService: ConfigService<IAnimatedCreativeServerOptions>,
        private httpService: HttpService,
        private authService: AuthService,
        private azureBlobService: AzureBlobService,
        private creativesetService: CreativesetService,
        private creativeService: CreativeService,
        private adService: AdService,
        private brandService: BrandService
    ) {}

    @Get('/error')
    simpleError(@Query('message') message: string): never {
        throw new Error(message || 'Testing Error');
    }

    @Post('/meta')
    async testMetadata(@Body('url') url: string): Promise<number | undefined> {
        const meta = await this.azureBlobService.getBlobMetadataByUrl(url);
        return meta?.contentLength;
    }

    @Get('/timeout')
    async timeoutError(
        @Res() res: Response,
        @Query('delay') delay?: number,
        @Query('url') url?: string
    ): Promise<void> {
        await sleep(delay ?? 100000);

        if (url) {
            try {
                const response = await firstValueFrom(
                    this.httpService.get(url, { responseType: 'arraybuffer' })
                );

                res.setHeader('Content-Type', response.headers['content-type']);
                res.setHeader('Content-Length', response.headers['content-length']);
                res.send(response.data);
            } catch {
                throw new InternalServerErrorException('Failed to fetch content');
            }
        }
    }

    @Get('/network-error')
    networkError(): Observable<AxiosResponse> {
        const hostName = this.configService.get('serviceHostName', { infer: true });
        const url = new URL(`${hostName}/test/error`);
        return this.httpService.get(url.toString(), { timeout: 5000 });
    }

    @Get('/network-timeout')
    networkTimeoutError(): Observable<AxiosResponse> {
        const hostName = this.configService.get('serviceHostName', { infer: true });
        const url = new URL(`${hostName}/test/timeout`);
        return this.httpService.get(url.toString(), { timeout: 5000 });
    }

    // test creative generation locally
    @Get('/metadataUrl/:creativesetId/:creativeId')
    async metadataUrlTestRoute(@Req() req: Request): Promise<unknown> {
        const snapshotUrl = req.query.snapshotUrl as string;
        const creativesetId = req.params.creativesetId;
        const creativeId = req.params.creativeId;
        const container = req.query.container as string;
        const destination = req.query.destination as string;

        const token = await this.authService.getToken();

        const result = await firstValueFrom(
            this.httpService.post(
                `http://api.bannerflow.local/acg/metadataUrl/${creativesetId}/${creativeId}`,
                JSON.stringify({
                    snapshotUrl,
                    destination,
                    container,
                    preloadImage: {
                        url: 'http://lol.lol'
                    }
                }),
                {
                    headers: {
                        Authorization: `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    },
                    timeout: this.configService.get('requestTimeout')
                }
            )
        );

        this.logger.verbose('status', result.status);
        this.logger.verbose('data', result.data);

        return result.data;
    }

    // use like: http://api.bannerflow.local/acg/test/snapshot-url?creativeset=1&snapshot=1
    @Get('/snapshot-url')
    getSnapshotUrl(
        @Query('creativeset') creativesetId: string,
        @Query('snapshot') snapshotId: string
    ): Promise<string> {
        return this.creativesetService.getCreativesetSnapshotUrl(creativesetId, snapshotId);
    }

    @Get('/brand')
    getBrand(@Query('brand') brandId: string): Promise<BrandManagerBrandDto> {
        return this.brandService.getBrand(brandId);
    }

    @Auth('studio')
    @Get('/auth/brand')
    async getBrandWithAuth(@Query('brand') brandId: string): Promise<BrandManagerBrandDto> {
        return await this.brandService.getBrand(brandId);
    }

    @Auth('studio')
    @Get('/auth/creativeset')
    async getCreativesetWithAuth(@Query('creativeset') creativeset: string): Promise<string> {
        return (await this.creativesetService.getCreativeset(creativeset)).name;
    }

    @Get('/snapshot')
    getSnapshotByUrl(@Query('url') url: string): Promise<CreativesetSnapshotDto> {
        return this.creativesetService.getCreativesetSnapshotFromUrl(url);
    }

    // use like: http://api.bannerflow.local/acg/test/snapshot/ad?url=<snapshot-url>&creative=1
    @Get('/snapshot/ad')
    async getAdOfSnapshotCreative(
        @Query('url') url: string,
        @Query('creative') creativeId: string
    ): Promise<unknown> {
        const snapshot = await this.creativesetService.getCreativesetSnapshotFromUrl(url);
        const creativeset = deserializeCreativesetSnapshot(snapshot, creativeId);

        const creative = await this.creativeService.getCreativeFromCreativeset(creativeset, creativeId);
        if (!creative.design) {
            throw new NotFoundError('Creative has not been activated.');
        }

        const dataFile = this.adService.getAdDataFile(creativeset, creative, {}, 'creative-generation');
        return this.adService.getAdCreative(creativeset, creative, dataFile);
    }
}
