import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { Type } from '@nestjs/common/interfaces/type.interface';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { ServeStaticModule } from '@nestjs/serve-static';
import { AdDownloadModule } from '@shared/server/ad-download';
import { AzureServiceBusModule } from '@shared/server/azure';
import { HealthModule } from '@shared/server/health';
import { MetadataModule } from '@shared/server/metadata';
import { RequestInterceptor } from '@shared/server/monitoring';
import { ScriptsModule } from '@shared/server/scripts';
import { join } from 'path';
import configuration from './config/configuration';
import { CoreModule } from './core/core.module';
import { AppService } from './core/services/app.service';
import { TestController } from './core/test.controller';

const controllers: Type<unknown>[] = [];

if (process.env.STAGE === 'local') {
    controllers.push(TestController);
}

const dynamicModules: Type<unknown>[] = [];
if (process.env.STAGE === 'sandbox') {
    dynamicModules.push(AdDownloadModule);
}

@Module({
    imports: [
        HttpModule,
        CoreModule,
        ScriptsModule,
        MetadataModule,
        ConfigModule.forRoot({
            isGlobal: true,
            load: [configuration]
        }),
        HealthModule.withEndpoints({
            configModule: ConfigModule,
            configService: ConfigService,
            useFactory: (configService: ConfigService) => {
                return configService.get('health.readyEndpoints', { infer: true })!;
            }
        }),
        ServeStaticModule.forRoot({
            rootPath: join(__dirname, 'assets'),
            serveRoot: '/static'
        }),
        AzureServiceBusModule.registerAsync({
            imports: [ConfigModule],
            useFactory: (configService: ConfigService) => {
                // connection string and prefix for local development
                const prefix = configService.get('azureServiceBus.prefix') || '';
                const connectionString = configService.get('azureServiceBus.connectionString');
                if (connectionString) {
                    return {
                        connectionString,
                        prefix
                    };
                }

                // managed identity for production environments
                const namespace = configService.getOrThrow('azureServiceBus.namespace');
                return {
                    fullyQualifiedNamespace: namespace
                };
            },
            inject: [ConfigService]
        }),
        ...dynamicModules
    ],
    providers: [
        AppService,
        {
            provide: APP_INTERCEPTOR,
            useClass: RequestInterceptor
        }
    ],
    controllers
})
export class AppModule {}
