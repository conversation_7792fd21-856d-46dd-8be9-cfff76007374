import { productionOrigins as origins } from '@shared/server/environment';
import { IAnimatedCreativeServerOptions } from '../acg.types';
import { defaultOptions, getHealthReadyEndpoints } from './options';

const prodOptions: IAnimatedCreativeServerOptions = {
    ...defaultOptions,
    inProduction: true,
    serviceHostName: process.env.HOST_NAME || 'https://api.bannerflow.com/acg',
    azureStorage: {
        ...defaultOptions.azureStorage,
        accountName: process.env.AZ_STORAGE_NAME || 'bfstudio',
        origin: process.env.AZ_STORAGE_ORIGIN || 'https://bfstudio.blob.core.windows.net'
    },
    caching: {
        brand: process.env.CACHE_BRAND ? process.env.CACHE_BRAND === 'true' : true,
        creativeset: process.env.CACHE_CREATIVESET ? process.env.CACHE_CREATIVESET === 'true' : true,
        snapshot: process.env.CACHE_SNAPSHOT ? process.env.CACHE_SNAPSHOT === 'true' : true,
        widget: process.env.CACHE_WIDGET ? process.env.CACHE_WIDGET === 'true' : true
    },
    redis: {
        enabled: false,
        host: 'bf-acg-production.redis.cache.windows.net'
    },
    azure: {
        appConfigUrl: 'https://bf-shared-ac.azconfig.io',
        keyVaultUrl: 'https://bf-shared-kv.vault.azure.net',
        secrets: {
            'auth0.clientId': 'acg-auth0-client-id',
            'auth0.clientSecret': 'acg-auth0-client-secret',
            'azure.appConfigConnectionString': 'acg-appconfig-connectionstring',
            creativePreviewSecret: 'acg-creative-preview-secret',
            'unleash.auth': 'acg-unleash-api-token'
        }
    },
    azureServiceBus: {
        namespace: 'bf-common-asb.servicebus.windows.net'
    },
    origins,
    auth0: {
        ...defaultOptions.auth0,
        uri: process.env.AUTH0_DOMAIN || 'https://login.bannerflow.com'
    },
    creativePreviewNewRelic: {
        enabled: true,
        accountID: '4122654',
        agentID: '*********',
        applicationID: '*********',
        trustKey: '4122654',
        licenseKey: 'NRJS-bd28b6acdc31b77b97c'
    },
    health: {
        readyEndpoints: getHealthReadyEndpoints(origins)
    },
    tokenCache: {
        ...defaultOptions.tokenCache,
        redis: {
            ...defaultOptions.tokenCache.redis,
            enabled: true,
            host:
                process.env.CLIENT_CREDENTIALS_REDIS_HOST ||
                'bf-identity-westeurope.westeurope.redisenterprise.cache.azure.net',
            port: 10000,
            password: process.env.CLIENTCREDENTIALS__REDIS__KEY
        }
    },
    unleash: {
        ...defaultOptions.unleash,
        enabled: true
    }
};

export function getProdOptions(): IAnimatedCreativeServerOptions {
    return prodOptions;
}
