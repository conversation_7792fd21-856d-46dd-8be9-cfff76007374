// https://docs.nestjs.com/techniques/configuration

import { Logger } from '@nestjs/common';
import { IAnimatedCreativeServerOptions } from '../acg.types';
import { defaultOptions } from './options';
import { getProdOptions } from './options.prod';
import { getSandboxOptions } from './options.sandbox';

function getOptions(): IAnimatedCreativeServerOptions {
    if (process.env.STAGE === 'production') {
        Logger.log('Using production config', 'Configuration');
        return getProdOptions();
    }
    if (process.env.STAGE === 'sandbox') {
        Logger.log('Using sandbox config', 'Configuration');
        return getSandboxOptions();
    }

    Logger.log('Using dev config', 'Configuration');
    return defaultOptions;
}
export default (): IAnimatedCreativeServerOptions => {
    const options = getOptions();
    Logger.debug(options, 'Configuration');
    return options;
};
