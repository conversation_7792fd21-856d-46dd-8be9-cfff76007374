import { ServerStage } from '@domain/server';
import { Logger } from '@nestjs/common';
import { devOrigins as origins, ServerOrigins } from '@shared/server/environment';
import { HealthReadyEndpoint } from '@shared/server/health';
import { assertRuntimeEnvironmentForACG } from '@shared/server/utils';
import { IAnimatedCreativeServerOptions } from '../acg.types';
import { build } from '../environments/build-info';

assertRuntimeEnvironmentForACG();

export const defaultOptions: IAnimatedCreativeServerOptions = {
    serviceHostName: 'http://api.bannerflow.local/acg', // host name needs to include the protocol
    auth0: {
        uri: process.env.AUTH0_URI || 'https://local-login.bannerflow.com',
        audience: process.env.AUTH0_AUDIENCE || 'https://bannerflow.com/resources/',
        clientId: process.env.AUTH0_CLIENT_ID || 'LlIhF703KyGTgZizKTkVTLG4ch1MkpEV',
        clientSecret:
            process.env.AUTH0_CLIENT_SECRET ||
            'laoC8muT-BEaxNMokhBGIInNVE6UVOeOTz2JzqUvn85mCSrIPoWr1b4YHuFZivdQ'
    },
    inProduction: false,
    inSandbox: false,
    stage: process.env.STAGE as ServerStage,
    port: getPort(),
    origins,
    azureStorage: {
        accountName: 'devstoreaccount1',
        accountAccessKey:
            'Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==',
        origin: 'http://storage-emulator:10000/devstoreaccount1'
    },
    creativePreviewSecret: process.env.PREVIEW_SECRET || 'secret',
    requestTimeout: 30000,
    loadProtectionEnabled: process.env.LOAD_PROTECTION === 'true',
    build,
    caching: {
        brand: process.env.CACHE_BRAND ? process.env.CACHE_BRAND === 'true' : true,
        creativeset: process.env.CACHE_CREATIVESET ? process.env.CACHE_CREATIVESET === 'true' : true,
        snapshot: process.env.CACHE_SNAPSHOT ? process.env.CACHE_SNAPSHOT === 'true' : true,
        widget: process.env.CACHE_WIDGET ? process.env.CACHE_WIDGET === 'true' : true
    },
    redis: {
        enabled: false,
        host: 'bf-acg-sandbox.redis.cache.windows.net'
    },
    azure: {
        appConfigUrl: 'https://bf-shared-sandbox-ac.azconfig.io',
        appConfigConnectionString:
            'Endpoint=https://bf-shared-sandbox-ac.azconfig.io;Id=+d6H-l8-s0:ZDsMi4u4B3bL3Y9VAexD;Secret=kycnl5jmUghCUDu70c/LdKSinLqeEtkZE08EvTEsLK4=',
        keyVaultUrl: 'https://bf-shared-sandbox-kv.vault.azure.net'
    },
    azureServiceBus: {
        namespace: 'bf-common-local-asb.servicebus.windows.net',
        connectionString:
            'Endpoint=sb://bf-common-local-asb.servicebus.windows.net/;SharedAccessKeyName=shared-sas-policy;SharedAccessKey=NAD18sAQ9sUyhgxzN4QXHb1dt4MAE/XuZ+ASbAHBOQY=',
        prefix: 'dev-prefix'
    },
    creativePreviewNewRelic: {
        enabled: false
    },
    health: {
        readyEndpoints: getHealthReadyEndpoints(origins)
    },
    tokenCache: {
        cacheKeyPrefix: 'acg',
        redis: {
            enabled: false
        }
    },
    unleash: {
        enabled: false,
        url: 'https://bf-feature-flags.azurewebsites.net/api',
        auth: 'default:development.unleash-insecure-api-token'
    }
};

export function getHealthReadyEndpoints(_origins: ServerOrigins): HealthReadyEndpoint[] {
    return [
        {
            name: 'SAPI',
            url: `${_origins.sapi.replace(/\/api$/, '')}/health/live`
        },
        {
            name: 'AAS',
            url: `${_origins.accountAccessService}/health/live`
        },
        {
            name: 'FM',
            url: `${_origins.fontManagerApi}/health/live`
        }
    ];
}

function getPort(): number {
    const defaultPort = 80;
    try {
        if (process.env.PORT) {
            const customPort = parseInt(process.env.PORT, 10);
            if (!isNaN(customPort)) {
                return customPort;
            }
            Logger.warn(`Parsed PORT="${process.env.PORT}" was NaN. Defaulting to ${defaultPort}.`);
        }
    } catch {
        Logger.error(`Error parsing PORT="${process.env.PORT}"`);
    }

    return defaultPort;
}
