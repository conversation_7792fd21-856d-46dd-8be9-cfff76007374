import { sandboxOrigins as origins } from '@shared/server/environment';
import { IAnimatedCreativeServerOptions } from '../acg.types';
import { defaultOptions, getHealthReadyEndpoints } from './options';

const sandboxOptions: IAnimatedCreativeServerOptions = {
    ...defaultOptions,
    inSandbox: true,
    serviceHostName: process.env.HOST_NAME || 'https://sandbox-api.bannerflow.com/acg',
    azureStorage: {
        ...defaultOptions.azureStorage,
        accountName: process.env.AZ_STORAGE_NAME || 'bfstudiosandbox',
        origin: process.env.AZ_STORAGE_ORIGIN || 'https://bfstudiosandbox.blob.core.windows.net'
    },
    azure: {
        appConfigUrl: 'https://bf-shared-sandbox-ac.azconfig.io',
        keyVaultUrl: 'https://bf-shared-sandbox-kv.vault.azure.net',
        secrets: {
            'auth0.clientId': 'acg-auth0-client-id',
            'auth0.clientSecret': 'acg-auth0-client-secret',
            'azure.appConfigConnectionString': 'acg-appconfig-connectionstring',
            creativePreviewSecret: 'acg-creative-preview-secret',
            'unleash.auth': 'acg-unleash-api-token'
        }
    },
    azureServiceBus: {
        namespace: 'bf-common-sandbox-asb.servicebus.windows.net'
    },
    origins,
    auth0: {
        ...defaultOptions.auth0,
        uri: process.env.AUTH0_DOMAIN || 'https://sandbox-login.bannerflow.com'
    },
    creativePreviewNewRelic: {
        enabled: true,
        accountID: '4232543',
        agentID: '*********',
        applicationID: '*********',
        trustKey: '4122654',
        licenseKey: 'NRJS-d0b27a9b958bc4b281c'
    },
    health: {
        readyEndpoints: getHealthReadyEndpoints(origins)
    },
    redis: {
        enabled: false,
        host: 'bf-acg-sandbox.redis.cache.windows.net'
    },
    tokenCache: {
        ...defaultOptions.tokenCache,
        redis: {
            ...defaultOptions.tokenCache.redis,
            enabled: true,
            host:
                process.env.CLIENT_CREDENTIALS_REDIS_HOST ||
                'bf-identity-sandbox.redis.cache.windows.net',
            port: 6380,
            password: process.env.CLIENTCREDENTIALS__REDIS__KEY
        }
    },
    unleash: {
        ...defaultOptions.unleash,
        enabled: true
    }
};

export function getSandboxOptions(): IAnimatedCreativeServerOptions {
    return sandboxOptions;
}
