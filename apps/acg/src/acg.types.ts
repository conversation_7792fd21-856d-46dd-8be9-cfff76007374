import { NewRelicConfig } from '@domain/acg/monitoring';
import { IBuildInfo } from '@domain/environment';
import {
    IAuth0ClientOptions,
    IAzureBlobServiceOptions,
    IAzureOptions,
    IAzureServiceBusOptions,
    IServerOptions,
    ITokenCacheOptions,
    ServerStage
} from '@domain/server';
import { ServerOrigins } from "@shared/server/environment/origins.types'";
import { HealthReadyEndpoint } from '@shared/server/health';
import { FeatureFlagConfig } from '@shared/server/utils';
import { MetadataConfig } from '@shared/server/metadata';

export interface IAnimatedCreativeServerOptions
    extends IServerOptions,
        FeatureFlagConfig,
        MetadataConfig {
    origins: ServerOrigins;
    stage: ServerStage;
    creativePreviewSecret: string;
    requestTimeout: number;
    loadProtectionEnabled: boolean;
    build: IBuildInfo;
    azureStorage: IAzureBlobServiceOptions;
    azure: IAzureOptions;
    azureServiceBus: IAzureServiceBusOptions;
    auth0: IAuth0ClientOptions;
    caching: {
        brand: boolean;
        creativeset: boolean;
        snapshot: boolean;
        widget: boolean;
    };
    redis: {
        enabled: boolean;
        host: string;
    };
    serviceHostName: string;
    creativePreviewNewRelic: NewRelicConfig;
    health: {
        readyEndpoints: HealthReadyEndpoint[];
    };
    tokenCache: ITokenCacheOptions;
}
