// NOTE: OpenTelemetry has to be the first import
import '@shared/server/monitoring/instrumentation';
// rest of app imports
console.time('main.ts imports');
import { Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { tinyFormat } from '@shared/server/monitoring';
import compression from 'compression';
import modRewrite from 'connect-modrewrite';
import { json } from 'express';
import morgan from 'morgan';
import { utilities as nestWinstonModuleUtilities, WinstonModule } from 'nest-winston';
import { createLogger, format, transports } from 'winston';
import { IAnimatedCreativeServerOptions } from './acg.types';
import { AppModule } from './app.module';
import { AppService } from './core/services/app.service';
console.timeEnd('main.ts imports');

async function bootstrap(): Promise<void> {
    const loggerInstance = createLogger({
        level: process.env.LOG_LEVEL ?? 'info',
        transports: [
            new transports.Console({
                format: format.combine(
                    format.timestamp(),
                    format.ms(),
                    nestWinstonModuleUtilities.format.nestLike('ACG', {
                        colors: process.env.STAGE === 'local',
                        prettyPrint: true
                    })
                )
            })
        ]
    });
    const app = await NestFactory.create<NestExpressApplication>(AppModule, {
        logger: WinstonModule.createLogger({
            instance: loggerInstance
        })
    });

    app.enableCors();
    // Use the X-Forwarded-* header's IP-address
    // @link https://expressjs.com/en/guide/behind-proxies.html
    app.set('trust proxy', 1);

    const appService = app.get<AppService>(AppService);
    const configService = app.get<ConfigService<IAnimatedCreativeServerOptions, true>>(ConfigService);
    const port = configService.get<number>('port')!;

    const cdnOrigin = configService.get('origins.cdn', { infer: true });
    applyDefaultMiddlewares(app, cdnOrigin);
    await appService.start();

    await app.listen(port);
    Logger.log(`ACG is running on: ${await app.getUrl()}`, 'Bootstrap');
}

bootstrap().catch(e => {
    Logger.error(`Failed to bootstrap, due to ${e}`, 'Bootstrap');
    process.exit(1);
});

function applyDefaultMiddlewares(app: NestExpressApplication, cdnOrigin: string): void {
    app.use(json({ limit: '50mb' }));
    app.use(
        morgan(tinyFormat, {
            stream: {
                write: (text: string) => Logger.log(text, 'HTTP')
            },
            skip: function (req) {
                if (process.env.STAGE !== 'local') {
                    return true;
                }
                return req.url.includes('health');
            }
        })
    );
    app.use(compression());
    applyCDNRedirect(app, cdnOrigin);

    // respond correctly to UnauthorizedError
    app.use(function (err, _req, _res, next): void {
        if (err.name === 'UnauthorizedError') {
            throw new UnauthorizedException(err.message);
        } else {
            next(err);
        }
    });
}

function applyCDNRedirect(app: NestExpressApplication, cdnOrigin: string): void {
    app.use(modRewrite([`^/scripts/(.*)$ ${cdnOrigin}/scripts/$1 [P]`]));
}
