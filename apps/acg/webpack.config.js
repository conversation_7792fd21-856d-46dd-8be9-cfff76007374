const { composePlugins, withNx } = require('@nx/webpack');
const path = require('node:path');

// Nx plugins for webpack.
module.exports = composePlugins(withNx(), config => {
    // fixes source maps https://github.com/nrwl/nx/issues/14708#issuecomment-1858838470
    config.output.devtoolModuleFilenameTemplate = function (info) {
        const rel = path.relative(process.cwd(), info.absoluteResourcePath);
        return `webpack:///./${rel}`;
    };
    config.devtool = 'source-map';

    return config;
});
