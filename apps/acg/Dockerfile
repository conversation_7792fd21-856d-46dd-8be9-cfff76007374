# This file is generated by Nx.
#
# Build the docker image with `pnpm exec nx docker-build acg`.
# Tip: Modify "docker-build" options in project.json to change docker build args.
#
# Run the container with `docker run -p 3000:3000 -t acg`.
FROM docker.io/node:lts-alpine

WORKDIR /app

RUN addgroup -g 1001 appuser && \
    adduser -D -u 1001 -G appuser appuser

RUN chown -R appuser:appuser /app

COPY dist/apps/acg acg
# copy ad & creative scripts which ACG depends on at runtime
COPY dist/libs/ad libs/ad
COPY dist/libs/creative libs/creative

# Set npm authentication for private registry
COPY .npmrc acg

# You can remove this install step if you build with `--bundle` option.
# The bundled output will include external dependencies.
RUN npm --prefix acg --omit=dev -f install --ignore-scripts

# Switch to the appuser
EXPOSE 8080
USER appuser:appuser

CMD ["node", "--max-old-space-size=1536", "--enable-source-maps", "acg"]