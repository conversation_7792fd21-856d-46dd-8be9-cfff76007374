param serviceName string
param location string = resourceGroup().location
param namespace string
param aksClusterName string
param aksClusterResourceGroup string
param environment string = 'production'

param managedIdentityName string = '${aksClusterName}-${environment == 'sandbox' ? 'sandbox-' : ''}${serviceName}-mi'
param managedFederatedName string = '${aksClusterName}-${environment == 'sandbox' ? 'sandbox-' : ''}${serviceName}-fi'

resource aksCluster 'Microsoft.ContainerService/managedClusters@2023-02-01' existing = {
  name: aksClusterName
  scope: resourceGroup(aksClusterResourceGroup)
}

resource managedIdentity 'Microsoft.ManagedIdentity/userAssignedIdentities@2018-11-30' = {
  name: managedIdentityName
  location: location
}

resource federatedIdentityCredential 'Microsoft.ManagedIdentity/userAssignedIdentities/federatedIdentityCredentials@2022-01-31-preview' = {
  name: managedFederatedName
  parent: managedIdentity
  properties: {
    issuer: aksCluster.properties.oidcIssuerProfile.issuerURL
    subject: 'system:serviceaccount:${namespace}:${serviceName}'
    audiences: [ 'api://AzureADTokenExchange' ]
  }
}

output managedIdentityClientId string = managedIdentity.properties.clientId