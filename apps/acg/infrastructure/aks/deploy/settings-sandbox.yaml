env:
    ENVIRONMENT: Production
    STAGE: sandbox
    LOG_LEVEL: debug
    PORT: 8080
    OTEL_EXPORTER_OTLP_ENDPOINT: 'https://otlp.eu01.nr-data.net'
    OTEL_SERVICE_NAME: 'ACG'
    OTEL_ATTRIBUTE_VALUE_LENGTH_LIMIT: 4095
    OTEL_EXPORTER_OTLP_COMPRESSION: gzip
    OTEL_EXPORTER_OTLP_PROTOCOL: http/protobuf
    OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE: delta

ingressnginx:
    enabled: true
    host: sandbox-api.bannerflow.com
    paths:
        - path: /acg/(.*)
          pathType: ImplementationSpecific

networkpolicy:
    enabled: true
    policyname: allow-incoming
    cidr: *********/19 #Always static cidr, belongs to Azure app gateway subnet
    nginxcidr: *********/19 #Always static cidr, belongs to Azure vnet backend subnet
    accessvianamespaces: true
    namespaces:
        - creative-generation #Allow CMM to access <PERSON><PERSON> within the cluster
        - studio #Allow SAPI to access ACG within the cluster
        - publishing #Allow PS to access ACG within the cluster

serviceAccount:
    create: true
    disableAutomountTokens: 'true'

service:
    type: ClusterIP
    port: 8080

labels:
    enabled: true
    label:
        azure.workload.identity/use: 'true'

autoscaling:
    enabled: true
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
    minReplicas: 2
    maxReplicas: 3

nodeSelector:
    scaling: dynamic

securityContext:
    runAsUser: 1001
    runAsGroup: 1001
    allowPrivilegeEscalation: false
    privileged: false

resources:
    requests:
        cpu: 200m
        memory: 200Mi
    limits:
        cpu: 500m
        memory: 600Mi
